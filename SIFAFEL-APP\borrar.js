﻿@{ @model SIFAFEL_MODEL.DataTransferObject.ModCompraVentaDTO
    Layout = "~/Views/Shared/_Layout_io.cshtml";
}
<style>
    .table .btn-sm {
        line-height: 1 !important;
    }
</style>

@Html.AntiForgeryToken()

@* REALIZAR COMPRAS *@
<section>
    <div class="row">
        @* SECCION DE PROVEEDOR *@
        <div class="col-md-7">
            <div class="iq-card">
                <div class="iq-card-header d-flex justify-content-between">
                    <div class="iq-header-title">
                        <h4 class="card-title"><i class="far fa-user"></i>&nbsp;Info. proveedor</h4>
                    </div>
                </div>
                <div class="iq-card-body">
                    <div class="form-horizontal">
                        @* CODIGO PROVEEDOR *@
                        <div class="form-group hide-control">
                            <div class="col-md-12">
                                <input class="form-control input-sm text-box single-line" data-val="true" id="idProveedor" name="idProveedor" type="text">
                            </div>
                        </div>

                        @* NIT PROVEEDOR *@
                        <div class="form-group row">

                            <label class="control-label col-sm-2 align-self-center mb-0">NIT:</label>

                            <div class="col-sm-10">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-id-card" aria-hidden="true"></i>
                                    </span>
                                    <input class="form-control input-sm text-box single-line" data-val="true" id="nitProveedor" name="nitProveedor" type="text" placeholder="Nit del proveedor">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" id="btnBuscarProveedor" type="button" title="Búscar Nit del proveedor">
                                            <span class="fa fa-search"></span>
                                        </button>
                                        <button class="btn btn-primary" id="btnBuscarProveedores" type="button" onclick="ListaProveedores()" title="Búscar proveedor por nombre">
                                            <span class="fa fa-users"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @* NOMBRE DEL PROVEEDOR *@
                        <div class="form-group row">
                            <label class="control-label col-sm-2 align-self-center mb-0">Nombre:</label>
                            <div class="col-sm-10">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-user" aria-hidden="true"></i>
                                    </span>
                                    <input class="form-control input-sm text-box single-line" data-val="true" id="nombreProveedor" name="nombreProveedor" type="text" placeholder="Nombre del proveedor" disabled>
                                </div>
                            </div>
                        </div>

                        @* DIRECCION *@
                        <div class="form-group row">
                            <label class="control-label col-sm-2 align-self-center mb-0">Direccion:</label>
                            <div class="col-sm-10">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-map-marker" aria-hidden="true"></i>
                                    </span>
                                    <input class="form-control input-sm text-box single-line" data-val="true" id="direccionProveedor" name="direccionProveedor" type="text" placeholder="Dirección del la empresa" disabled>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>

        @*SECCION DE COMPRA *@
        <div class="col-md-5">
            <div class="iq-card">
                <div class="iq-card-header d-flex justify-content-between">
                    <div class="iq-header-title">
                        <h4 class="card-title"><i class="far fa-file-invoice"></i>&nbsp;Compra</h4>
                    </div>
                </div>
                <div class="iq-card-body">
                    <div class="form-horizontal">
                        @* CODIGO COMPRA *@
                        <div class="form-group row">
                            <label class="control-label col-sm-5 align-self-center mb-0">Código de compra:</label>
                            <div class="col-sm-7">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-file-invoice" aria-hidden="true"></i>
                                    </span>
                                    <input class="form-control input-sm text-box single-line" data-val="true" id="codigoCompra" name="codigoCompra" type="text" placeholder="No. Compra">
                                </div>
                            </div>
                        </div>

                        @* FECHA COMPRA *@
                        <div class="form-group row">
                            <label class="control-label col-sm-5 align-self-center mb-0">Fecha Compra:</label>
                            <div class="col-sm-7">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-calendar" aria-hidden="true"></i>
                                    </span>
                                    <input class="form-control input-sm text-box single-line" data-val="true" id="fechaCompra" name="fechaCompra" type="date">
                                </div>
                            </div>
                        </div>

                        @*FORMA DE PAGO*@
                        <div class="form-group row">
                            <label class="control-label col-sm-5 align-self-center mb-0">Forma de pago:</label>
                            <div class="col-sm-7">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-addon">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill-wave" aria-hidden="true"></i>
                                    </span>
                                    @*@Html.DropDownListFor(model => model.ConfMedioPago.codigo, new SelectList(ViewBag.MedioPago, "codigo", "descripcion"), htmlAttributes: new { @class = "form-control form-select", @id = "ddlFormaPago", @name = "ddlFormaPago" })*@
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    @* PRODUCTOS *@
    <div class="row">
        <div class="col-md-12">
            <div class="iq-card">
                <div class="iq-card-body">
                    <div class="row">

                        <div class="col-lg-12">

                            @* CODIGO PRODUCTO *@
                            <div class="col-lg-4 col-sm-12">

                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-addon">
                                                @*<img src="@System.Configuration.ConfigurationManager.AppSettings["url_cdn"]sifafel/img/modules/inventory/barcode-scan.gif" height="23" />*@
                                                <i class="far fa-barcode"></i>
                                            </span>
                                            <input class="form-control input-sm text-box single-line" data-val="true" id="idProducto" name="idProducto" type="text" placeholder="Codigo de producto">
                                            <div class="input-group-append">
                                                <button class="btn btn-primary" id="btnBuscarProd" type="button">
                                                    <span class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-search" title="Búscar producto por codigo"></span>
                                                </button>
                                                <button class="btn btn-sm btn-primary" onclick="_ListaProductos()" type="button" role="button" title="Búscar producto por nombre">
                                                    <span class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-boxes"></span>&nbsp;productos
                                                </button>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="row"></div>

                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="dtProductoC" class="table table-bordered table-hover table-striped table-condensed">
                                    <thead class="table-head">
                                        <tr>
                                            <th>Id Producto</th>
                                            <th>Código</th>
                                            <th>Producto</th>
                                            <th>Cantidad</th>
                                            <th>Existencia</th>
                                            <th>Costo U.</th>
                                            <th width="200">Costo Total</th>
                                            <th>Precio Venta</th>
                                            <th>Precio minimo</th>
                                            <th>Descripción</th>
                                            <th>Estado</th>
                                            <th>&nbsp;</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-5 offset-md-7 col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group row">
                                    <label class="control-label col-sm-3 align-self-center mb-0">Sub Total:</label>
                                    <div class="input-group input-group-sm col-md-9">
                                        <span class="input-group-addon">
                                            <i id="subTotal" class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill-alt" aria-hidden="true"></i>
                                        </span>
                                        <input class="form-control input-xs text-box single-line price" data-val="true" id="txtSubTotal" name="txtSubTotal" placeholder="0.00" disabled>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="control-label col-sm-3 align-self-center mb-0">Total:</label>
                                    <div class="input-group input-group-sm col-md-9">
                                        <span class="input-group-addon">
                                            <i id="total" class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill-alt" aria-hidden="true"></i>
                                        </span>
                                        <input class="form-control input-xs text-box single-line price" data-val="true" id="txtTotal" name="txtTotal" placeholder="0.00" disabled>
                                    </div>
                                </div>
                            </div>
                            <div class="row"></div>
                            <br />
                            <button id="btnGuardarCompra" onclick="GuardarCompra()" type="button" role="button" class="btn btn-primary" title="Registrar compra"><i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-save"></i>&nbsp;Registrar</button>
                            <button id="btnCancelar" type="button" role="button" class="btn iq-bg-danger" title="Cancelar la compra"><i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-ban"></i>&nbsp;Cancelar</button>

                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@* MODAL PARA REGISTRO DE PROVEEDOR *@
<section>
    <div class="modal fade modal-crud" id="mdlNuevoProveedor">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="title">Crear Proveedor</h4>
                    <a href="#" class="close" data-dismiss="modal">&times;</a>
                </div>
                <div class="modal-body">
                    <div class="form-content">
                        @{
                            ViewBag.CrearRegistro = true;
                        }

                        @Html.Partial("Proveedor/_CrearEditar")
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" role="button" class="btn btn-success btn-sm save-changes"><i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-save"></i>&nbsp;Guardar</button>
                </div>
            </div>
        </div>
    </div>
</section>

@* MODAL PARA LA LISTA DE PROVEEDORES *@
<section>
    <div class="modal fade modal-crud" id="mdlListaProveedores">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="title">Lista de Proveedores</h4>
                    <a href="#" class="close" data-dismiss="modal">&times;</a>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="dtLstProveedor" class="table table-bordered table-hover table-striped table-condensed">
                            <thead class="table-head">
                                <tr>
                                    <th>Código</th>
                                    <th>NIT</th>
                                    <th>Nombres</th>
                                    <th>Apellidos</th>
                                    <th>Teléfono</th>
                                    <th>Correo</th>
                                    <th>Direccion</th>
                                    <th>&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>
</section>

@* MODAL PARA MOSTRAR LOS PRODUCTOS DISPONIBLES *@
<section>
    <div class="modal fade modal-crud" id="mdlListaProductos">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="title">Lista Productos</h4>
                    <a href="#" class="close" data-dismiss="modal">&times;</a>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table id="dtLstProducto" class="table table-bordered table-hover table-striped table-condensed">
                            <thead class="table-head">
                                <tr>
                                    <th>Código</th>
                                    <th>Marca</th>
                                    <th>Modelo</th>
                                    <th>Año</th>
                                    <th>Producto</th>
                                    <th>Cantidad</th>
                                    <th>Precio U.</th>
                                    <th>Descripción</th>
                                    <th>&nbsp;</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">

                </div>
            </div>
        </div>
    </div>
</section>

<section>
    @Html.Partial("Reporte/_ReportViewer")
</section>

<script type="text/javascript" src="@System.Configuration.ConfigurationManager.AppSettings["url_cdn"]vendor/sum/sum.js"></script>
<script type="text/javascript" src="@System.Configuration.ConfigurationManager.AppSettings["url_cdn"]SIFAFEL/js/compra-venta.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        initTableProducto();
        ClearAllFields();

        $(".save-changes").on('click', function () {
            var _data = {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                nit: $("#nit").val(),
                nombres: $("#nombres").val(),
                apellidos: $("#apellidos").val(),
                telefono: $("#telefono").val(),
                email: $("#email").val(),
                direccion: $("#direccion").val()
            };

            NuevoProveedor(_data);
        });

        setTodayDateFormat("#fechaCompra");
        /////BUSCAR PROVEEDOR
        $("#btnBuscarProveedor").on("click", function () {
            BuscarProveedor($("#nitProveedor").val());
        });

        $("#nitProveedor").on('keypress', function (e) {
            if (e.which == 13) {
                if ($(this).val().trim() != "") {
                    BuscarProveedor($("#nitProveedor").val());
                }
            }
        });
        /////////////////////////////////

        ////////BUSCAR PRODUCTO////////////////////
        $("#btnBuscarProd").on("click", function () {
            var dataReult = BuscarProducto($("#idProducto").val());
            if (dataReult != null || dataReult != undefined) {
                AddProductoTable(dataReult);
            }
        });

        $("#idProducto").on('keypress', function (e) {
            if (e.which == 13) {
                if ($(this).val().trim() != "") {
                    var dataReult = BuscarProducto($("#idProducto").val());
                    if (dataReult != null || dataReult != undefined) {
                        $("#idProducto").val("");
                        AddProductoTable(dataReult);
                    }
                }
            }
        });
        /////////////////////////////////////////////////////////

        $("#btnCancelar").on('click', function () {
            Swal.fire({
                title: 'Cancelar venta',
                text: "¿Desea Cancelar la venta?",
                icon: 'question',
                showDenyButton: true,
                showCancelButton: false,
                confirmButtonText: 'Si',
                denyButtonText: 'No'
            }).then((result) => {
                if (result.isConfirmed) {
                    ClearAllFields();
                }
            });
        });
    });

    function initTableProducto() {
        //tabla que se muestra para la venta de producto
        $("#dtProductoC").DataTable({
            language: {
                url: _url_datatable_es
            },
            searching: false,
            ordering: false,
            info: false,
            paging: false,
            columnDefs: [
                {
                    targets: [1, 3, 4],
                    width: '8%',
                    className: 'text-center'
                },
                {
                    targets: [5, 6, 7, 8],
                    width: '10%',
                    className: 'text-center'
                },
                {
                    targets: 2,
                    width: '25%'
                },
                {
                    targets: [0, 9, 10],
                    visible: false
                },
                {
                    targets: 11,
                    className: 'text-right',
                    width: '3%'
                },
            ],
            columns: [
                { data: "id_producto" },
                { data: "codigo" },
                { data: "nombre" },
                {
                    data: "cantidad",
                    render: function (data, type, row, meta) {
                        return '<input id="txtCount_' + meta.row + '" class="form-control input-xs text-box single-line stock" data-val="true" type="number" min="1" onkeyup="CalcularTotal(this,' + meta.row + ')" onchange="CalcularTotal(this,' + meta.row + ')" onblur="validarCantidadCosto(this,' + meta.row + ')" onkeypress="onKeyPressOnlyNumber()">';
                    }
                },
                { data: "existencia" },
                {
                    data: "costo_unitario",
                    render: function (data, type, row, meta) {
                        return '<input id="txtCostoUnit_' + meta.row + '" class="form-control input-xs text-box single-line price" data-val="true" type="text" onblur="validarCantidadCosto(this,' + meta.row + ')" onkeypress="onKeyPressOnlyNumber(true)">';
                    }
                },
                {
                    data: "costo_total",
                    render: function (data, type, row, meta) {
                        return MoneyFormat("Q", data);
                    }
                },
                {
                    data: "precio_venta",
                    render: function (data, type, row, meta) {
                        return '<input id="txtPrecioUnit_' + meta.row + '" class="form-control input-xs text-box single-line price" data-val="true" type="text" onblur="validarCantidadCosto(this,' + meta.row + ')" onkeypress="onKeyPressOnlyNumber(true)">';
                    }
                },
                {
                    data: "precio_minimo",
                    render: function (data, type, row, meta) {
                        return '<input id="txtPrecioMin_' + meta.row + '" class="form-control input-xs text-box single-line price" data-val="true" type="text" onblur="validarCantidadCosto(this,' + meta.row + ')" onkeypress="onKeyPressOnlyNumber(true)">';
                    }
                },
                { data: "descripcion" },
                { data: "estado" },
                {
                    data: function (item) {
                        return '<button id="btnEliminar" class="btn btn-danger btn-sm" type="button" role="button" title="Eliminar"><span class="fa fa-trash"></span></button>';
                    }
                }
            ],
            createdRow: function (row, data, dataIndex) {
                $(row).find("#btnEliminar").on("click", function () {
                    $("#dtProductoC").DataTable().row($(this).parents('tr')).remove().draw();
                    updateTotalPay("dtProductoC", "Q");
                });
            }
        });

        //tabla que contiene la lista de productos
        $("#dtLstProducto").DataTable({
            language: {
                url: _url_datatable_es
            },
            //searching: false,
            //ordering: false,
            info: false,
            paging: true,
            autoWidth: false,
            columnDefs: [
                {
                    targets: [1, 2, 3, 5, 8],
                    className: 'text-center'
                },
                {
                    targets: [0, 6],
                    className: 'text-right'
                },
                {
                    targets: [7],
                    visible: false
                },
                {
                    targets: [6],
                    //width: "200px"
                }
            ],
            columns: [
                { data: "codigo" },
                { data: "marca" },
                { data: "modelo" },
                { data: "anio" },
                { data: "nombre" },
                { data: "stock_actual" },
                {
                    data: function (item) {
                        return '<span class="currency">' + item.moneda + '</span>' +
                            '<span class="price">' + __formatMoney(item.precio_unitario, 2, '.', ',') + '</span>';
                    }
                },
                { data: "descripcion" },
                {
                    data: function (item) {
                        return '<button class="btn btn-success btn-sm add-product" type="button" role="button" style="cursor: pointer;" title="Agregar producto"><span class="fa fa-plus"></span></button>';
                    }
                },
            ],
            createdRow: function (row, data, dataIndex) {
                $(row).find(".add-product").on("click", function () {
                    let $btnAddProduct = $(this);
                    $btnAddProduct.html('<span class="fas fa-spinner fa-spin"></span>');
                    AddProductoTable(data);
                    setTimeout(function () {
                        $btnAddProduct.html('<span class="fa fa-plus"></span>');
                        $("#mdlListaProductos").modal("hide");
                    }, 200);
                });
            }
        });

        //tabla que contiene la lista de proveedores
        $("#dtLstProveedor").DataTable({
            language: {
                url: _url_datatable_es
            },
            info: false,
            paging: true,
            autoWidth: false,
            columnDefs: [
                {
                    targets: [0],
                    visible: false
                }
            ],
            columns: [
                { data: "id_cliente" },
                { data: "nit" },
                { data: "nombres" },
                { data: "apellidos" },
                { data: "telefono" },
                { data: "email" },
                { data: "direccion" },
                {
                    data: function (item) {
                        return '<button class="btn btn-success btn-sm add-proveedor" type="button" role="button" style="cursor: pointer;" title="Agregar proveedor"><span class="fa fa-plus"></span></button>';
                    }
                },
            ],
            createdRow: function (row, data, dataIndex) {
                $(row).find(".add-proveedor").on("click", function () {
                    let $btnAddProduct = $(this);
                    $btnAddProduct.html('<span class="fas fa-spinner fa-spin"></span>');
                    ClearFieldProveedor();
                    SetDataProveedor(data);

                    setTimeout(function () {
                        $btnAddProduct.html('<span class="fa fa-plus"></span>');
                        $("#mdlListaProveedores").modal("hide");
                    }, 200);
                });
            }
        });
    }

    function BuscarProveedor(nit) {
        ClearFieldProveedor();

        if (nit != "") {
            nit = nit.toUpperCase();

            if (nit != "C/F" && nit != "CF") {
                $.ajax({
                    url: "@System.Configuration.ConfigurationManager.AppSettings["url_redirect"]Proveedor/Buscar_Proveedor" + "?nit=" + nit,
                    data: null,
                    type: "GET",
                    contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    dataType: "json",
                    beforeSend: function () {
                        Holdon_Open("Buscando...");
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            SetDataProveedor(result.objectDTO);
                        }
                        else if (result.type == "warning") {
                            Swal.fire({
                                title: result.title,
                                text: result.text,
                                icon: 'question',
                                showDenyButton: true,
                                showCancelButton: false,
                                confirmButtonText: 'Si',
                                denyButtonText: 'No'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    $("#nit").val(nit);
                                    $("#mdlNuevoProveedor").modal("show");
                                    Holdon_Close();
                                }
                            });
                        }
                        else {
                            Swal.fire({ icon: result.type, text: result.text });
                        }
                    },
                    error: function (result) {
                        console.log('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrio un error al iniciar el proceso de búsqueda.',
                        });
                    },
                    complete: function () {
                        Holdon_Close();
                    }
                });

            } else {
                //Swal.fire({ icon: "info", text: "Consumidor final" });
                SetDataProveedor({
                    id_proveedor: "",
                    nit: "C/F",
                    nombres: 'CONSUMIDOR',
                    apellidos: 'FINAL',
                    direccion: 'Ciudad'
                },true);
            }
        }
        else {
            Swal.fire({ icon: "warning", text: "Por favor ingrese el número de NIT del proveedor" });
        }
    }

    function ClearFieldProveedor()
    {
        $("#idProveedor").val("");
        $("#nitProveedor").val("");
        $("#nombreProveedor").val("");
        $("#direccionProveedor").val("");

        $("#nit").val("");
        $("#nombres").val("");
        $("#apellidos").val("");
        $("#telefono").val("");
        $("#email").val("");
        $("#direccion").val("");
    }

    function ClearAllFields()
    {
        ClearFieldProveedor()

        $("#idProducto").val("");
        $("#codigoCompra").val("");

        $("#txtSubTotal").val("");
        $("#txtTotal").val("");

        $('#dtProductoC').dataTable().fnClearTable();
    }

    function SetDataProveedor(data, enableFields)
    {
        $("#idProveedor").val(data.id_proveedor);
        $("#nitProveedor").val(data.nit);
        $("#nombreProveedor").val(data.nombres + ((data.apellidos == null || data.apellidos == '') ? '' : ' ' + data.apellidos));
        $("#direccionProveedor").val(data.direccion);

        $("#nombreProveedor").attr("disabled", !enableFields);
        $("#direccionProveedor").attr("disabled", !enableFields);
    }

    function NuevoProveedor(pData) {
        $.ajax({
            url: "@System.Configuration.ConfigurationManager.AppSettings["url_redirect"]Proveedor/Crear",
            data: pData,
            type: "POST",
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            dataType: "json",
            async: false,
            beforeSend: function () {
                Holdon_Open("Guardando...");
            },
            success: function (result) {
                if (result.type == "success") {
                    ClearFieldProveedor();
                    SetDataProveedor(result.objectDTO);
                    $("#mdlNuevoProveedor").modal("hide");
                }
                else {
                    Swal.fire({ icon: result.type, text: result.text });
                }
            },
            error: function (result) {
                Swal.fire({
                    icon: 'error',
                    text: 'Ocurrio un error al iniciar el proceso de guardar el registro.',
                });
            },
            complete: function () {
                Holdon_Close();
            }
        });
    }

    //funcion que agrega los productos a la tabla para la venta
    function AddProductoTable(dataResult) {
        var table = $('#dtProductoC').DataTable();
        var selectRow = table.column(0).data().indexOf(dataResult.id_producto);

        //si no existe que agregue un nuevo registro de lo contrario solo actualiza los datos
        if (selectRow === -1) { //validar que no se ingresen los mismos datos
            var addedRow = table.row.add({
                "id_producto": dataResult.id_producto,
                "codigo": dataResult.codigo,
                "nombre": dataResult.nombre,
                "cantidad": 1,
                "existencia": dataResult.stock_actual,
                "precio_venta": dataResult.precio_unitario,
                "precio_minimo": dataResult.min_descuento,
                "costo_unitario": dataResult.costo_unitario,
                "costo_total": dataResult.costo_unitario,
                "descripcion": dataResult.descripcion,
                "estado": dataResult.estado
            }).draw();

            var index = addedRow.index();
            $('#txtCount_' + index).val(1);
            $('#txtPrecioUnit_' + index).val(dataResult.precio_unitario);
            $('#txtPrecioMin_' + index).val(dataResult.min_descuento);
            $('#txtCostoUnit_' + index).val(dataResult.costo_unitario);
        }
        else {
            if (selectRow != undefined) {
                var count = parseInt($('#txtCount_' + selectRow).val()) + 1;
                var costo_total = parseFloat(dataResult.costo_unitario).toFixed(2) * count;

                table.cell(selectRow, 3).data(count).draw();
                table.cell(selectRow, 6).data(costo_total).draw();
                $('#txtCount_' + selectRow).val(count);
            }
        }

        var total = parseFloat(table.column(6).data().sum());
        updateTotalPagar(total, 0, dataResult.moneda);
    }

    //event onkeyup funcion que realiza el calculo para las cantidades que se ingrese en cada producto de la tabla
    function CalcularTotal(inputElement, index) {
        var table = $('#dtProductoC').DataTable();
        var count = parseInt($('#txtCount_' + index).val());
        var costo_unitario = parseFloat($('#txtCostoUnit_' + index).val());

        if (!isNaN(count) && count.length !== 0) {
            var costo_total = count * costo_unitario;
            table.cell(index, 3).data(count).draw();

            table.cell(index, 6).data(costo_total).draw();
            $('#txtCount_' + index).val(count);

            var total = parseFloat(table.column(6).data().sum());
            updateTotalPagar(total, 0, "Q");
        }

        inputElement.focus();//establecer el foco en el input
    }

    function GuardarDatosProveedorCF()
    {
        if ($("#idProveedor").val().length == "" && $("#nitProveedor").val() == "C/F") {
            var _data = {
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val(),
                nit: $("#nitProveedor").val(),
                nombres: $("#nombreProveedor").val(),
                direccion: $("#direccionProveedor").val()
            };

            NuevoProveedor(_data);
        }
    }

    //funcion para guardar la compra realizada
    function GuardarCompra() {
        GuardarDatosProveedorCF();

        var dataJson = {
            EncVenta: {
                id_referencia: $("#codigoCompra").val(),
                fecha_venta: $("#fechaCompra").val(),
                forma_pago: $("#ddlFormaPago").val(),
                sub_total: $("#txtSubTotal").val(),
                total_pagar: $("#txtTotal").val(),
                moneda: $("#total").html(),

                id_proveedor: $("#idProveedor").val(),
                nombre: $("#nombreProveedor").val(),
                direccion: $("#direccionProveedor").val(),
                nit: $("#nitProveedor").val(),
                tipo: "E"
            },
            DetVenta: {
                Productos: getDTProductos("dtProductoC")
            }
        }
        if ($("#idProveedor").val() == "" || $("#idProveedor").val() == undefined) {

            Swal.fire({ icon: "warning", text: "Debe ingresar un proveedor para guardar la compra" });
        }
        if ($("#codigoCompra").val() == "" || $("#codigoCompra").val() == undefined) {

            Swal.fire({ icon: "warning", text: "Debe ingresar un numero de compra" });
        }
        else {
            if ($("#dtProductoC").DataTable().data().count() > 0) {
                $.ajax({
                    url: "@System.Configuration.ConfigurationManager.AppSettings["url_redirect"]Compras/GuardarCompra",
                    data: dataJson,
                    type: "POST",
                    contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    dataType: "json",
                    beforeSend: function () {
                        Holdon_Open("Procesando...");
                    },
                    success: function (response, arg, xhr) {
                        if (response.type == "success") {
                            ClearAllFields();

                            Swal.fire({
                                icon: response.type,
                                text: response.text
                            });
                        }
                        else {
                            Swal.fire({
                                icon: response.type,
                                text: response.text
                            });
                        }
                    },
                    error: function (result) {
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrio un error al iniciar el proceso de guardar la compra.',
                        });
                    },
                    complete: function () {
                        Holdon_Close();
                    }
                });
            } else {
                Swal.fire({ icon: "warning", text: "Debe ingresar por lo menos un producto para guardar la compra." });
            }
        }
    }

    function validarCantidadCosto(inputElement, index) {
        var table = $('#dtProductoC').DataTable();
        var count = parseInt($('#txtCount_' + index).val());
        var costo = parseFloat($('#txtCostoUnit_' + index).val());
        var costo_unitario = parseFloat($('#txtCostoUnit_' + index).val()); //Obtener costo_unitario
        var precio_unitario = parseFloat($('#txtPrecioUnit_' + index).val()); //Obtener precio_unitario
        var precio_minimo = parseFloat($('#txtPrecioMin_' + index).val()); //Obtener precio_unitario

        if (precio_unitario < costo_unitario) {
            Swal.fire({ icon: "warning", text: "El precio es menor que el costo unitario, se le agrego automaticamente el valor del precio unitario." });
            precio_unitario = costo_unitario;
        }
        if (precio_minimo < costo_unitario) {
            Swal.fire({ icon: "warning", text: "El precio minimo es menor que el costo unitario, se le agrego automaticamente el valor del precio minimo." });
            precio_minimo = costo_unitario;
        }

        if (isNaN(count) || count.length === 0) { //si no existe ningun valor en el campo le agrega 1
            count = 1;
        }

        if (isNaN(costo) || costo.length === 0) { //si no existe ningun valor en el campo le agrega el costo_unitairo
            costo = costo_unitario;
        }

        var costo_total = count * costo;
        table.cell(index, 3).data(count).draw();          
        table.cell(index, 5).data(costo).draw();
        table.cell(index, 6).data(costo_total).draw();
        table.cell(index, 7).data(precio_unitario).draw();
        table.cell(index, 8).data(precio_minimo).draw();
        $('#txtCount_' + index).val(count);
        $('#txtPrecioUnit_' + index).val(precio_unitario);
        $('#txtPrecioMin_' + index).val(precio_minimo)
        $('#txtCostoUnit_' + index).val(costo);

        var total = parseFloat(table.column(6).data().sum());
        updateTotalPagar(total, 0, "Q");
    }
</script>