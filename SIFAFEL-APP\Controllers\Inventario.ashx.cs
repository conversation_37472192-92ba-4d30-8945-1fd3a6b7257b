using Newtonsoft.Json;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.api_response;
using SIFAFEL_CORE.web_request;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Modulos;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.DataTransferObject;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Descripción breve de Inventario
    /// </summary>
    public class Inventario : SessionHandler
    {

        public InfoUsuario info_usuario = new InfoUsuario();

        public override void HandleRequest(HttpContext context)
        {
            info_usuario = context.Session["AuthInfoUser"] as InfoUsuario;
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "get/products":
                    context.Response.Write(JsonConvert.SerializeObject(GetProducts()));
                    break;
                case "get/existencia_sucursal":
                    string idExistencia = context.Request["id"];
                    context.Response.Write(JsonConvert.SerializeObject(ExistenciaSucursal(idExistencia)));
                    break;

                case "get/modelos":
                    string idMarca = context.Request["id_marca"];
                    context.Response.Write(JsonConvert.SerializeObject(GetModelos(idMarca)));
                    break;

                case "get/producto_detalle":
                    string idDetalle = context.Request["id"];
                    context.Response.Write(JsonConvert.SerializeObject(Detalle(idDetalle)));
                    break;

                case "get/proveedores":
                    context.Response.Write(JsonConvert.SerializeObject(GetProveedores()));
                    break;
                case "get/categorias":
                    context.Response.Write(JsonConvert.SerializeObject(GetCategorias()));
                    break;
                case "get/categorias/contribuyente":
                    context.Response.Write(JsonConvert.SerializeObject(GetCategoriasContribuyente()));
                    break;
                case "get/sub_categorias":
                    string idCategoria = context.Request["id_categoria"];
                    context.Response.Write(JsonConvert.SerializeObject(GetSubCategorias(idCategoria)));
                    break;
                case "get/marcas":
                    context.Response.Write(JsonConvert.SerializeObject(GetMarcas()));
                    break;
                case "get/monedas":
                    context.Response.Write(JsonConvert.SerializeObject(GetMonedas()));
                    break;
                case "get/anios":
                    context.Response.Write(JsonConvert.SerializeObject(GetAnios()));
                    break;
                case "crear/producto":
                    context.Response.Write(JsonConvert.SerializeObject(CrearProducto()));
                    break;
                case "get/producto":
                    string codigo = context.Request["codigo"];
                    context.Response.Write(JsonConvert.SerializeObject(GetProducto(codigo)));
                    break;

                default:
                    break;
            }
        }



        public response GetProducts()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<VWInventarioContribuyenteDTO> productos = new List<VWInventarioContribuyenteDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                    rest.Add("pIdSucursal", sessionManager.GetIdSucursal().ToString());
                    rest.Add("pSoloExistencia", "S");
                    productos = rest.GenericRestClient<List<VWInventarioContribuyenteDTO>, DBNull>("api_inventory_get_products", token.access_token, TypeMethod.Get);

                    if (productos.Count() > 0)
                    {
                        response.data = productos;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado del inventario de los productos";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de los productos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }









        /// <summary>
        /// Obtiene todos los proveedores
        /// </summary>
        /// <returns></returns>
        public response GetProveedores()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModProveedorDTO> proveedores = new List<ModProveedorDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    proveedores = rest.GenericRestClient<List<ModProveedorDTO>, DBNull>("api_mod_proveedor_get_list", token.access_token, TypeMethod.Get);

                    if (proveedores.Count() > 0)
                    {
                        response.data = proveedores;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado del inventario de los productos";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de los productos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene las categorias de los productos
        /// </summary>
        /// <returns></returns>
        public response GetCategorias()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModProductoCategoriaDTO> categorias = new List<ModProductoCategoriaDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    categorias = rest.GenericRestClient<List<ModProductoCategoriaDTO>, DBNull>("api_mod_categoria_producto_get_list", token.access_token, TypeMethod.Get);

                    if (categorias.Count() > 0)
                    {
                        response.data = categorias;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado del inventario de los productos";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de los productos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene las categorias de los productos por contribuyente
        /// </summary>
        /// <returns></returns>
        public response GetCategoriasContribuyente()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModProductoCategoriaGenericoDTO> categorias = new List<ModProductoCategoriaGenericoDTO>();

                try
                {

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                    categorias = rest.GenericRestClient<List<ModProductoCategoriaGenericoDTO>, DBNull>("api_mod_categoria_producto_get_list_contribuyente", token.access_token, TypeMethod.Get);

                    if (categorias.Count() > 0)
                    {
                        response.data = categorias;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado de categorias";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de categorias: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }



        /// <summary>
        /// Obtiene las subcategorias de los productos
        /// </summary>
        /// <returns></returns>
        public response GetSubCategorias(string id_categoria)
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModProductoCategoriaGenericoDTO> subcategorias = new List<ModProductoCategoriaGenericoDTO>();

                try
                {
                    string idDesencriptado = encript_core.GetValue(id_categoria);

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("idCategoria", idDesencriptado);

                    subcategorias = rest.GenericRestClient<List<ModProductoCategoriaGenericoDTO>, DBNull>("api_mod_sub_categoria_producto_get_list", token.access_token, TypeMethod.Get);

                    if (subcategorias.Count() > 0)
                    {
                        response.data = subcategorias;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado de las subcategorias";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de subcategorias: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }



        /// <summary>
        /// Obtiene las marcas de los productos
        /// </summary>
        /// <returns></returns>
        public response GetMarcas()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<ModProductoMarcaDTO> marcas = new List<ModProductoMarcaDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    marcas = rest.GenericRestClient<List<ModProductoMarcaDTO>, DBNull>("api_mod_marca_producto_get_list", token.access_token, TypeMethod.Get);

                    if (marcas.Count() > 0)
                    {
                        response.data = marcas;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado del inventario de los productos";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de los productos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene las monedas disponibles para el contribuyente
        /// </summary>
        /// <returns></returns>
        public response GetMonedas()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<object> monedas = new List<object>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pIdCobtribuyente", sessionManager.GetIdContribuyente().ToString());
                    monedas = rest.GenericRestClient<List<object>, DBNull>("api_mod_moneda_contribuyente_get_list", token.access_token, TypeMethod.Get);

                    if (monedas.Count() > 0)
                    {
                        response.data = monedas;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado de monedas";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de monedas: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Obtiene la lista de años disponibles para productos
        /// Implementa la misma lógica que endpoints_a_consutlar.txt
        /// </summary>
        /// <returns></returns>
        public response GetAnios()
        {
            response response = new response();

            try
            {
                string rangoAniosPermitidos = ConfigurationManager.AppSettings["anios_permitidos"];

                int anioActual = DateTime.Today.Year;
                int anioMinimo = anioActual - int.Parse(rangoAniosPermitidos);
                int cantidadAnios = int.Parse(rangoAniosPermitidos) + 2;

                // Generar la lista de años
                var anios = Enumerable.Range(anioMinimo, cantidadAnios)
                    .OrderByDescending(x => x)
                    .Select(x => new
                    {
                        anio = x,
                        descripcion = x.ToString()
                    })
                    .ToList();

                if (anios.Count > 0)
                {
                    response.data = anios;
                    response.text = "Lista de años obtenida correctamente.";
                    response.type = TIPO_MENSAJE.SUCCESS;
                }
                else
                {
                    response.text = "No se pudo generar la lista de años";
                    response.type = TIPO_MENSAJE.WARNING;
                }
            }
            catch (Exception ex)
            {
                response.title = "";
                response.text = $"Error al obtener la lista de años: {ex.Message}";
                response.type = TIPO_MENSAJE.ERROR;
            }

            return response;
        }















        public response ListadoProducto()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<VWInventarioContribuyenteDTO> productos = new List<VWInventarioContribuyenteDTO>();

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pSoloExistencia", "S");
                    productos = rest.GenericRestClient<List<VWInventarioContribuyenteDTO>, DBNull>("api_inventory_get_products", token.access_token, TypeMethod.Get);

                    if (productos.Count > 0)
                    {
                        response.data = productos;
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se encontraron productos.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.text = $"Error al obtener productos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }

            return response;
        }


        public response ExistenciaSucursal(string id)
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (!string.IsNullOrWhiteSpace(id) && tokenManager.Valid())
            {
                try
                {
                    string idDesencriptado = encript_core.GetValue(id);
                    token_response token = tokenManager.Get();

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("IdProduct", idDesencriptado);
                    var productos = rest.GenericRestClient<List<ModProductoSucursalDTO>, DBNull>("api_inventory_get_product_existence", token.access_token, TypeMethod.Get);

                    response.data = productos;
                    response.text = "Existencia obtenida correctamente.";
                    response.type = TIPO_MENSAJE.SUCCESS;
                }
                catch (Exception ex)
                {
                    response.text = $"Error al obtener existencia: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }

            return response;
        }

        public response GetModelos(string id_marca)
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (!string.IsNullOrWhiteSpace(id_marca) && tokenManager.Valid())
            {
                try
                {
                    string idDesencriptado = encript_core.GetValue(id_marca);
                    token_response token = tokenManager.Get();

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("id_marca", idDesencriptado);

                    var modelos = rest.GenericRestClient<List<ModProductoModeloDTO>, DBNull>("api_mod_modelo_producto_get_list_marca", token.access_token, TypeMethod.Get);
                    response.data = modelos;

                    if (modelos.Count > 0)
                    {
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No hay modelos registrados.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.text = $"Error al obtener modelos: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }

            return response;
        }


        public response Detalle(string id)
        {
            response response = new response();
            TokenManager tokenManager = new TokenManager();

            if (!string.IsNullOrWhiteSpace(id) && tokenManager.Valid())
            {
                try
                {
                    string idDesencriptado = encript_core.GetValue(id);
                    var producto = GetProductoById(idDesencriptado);

                    response.data = producto;
                    response.text = "Detalle obtenido correctamente.";
                    response.type = TIPO_MENSAJE.SUCCESS;
                }
                catch (Exception ex)
                {
                    response.text = $"Error al obtener detalle: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }

            return response;
        }

        private ModProductoDTO GetProductoById(string idProducto)
        {
            ModProductoDTO producto = new ModProductoDTO();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                rest.Add("idProducto", idProducto);
                producto = rest.GenericRestClient<ModProductoDTO, DBNull>("api_get_product_sucursal", token.access_token, TypeMethod.Get);
            }

            return producto;
        }


        public response CrearProducto()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            // JSON de referencia para pruebas - Estructura esperada por la API
            string jsonReferencia = @"{
                ""id_producto"": 0,
                ""id_contribuyente"": 123,
                ""id_sucursal"": 1,
                ""codigo"": ""0987"",
                ""nombre"": ""Producto de Prueba"",
                ""descripcion"": ""Descripción del producto"",
                ""id_sub_categoria"": 5,
                ""sub_categoria"": ""Computadoras"",
                ""id_marca"": 2,
                ""marca"": ""ACURA"",
                ""id_modelo"": 3,
                ""modelo"": ""HI"",
                ""anio"": 2024,
                ""id_proveedor"": 1,
                ""proveedor"": ""Formaggi Fortini"",
                ""id_moneda"": 1,
                ""moneda"": ""GTQ"",
                ""costo_unitario"": 100.00,
                ""precio_unitario"": 150.00,
                ""min_descuento"": 120.00,
                ""stock"": 50,
                ""stock_maximo"": 100,
                ""stock_minimo"": 10,
                ""fecha_compra"": ""2024-01-15"",
                ""referencia_compra"": ""REF-001"",
                ""fecha_vencimiento"": ""2025-01-15"",
                ""img_producto"": ""data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPoAAAD6...""
            }";

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                try
                {
                    token_response token = tokenManager.Get();

                    // CAPTURAR Y MOSTRAR EL JSON REAL QUE VIENE DEL FRONTEND
                    string jsonFromFrontend = HttpContext.Current.Request["data"];
                    string decodedJson = "";
                    if (!string.IsNullOrEmpty(jsonFromFrontend))
                    {
                        decodedJson = RequestParameters.mtdValue(jsonFromFrontend);
                        System.Diagnostics.Debug.WriteLine("=== JSON REAL DEL FRONTEND ===");
                        System.Diagnostics.Debug.WriteLine(decodedJson);
                        System.Diagnostics.Debug.WriteLine("=== FIN JSON FRONTEND ===");

                        // Guardar en archivo de log
                        try
                        {
                            string logPath = HttpContext.Current.Server.MapPath("~/frontend_json_log.txt");
                            System.IO.File.AppendAllText(logPath,
                                $"\n\n=== {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===\n{decodedJson}\n");
                        }
                        catch { /* Ignorar errores de escritura */ }
                    }

                    ModProductoDTO modulo = RequestParameters.GetObject<ModProductoDTO>("data");

                    modulo.id_contribuyente = (int)sessionManager.GetIdContribuyente();
                    modulo.id_sucursal = (int)sessionManager.GetIdSucursal();

                    if (String.IsNullOrEmpty((modulo.id_producto).ToString()))
                    {
                        modulo.id_producto = 0;
                    }

                    // JSON que se va a enviar al endpoint
                    string hola = JsonConvert.SerializeObject(modulo, Formatting.Indented);
                    System.Diagnostics.Debug.WriteLine("=== JSON PARA ENDPOINT ===");
                    System.Diagnostics.Debug.WriteLine(hola);
                    System.Diagnostics.Debug.WriteLine("=== FIN JSON ENDPOINT ===");

                    // Guardar también en archivo de log
                    try
                    {
                        string logPath = HttpContext.Current.Server.MapPath("~/endpoint_json_log.txt");
                        System.IO.File.AppendAllText(logPath,
                            $"\n\n=== ENDPOINT JSON {DateTime.Now:yyyy-MM-dd HH:mm:ss} ===\n{hola}\n");
                    }
                    catch { /* Ignorar errores de escritura */ }

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());

                    var resultado = rest.GenericRestClient<int, ModProductoDTO>("api_inventory_get_product_create", token.access_token, TypeMethod.Post, modulo);

                    if (resultado > 0)
                    {
                        response.data = resultado;
                        response.text = $"Producto guardado exitosamente. JSON enviado al endpoint: {hola}";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = $"No se pudo guardar el producto. JSON enviado al endpoint: {hola}";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.text = $"Error al guardar el producto: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            else
            {
                response.text = "Sesión no válida";
                response.type = TIPO_MENSAJE.ERROR;
            }

            return response;
        }

        public response GetProducto(string codigo)
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (!string.IsNullOrWhiteSpace(codigo) && sessionManager.Valid() && tokenManager.Valid())
            {
                try
                {
                    token_response token = tokenManager.Get();
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                    // Agregar los parámetros requeridos por la API
                    rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                    rest.Add("pIdSucursal", sessionManager.GetIdSucursal().ToString());
                    rest.Add("pCodProduct", codigo);

                    // Llamar al endpoint configurado en Web.config
                    var productos = rest.GenericRestClient<List<object>, DBNull>("api_mod_get_producto", token.access_token, TypeMethod.Get);

                    if (productos != null && productos.Count > 0)
                    {
                        response.data = productos;
                        response.text = "Producto encontrado correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se encontró el producto con el código especificado.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.text = $"Error al buscar el producto: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            else
            {
                response.text = "Código de producto requerido o sesión no válida.";
                response.type = TIPO_MENSAJE.ERROR;
            }

            return response;
        }

    }
}
