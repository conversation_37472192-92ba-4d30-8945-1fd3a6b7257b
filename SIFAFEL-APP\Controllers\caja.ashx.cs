﻿using Newtonsoft.Json;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.api_response;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Modulos;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.SessionState;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Descripción breve de caja
    /// </summary>
    public class caja : SessionHandler
    {
        public override void HandleRequest(HttpContext context)
        {
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "get/medio/pago":
                    context.Response.Write(JsonConvert.SerializeObject(GetMediosPago()));
                    break;
                case "realizar/operacion":
                    context.Response.Write(JsonConvert.SerializeObject(RealizarOperacion()));
                    break;
                case "get/cajas/abiertas":
                    context.Response.Write(JsonConvert.SerializeObject(GetCajasAbiertas()));
                    break;
                //
                case "get/cajas/operaciones":
                    context.Response.Write(JsonConvert.SerializeObject(GetCajasOperaciones()));
                    break;
                case "get/cajas/transacciones":
                    context.Response.Write(JsonConvert.SerializeObject(GetTransaccionesOperacion()));
                    break;
                case "get/medio/pagoTransaccion":
                    context.Response.Write(JsonConvert.SerializeObject(GetMedioPagoTransaccion()));
                    break;


                default:
                    context.Response.StatusCode = 404;
                    context.Response.StatusDescription = "Not Found";
                    break;
            }
        }

        /// <summary>
        /// Recuperar los medios de pago de una caja
        /// </summary>
        /// <returns></returns>
        public response GetMediosPago()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                try
                {
                    token_response token = tokenManager.Get();
                    _ModuloCaja modulo = RequestParameters.GetObject<_ModuloCaja>("data");

                    modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                    modulo.id_sucursal = sessionManager.GetIdSucursal();

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_caja_medios", token.access_token, TypeMethod.Post, modulo);

                }
                catch (Exception ex)
                {

                }
            }

            return response;
        }

        /// <summary>
        /// Recupera las cajas abiertas de una sucursal
        /// </summary>
        /// <returns></returns>
        public response GetCajasAbiertas()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                _ModuloCaja modulo = new _ModuloCaja();

                modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                modulo.id_sucursal = sessionManager.GetIdSucursal();
                modulo.fecha = DateTime.Parse(RequestParameters.GetValue("fecha"));

                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_cajas_abiertas", token.access_token, TypeMethod.Post, modulo);
            }

            return response;
        }

        /// <summary>
        /// Realizar operacion
        /// </summary>
        /// <returns></returns>
        public response RealizarOperacion()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                _ModuloCaja modulo = RequestParameters.GetObject<_ModuloCaja>("data");

                modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                modulo.id_sucursal = sessionManager.GetIdSucursal();

                if (modulo.tipo == Estado.APERTURA)
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_caja_apertura", token.access_token, TypeMethod.Post, modulo);
                }
                else if (modulo.tipo == Estado.CIERRE)
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_caja_cierre", token.access_token, TypeMethod.Post, modulo);
                }
            }

            return response;
        }

        /// <summary>
        /// Obtiene las operaciones de las cajas
        /// </summary>
        /// <returns></returns>
        public response GetCajasOperaciones()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                _ModuloCaja modulo = RequestParameters.GetObject<_ModuloCaja>("data");

                modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                modulo.id_sucursal = sessionManager.GetIdSucursal();
                            
                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_cajas_operaciones", token.access_token, TypeMethod.Post, modulo);
            }

            return response;
        }

        /// <summary>
        /// Obtiene las transacciones de una operacion
        /// </summary>
        /// <returns></returns>
        public response GetTransaccionesOperacion()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                _ModuloCaja modulo = RequestParameters.GetObject<_ModuloCaja>("data");
                
                modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                modulo.id_sucursal = sessionManager.GetIdSucursal();

                
                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                response = rest.GenericRestClient<response, _ModuloCaja>("api_mod_cajas_transacciones", token.access_token, TypeMethod.Post, modulo);
            }

            return response;
        }

        /// <summary>
        /// Obtiene el medio de pago de una transaccion
        /// </summary>
        /// <returns></returns>
        public response GetMedioPagoTransaccion()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                _MedioPago modulo = RequestParameters.GetObject<_MedioPago>("data");

                modulo.id_contribuyente = sessionManager.GetIdContribuyente();
                modulo.id_sucursal = sessionManager.GetIdSucursal();


                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                response = rest.GenericRestClient<response, _MedioPago>("api_mod_cajas_medio_pago", token.access_token, TypeMethod.Post, modulo);
            }

            return response;
        }


    }
}