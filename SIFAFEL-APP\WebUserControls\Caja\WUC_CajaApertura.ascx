﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="WUC_CajaApertura.ascx.cs" Inherits="SIFAFEL_APP.WebUserControls.Caja.WUC_CajaApertura" %>

<!DOCTYPE html>
<html lang="es-gt" data-layout-mode="light_mode" data-layout-style="default" data-nav-color="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="SIFAFEL" />
    <meta name="keywords" content="Punto de venta, facturación electrónica" />
    <meta name="author" content="SIFAFEL" />
    <meta name="robots" content="noindex, nofollow" />
    <title></title>
    <script type="text/javascript">
        (function () {
            const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const htmlElement = document.documentElement;

            htmlElement.setAttribute('data-layout-mode', prefersDarkMode ? 'dark_mode' : 'light_mode');
            htmlElement.setAttribute('data-nav-color', prefersDarkMode ? 'dark' : 'light');
        })();
    </script>
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/animate.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/buttons.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/all.min.css" />

    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/style.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/main.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/daterangepicker/daterangepicker.css" />


</head>
<body class="sidebar-main-menu mini-sidebar">
   

    <style type="text/css">
        .swal2-popup {
            font-size: 0.85rem !important;
        }

        .amount {
            text-align: right !important;
        }

            .amount.total {
                font-weight: bold;
            }

        table {
            width: 100% !important;
        }

            table tfoot td.amount {
                text-align: right !important;
            }

                table tfoot td.amount.total {
                    font-weight: bold !important;
                }

        tr td {
        }

            tr td .currency {
                float: left;
            }

            tr td .price {
                float: right;
            }
    </style>


    <!-- Inputs ocultos -->
    <input type="hidden" id="caj_aper_txtFecha" />
    <input type="hidden" id="caj_aper_ddlCaja" />
    <input type="hidden" id="caj_aper_ddlMoneda" />


    <div class="modal fade" id="caj_aper_unauthorizedModal" tabindex="-1" aria-labelledby="caj_aper_unauthorizedModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="caj_aper_unauthorizedModalLabel">Sesión Expirada</h5>

                </div>
                <div class="modal-body">
                    Tu sesión ha expirado o no tienes autorización para realizar esta acción. Por favor, inicia sesión nuevamente.
                <input type="text" class="form-control" disabled="disabled" placeholder="Usuario" />
                    <input type="password" class="form-control" disabled="disabled" placeholder="Clave" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success">Continuar</button>
                    <button type="button" class="btn btn-danger">Salir</button>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript">
        let pendingRequestApertura = null; // Almacena la información de la solicitud pendiente

        $(document).ajaxError(function (event, jqxhr, settings, thrownError) {
            if (jqxhr.status === 401) {
                pendingRequestApertura = {
                    url: settings.url,
                    type: settings.type,
                    data: settings.data || null, // Datos enviados en la solicitud
                    contentType: settings.contentType || 'application/json', // Tipo de contenido
                    headers: settings.headers || {}, // Encabezados personalizados
                };
                $('#caj_aper_unauthorizedModal').modal('show');
            } else if (thrownError === 'ERR_CONNECTION_REFUSED') {
                console.error('Error: Connection refused. Unable to reach the server.');
            } else {
                console.error(`Unhandled AJAX error: ${thrownError}`);
            }
        });

        window.addEventListener('error', function (event) {
            console.log(event.target.tagName);
            if (event.target.tagName === 'IMG') {
                //event.target.src = `${_url_cdn}img/products/icon.png`;
                //event.preventDefault();
                //PENDIENTE MEJORAR
            }
        }, true);

        function __GetDateTodayApertura() {
            let fechaSeleccionada = new Date();
            let dia = fechaSeleccionada.getDate().toString().padStart(2, '0');
            let mes = (fechaSeleccionada.getMonth() + 1).toString().padStart(2, '0');
            let anio = fechaSeleccionada.getFullYear();

            return `${dia}/${mes}/${anio}`;
        }

        function __GetInputValueApertura(value) {
            return value === '' || value === undefined ? null : value;
        }

        Date.prototype.toDateInputValue = (function () {
            var local = new Date(this);
            local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
            return local.toJSON().slice(0, 10);
        });

        function __ProgressApertura(message) {
            $("#caj_aper_x-loader-message").html(message);
            $("#caj_aper_global-loader-msg").fadeIn().css("display", "flex");
        }

        function __ProgressOffApertura() {
            $("#caj_aper_global-loader-msg").fadeOut(400, function () {
                $("#caj_aper_x-loader-message").html("");
            });
        }

        function __Format(p_fecha) {
            if (!p_fecha) return null;

            if (/^\d{4}-\d{2}-\d{2}$/.test(p_fecha)) {
                const partes_fecha = p_fecha.split("-");
                return `${partes_fecha[2]}/${partes_fecha[1]}/${partes_fecha[0]}`;
            }

            if (/^\d{2}\/\d{2}\/\d{4}$/.test(p_fecha)) {
                return p_fecha;
            }

            return p_fecha;
        }

        function __setMaxDateToToday(p_selector, p_retroactive_days = 0, p_just_today = false) {
            let today = new Date();

            const formattedToday = today.getFullYear() + '-' +
                String(today.getMonth() + 1).padStart(2, '0') + '-' +
                String(today.getDate()).padStart(2, '0');

            if (p_just_today) {
                $(p_selector).attr('min', formattedToday);
                $(p_selector).attr('max', formattedToday);
            } else {
                if (p_retroactive_days > 0) {
                    let minDate = new Date(today);
                    minDate.setDate(today.getDate() - p_retroactive_days);

                    const formattedMinDate = minDate.getFullYear() + '-' +
                        String(minDate.getMonth() + 1).padStart(2, '0') + '-' +
                        String(minDate.getDate()).padStart(2, '0');

                    $(p_selector).attr('min', formattedMinDate);
                } else {
                    $(p_selector).removeAttr('min');
                }
                $(p_selector).attr('max', formattedToday);
            }
            $(p_selector).val('').removeAttr('value');
        }

        function MoneyFormat(moneda, price) {
            return "<span class=\"currency\">" + moneda + "</span>" +
                "<span class=\"price\">" + PriceNumberFormat(price) + "</span>";
        }

        function getParameterByName(name, url) {
            if (!url) url = window.location.href; // Si no se pasa una URL, usamos la actual
            name = name.replace(/[\[\]]/g, '\\$&'); // Escapar caracteres especiales
            var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
            var results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return ''; // Si el parámetro no tiene valor, retornamos una cadena vacía
            return decodeURIComponent(results[2].replace(/\+/g, ' ')); // Devolvemos el valor del parámetro
        }

        function base64_file(base64str, fileName, contentType) {
            if (window.ReactNativeWebView) {
                // APP
                console.log("Metodo App");
                if (base64str && base64str.length > 0) {
                    const [name, ext] = fileName.split('.');
                    const response = {
                        d: base64str,
                        title: name,
                        ext: ext
                    };
                    window.ReactNativeWebView.postMessage(JSON.stringify(response));
                } else {
                    const errorResponse = { error: "No se puede descargar el documento seleccionado" };
                    window.ReactNativeWebView.postMessage(JSON.stringify(errorResponse));
                }
            } else {
                // NAVEGADOR
                console.log("Metodo Navegador");
                try {
                    const binary = atob(base64str.replace(/\s/g, ''));
                    const len = binary.length;
                    const buffer = new ArrayBuffer(len);
                    const view = new Uint8Array(buffer);

                    for (let i = 0; i < len; i++) {
                        view[i] = binary.charCodeAt(i);
                    }

                    const blob = new Blob([view], { type: contentType });

                    if (navigator.msSaveBlob) {
                        // IE/Edge
                        navigator.msSaveBlob(blob, fileName);
                    } else {
                        // Otros navegadores
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = fileName;

                        document.body.appendChild(link);
                        link.click();

                        setTimeout(() => {
                            document.body.removeChild(link);
                            window.URL.revokeObjectURL(link.href);
                        }, 1500);
                    }
                } catch (error) {
                    console.error("Ocurrió un error al procesar el archivo:", error);
                }
            }
        }

    </script>





    <%--                <form method="post" action="./apertura.aspx" id="caj_aper_frm_page">--%>
    <div class="aspNetHidden">
        <input type="hidden" name="__VIEWSTATE" id="caj_aper___VIEWSTATE" value="FNRQvrFxABo3YtzGfi3hbWKvxXn/4TOInAp649ZfkIxmP9jvhBU8HoQjmUphKlqsNb/X3GOYPZeL8bnC91WlWHfm0AKLyd3NsVxYgS16B3A=" />
    </div>

    <div class="aspNetHidden">

        <input type="hidden" name="__VIEWSTATEGENERATOR" id="caj_aper___VIEWSTATEGENERATOR" value="B101F98F" />
    </div>


    <div class="modal fade" id="caj_aper_modal_medios" tabindex="-1" aria-labelledby="caj_aper_modal_medios_label" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="caj_aper_modal_medios_label">
                        <i class="fas fa-coins"></i>&nbsp;Medios de pago
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                </div>
                <div class="modal-body">
                    <div id="caj_aper_x-div-medios" class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="caj_aper_tbl_medio_pago" class="table">
                                    <thead>
                                        <tr>
                                            <th>Medio</th>
                                            <th>Saldo inicial</th>
                                        </tr>
                                    </thead>
                                    <tfoot>
                                        <tr>
                                            <td class="amount total"><strong>Total</strong></td>
                                            <td class="amount total">
                                                <div class="input-group input-group-sm">
                                                    <span id="caj_aper_lbl_moneda_total" class="input-group-text"></span>
                                                    <input id="caj_aper_lbl_monto_total" class="form-control amount" disabled type="number" value="" />
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <div id="caj_aper_x-msg-modulo" class="alert alert-warning" role="alert">
                                <h4 class="alert-heading"></h4>
                                <p class="alert-message"></p>
                            </div>

                            <div class="d-flex justify-content-center mt-2">
                                <button id="caj_aper_btn_apertura_caja" type="button" class="btn btn-primary">
                                    <i data-feather="check"></i>Aperturar Caja
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <script type="text/javascript">
        var tbl_medio_pago;

        $(document).ready(function () {
            feather.replace();

            $("#caj_aper_x-msg-modulo").hide();
            $("#caj_aper_x-div-medios").hide();

            __setMaxDateToToday("#caj_aper_txtFecha", 5, true);
            $('#caj_aper_txtFecha').val(new Date().toDateInputValue());



            $("#caj_aper_btn_apertura_caja").on("click", function () {
                //traer los dats reales

                if (!$('#caj_aper_txtFecha').val()) { Swal.fire('Campo requerido', 'Selecciona una fecha.', 'warning'); return; }
                if (!$('#caj_aper_ddlCaja').val()) { Swal.fire('Campo requerido', 'Selecciona una caja.', 'warning'); return; }
                if (!$('#caj_aper_ddlMoneda').val()) { Swal.fire('Campo requerido', 'Selecciona una moneda.', 'warning'); return; }

                var tableData = tbl_medio_pago.rows().data();
                var recalculatedTotal = 0;
                var userEnteredTotal = parseFloat($('#caj_aper_lbl_monto_total').val()) || 0;

                tableData.each(function (row) { recalculatedTotal += parseFloat(row.monto_apertura) || 0; });

                if (userEnteredTotal !== recalculatedTotal) {
                    Swal.fire('Advertencia', 'El total ingresado manualmente no coincide con la suma de los medios de pago. Se usará el total calculado.', 'warning');
                } else {
                    if (recalculatedTotal > 0) {
                        __Aperturar();
                    } else {
                        Swal.fire({
                            title: 'Advertencia',
                            text: 'Desea abrir la caja con saldo cero?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: 'Sí, abrir',
                            cancelButtonText: 'No, cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                __Aperturar();
                            } else if (result.dismiss === Swal.DismissReason.cancel) {
                                Swal.close();
                            }
                        });
                    }
                }
            });

        });

        function __Aperturar() {
            var mediosPago = [];
            var tableData = tbl_medio_pago.rows().data();
            var recalculatedTotal = 0;

            tableData.each(function (row) {
                mediosPago.push({
                    id_medio_pago: row.id_medio_pago,
                    monto: parseFloat(row.monto_apertura) || 0
                });
                recalculatedTotal += parseFloat(row.monto_apertura) || 0;
            });

            var jsonData = {
                fecha: __Format($('#caj_aper_txtFecha').val()),
                id_caja: $('#caj_aper_ddlCaja').val() ? parseInt($('#caj_aper_ddlCaja').val()) : null,
                id_moneda: $('#caj_aper_ddlMoneda').val() ? parseInt($('#caj_aper_ddlMoneda').val()) : null,
                tipo: "APE",
                total: parseFloat(recalculatedTotal.toFixed(2)),
                medios_pago: mediosPago
            };

            __ProgressApertura(`Aperturando...`);

            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("realizar/operacion"));
                fmAuth.append("data", mtdEnc(JSON.stringify(jsonData)));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {

                    },
                    success: function (response) {
                        if (response) {
                            Swal.fire(response.title || '', response.text || '', response.type || '');
                            if (response.type == "success") {
                                setTimeout(function () {
                                    //$('#caj_aper_btn_consulta_caja').click();
                                    $("#caj_aper_modal_medios").modal("hide");
                                }, 50);
                            }
                        }
                    },
                    complete: function () {
                        __ProgressOffApertura();
                    }
                });
            }, 50);
        }




        function abrirTablaCajaApertura(fecha, id_caja, id_moneda) {
            $("#caj_aper_x-msg-modulo").hide();

            if (!fecha) { Swal.fire('Campo requerido', 'Seleccione una fecha en la URL.', 'warning'); return; }
            if (!id_caja) { Swal.fire('Campo requerido', 'Seleccione una caja en la URL.', 'warning'); return; }
            if (!id_moneda) { Swal.fire('Campo requerido', 'Seleccione una moneda en la URL.', 'warning'); return; }

            __ProgressApertura(`Consultando...`);

            setTimeout(function () {
                var jsonData = JSON.stringify({
                    fecha: __Format(fecha),
                    id_caja: parseInt(id_caja),
                    id_moneda: parseInt(id_moneda),
                    tipo: "APE"
                });

                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("get/medio/pago"));
                fmAuth.append("data", mtdEnc(jsonData));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
               type: "post",
               contentType: false,
               processData: false,
               ContentType: "text/html;charset=utf-8",
               dataType: "json",
               data: fmAuth,
               async: true,
               beforeSend: function () {
                   if (tbl_medio_pago) {  
                       tbl_medio_pago.clear().draw();
                   }

                   $("#caj_aper_x-div-medios").hide();
                   $("#caj_aper_btn_apertura_caja").hide();
               },
               success: function (response) {
                   if (response) {

                       const alertType = response.type === "error" ? "danger" : response.type;
                       $("#caj_aper_x-msg-modulo").removeAttr('class').addClass(`alert alert-${alertType}`).show();
                       $("#caj_aper_x-msg-modulo").find(".alert-heading").html(response.title);
                       $("#caj_aper_x-msg-modulo").find(".alert-message").html(response.text);

                       $("#caj_aper_x-div-medios").show();

                       if (response.data_tables && response.data_tables[0] && response.data_tables[0].length > 0) {

                           // Si el DataTable no está inicializado aún, inicialízalo
                           if (!$.fn.DataTable.isDataTable('#caj_aper_tbl_medio_pago')) {
                               tbl_medio_pago = $('#caj_aper_tbl_medio_pago').DataTable({
                                   "bFilter": false,
                                   "ordering": false,
                                   "bLengthChange": false,
                                   "bInfo": false,
                                   "paging": false,
                                   "destroy": true,
                                   "autoWidth": false,
                                   "responsive": true,
                                   "language": {
                                       search: ' ',
                                       searchPlaceholder: "Search",
                                       info: " ",
                                       paginate: {
                                           next: ' <i class=" fa fa-angle-right"></i>',
                                           previous: '<i class="fa fa-angle-left"></i> '
                                       },
                                   },
                                   "columns": [
                                       {
                                           "data": "descripcion",
                                           "render": function (data, type, row) {
                                               let iconSrc = '';
                                               if (row.url_logo) {
                                                   iconSrc = _url_cdn + row.url_logo;
                                               } else {
                                                   iconSrc = 'https://via.placeholder.com/50';
                                               }
                                               return `<img class="medio-icon" src="${iconSrc}" alt="">&nbsp;${data}`;
                                           }
                                       },
                                       {
                                           "data": "monto_apertura",
                                           "render": function (data, type, row) {
                                               let monto = parseFloat(data) || 0;
                                               return `<div class="input-group input-group-sm">
                                    <span class="input-group-text">${row.moneda}</span>
                                    <input class="form-control amount" type="number" value="${monto.toFixed(2)}" />
                                </div>`;
                                           }
                                       }
                                   ],
                                   createdRow: function (row, data, dataIndex) {
                                       $(row).find("input.amount").on("input", function () {
                                           data.monto_apertura = parseFloat($(this).val()) || 0;
                                           var table_data = tbl_medio_pago.rows().data();
                                           let total_monto = 0;

                                           table_data.each(function (row) {
                                               total_monto += parseFloat(row.monto_apertura) || 0;
                                           });

                                           $('#caj_aper_lbl_moneda_total').text(data.moneda);
                                           $('#caj_aper_lbl_monto_total').val(total_monto.toFixed(2));
                                       });
                                   }
                               });
                           }

                           // Limpiar y agregar nuevas filas al DataTable existente
                           tbl_medio_pago.clear().draw();
                           tbl_medio_pago.rows.add(response.data_tables[0]);
                           tbl_medio_pago.draw();
                           $('#caj_aper_tbl_medio_pago').show();

                           // Disparar el evento de input en el primer campo de cantidad
                           setTimeout(function () {
                               var firstInput = $('#caj_aper_tbl_medio_pago').find('input.amount').first();
                               firstInput.trigger('input');
                           }, 50);

                           // Verificar el tipo de respuesta y mostrar/ocultar el botón
                           if (response.type === "success") {
                               $("#caj_aper_btn_apertura_caja").show();
                           } else {
                               $("#caj_aper_btn_apertura_caja").hide();

                               // Deshabilitar los campos de entrada en las filas
                               tbl_medio_pago.rows().every(function () {
                                   $(this.node()).find('input').prop('disabled', true);
                               });
                           }
                       }                   }
               },
               complete: function () {
                   __ProgressOffApertura();
               }
           });
       }, 50);
        };

        const __fnChangeSucursalApertura = (element, pIdSucursal, pNombreSucursal) => {
            let idSucursal = $("#caj_aper_x-selected-store-name").data("id");

            if (idSucursal != pIdSucursal) {
                Swal.fire({
                    title: "¿Desea cambiar de sucursal?",
                    imageUrl: "<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/change-store.png",
                    imageHeight: 80,
                    showCancelButton: true,
                    confirmButtonText: "Aceptar",
                    cancelButtonText: `Cancelar`
                }).then((result) => {
                    if (result.isConfirmed) {
                        let nomSucursal = mtdValue(pNombreSucursal);

                        var fmAuth = new FormData();
                        fmAuth.append("mth", mtdEnc("change/sucursal"));
                        fmAuth.append("pIdSucursal", pIdSucursal);

                        $.ajax({
                            url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/master.ashx",
                            type: "post",
                            contentType: false,
                            processData: false,
                            ContentType: "text/html;charset=utf-8",
                            dataType: "json",
                            data: fmAuth,
                            async: true,
                            beforeSend: function () {
                                __ProgressApertura(`Actualizando Sucursal<br/><br/>"${nomSucursal}"`);
                            },
                            success: function (result, arg) {
                                if (result) {
                                    if (result.type == "success") {
                                        setTimeout(function () {
                                            $("#caj_aper_x-selected-store-name").html(nomSucursal);
                                            $("#caj_aper_x-selected-store-name").data("id", pIdSucursal);

                                            $(element).closest(".dropdown-menu").find("a.dropdown-item").removeClass("active");
                                            $(element).addClass("active");

                                            __ProgressOffApertura();
                                            location.reload();
                                        }, 800);
                                    } else {
                                        Swal.fire({
                                            icon: result.type,
                                            text: result.text,
                                        });
                                    }
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        text: 'Ocurrió un error durante el proceso',
                                    });
                                }
                            },
                            complete: function () {
                                __ProgressOffApertura();
                            }
                        });
                    }
                });
            }
        };


        function __ModalCajaApertura(data = {}) {
            const {
                fecha = '',
                id_caja = '',
                id_monedaa = '',
            } = data;

            $("#caj_aper_txtFecha").val(data.fecha);
            $("#caj_aper_ddlCaja").val(data.id_caja);
            $("#caj_aper_ddlMoneda").val(data.id_moneda);

            abrirTablaCajaApertura(data.fecha, data.id_caja, data.id_moneda);

            $("#caj_aper_modal_medios").modal("show");

        }
    </script>
</body>
</html>
