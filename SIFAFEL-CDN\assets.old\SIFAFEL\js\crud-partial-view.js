﻿/*const { computeStyles } = require("@popperjs/core");*/
//jQuery.validator.setDefaults({
//    debug: true,
//    success: "valid"
//});
class partial_view {

    constructor(param) {
        if (param) {
            let config = {
                url: {
                    create: "Crear",
                    edit: "Editar",
                    delete: "Eliminar",
                    load_data: "Listado",
                    detail: "Detalle",
                },
                modal_size: "",
                js_datatable: {
                    language: {
                        url: _url_datatable_es
                    }
                },
                sText: {
                    sTitleCreate: "Crear registro",
                    sTitleEdit: "Editar registro",
                    sTitleDelete: "Eliminar registro",
                    sTitleDetail: "Detalle"
                }
            }

            let data = $.extend(true, config, param);
            let data_config = data.js_datatable;

            /*INIT*/
            this.__CreateModal("mdl" + data.id_container, data.modal_size);

            /*SET VALUE*/
            this.__containerTable = $("#" + data.id_container);
            this.__mdlPartial = $("#mdl" + data.id_container);
            this.__btnAddElement = $("#" + data.id_add_element);
            this.__id_add_element = data.id_add_element;
            this.__event_type = null;
            this.__data = data;
            this.__url_create = data.url.create;
            this.__url_edit = data.url.edit;
            this.__url_delete = data.url.delete;
            this.__url_load_data = data.url.load_data;
            this.__url_detail = data.url.detail;
            this.__js_datatable = data_config;
            this.__js_datatable_group = data_config.column_group;

            var element_value = this;
            this.__ReloadDataTable();

            $(this.__containerTable).addClass("table-responsive");

            /*INITIALIZATION*/
            this.__InitAddButton(this.__btnAddElement, element_value);

            $(this.__mdlPartial).find(".save-changes").on("click", function () {
                //validar formulario
                if ($(element_value.__mdlPartial).find("#input-form").valid({ lang: 'es' })) {
                    var $inputs = $(element_value.__mdlPartial).find('form :input');
                    //var _data = {};
                    var _data = new FormData();
                    $inputs.each(function (index) {
                        let data_value = $(this).val();
                        if ($(this).attr('type') == "file") {
                            var file = $(this)[0];
                            if (file.files && file.files[0]) {
                                data_value = file.files[0];
                            }
                        }
                        _data.append($(this).attr('name'), data_value);
                    });
                    console.log('_data: ', _data);
                    $.ajax({
                        url: element_value.__event_type,
                        data: _data,
                        type: "POST",
                        //contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                        contentType: false,
                        processData: false,
                        dataType: "json",
                        beforeSend: function () {
                            Holdon_Open("Procesando...");
                        },
                        success: function (result) {
                            if (result.type == "success") {
                                Swal.fire({
                                    icon: result.type,
                                    text: result.text,
                                });

                                element_value.__ReloadDataTable();
                                $(element_value.__mdlPartial).modal("hide");
                                $(element_value.__mdlPartial).find(".form-content").empty();
                            }
                            else {
                                Swal.fire({
                                    icon: result.type,
                                    text: result.text,
                                });
                            }
                        },
                        error: function (result) {
                            console.log('error:', result);
                            Swal.fire({
                                icon: 'error',
                                text: 'Ocurrió un error durante el proceso',
                            });
                        },
                        complete: function () {
                            Holdon_Close();
                        }
                    });
                }
                return false;
            });

        } else {
            Swal.fire({
                icon: 'error',
                text: 'No data',
            });
        }
    }

    __InitAddButton(btnAddItem, element_value) {
        $(btnAddItem).on("click", function () {
            Holdon_Open("Cargando...");

            $(element_value.__mdlPartial).find(".save-changes").show();
            $(element_value.__mdlPartial).find(".form-content").empty().load(element_value.__url_create, function () {
                $(element_value.__mdlPartial).find(".title").html(element_value.__data.sText.sTitleCreate);
                $(element_value.__mdlPartial).modal("show");

                Holdon_Close();

                //inititae jQuery validation
                //$(element_value.__mdlPartial).find("#input-form").validate();
            });
            element_value.__event_type = element_value.__url_create;
        });
    }

    __ReloadDataTable() {
        Holdon_Open("Cargando...");

        var element_value = this;
        $(this.__containerTable).load(this.__url_load_data, function () {
            element_value.__js_datatable.initComplete = function (settings, json) {
                if ($(element_value.__btnAddElement).length == 0) {
                    var buttonAdd = $("#" + element_value.__id_add_element);
                    element_value.__InitAddButton($(buttonAdd), element_value);
                }
            };
            var table = $(element_value.__containerTable).find("table").DataTable(element_value.__js_datatable);

            if (element_value.__js_datatable_group) {
                $(element_value.__containerTable).find('table tbody').on('click', 'tr.group', function () {
                    var currentOrder = table.order()[0],
                        groupColumn = element_value.__js_datatable_group;
                    if (currentOrder[0] === groupColumn && currentOrder[1] === 'asc') {
                        table.order([groupColumn, 'desc']).draw();
                    }
                    else {
                        table.order([groupColumn, 'asc']).draw();
                    }
                });
            }
            setTimeout(function () {
                //table.columns.adjust().draw();
            }, 2000);

            Holdon_Close();
        });

    }

    __Edit(pId) {
        Holdon_Open("Cargando...");

        var element_value = this;
        this.__event_type = this.__url_edit;

        $(this.__mdlPartial).find(".save-changes").show();
        $(this.__mdlPartial).find(".form-content").empty().load(this.__url_edit + "?id=" + pId, function () {
            $(element_value.__mdlPartial).find(".title").html(element_value.__data.sText.sTitleEdit);
            $(element_value.__mdlPartial).modal("show");

            Holdon_Close();
        });
    }

    __Detail(pId) {
        Holdon_Open("Cargando...");

        var element_value = this;
        this.__event_type = this.__url_detail;

        $(this.__mdlPartial).find(".save-changes").hide();
        $(this.__mdlPartial).find(".form-content").empty().load(this.__url_detail + "?id=" + pId, function () {
            $(element_value.__mdlPartial).find(".title").html(element_value.__data.sText.sTitleDetail);
            $(element_value.__mdlPartial).modal("show");

            Holdon_Close();
        });
    }

    __Delete(pId) {
        var element_value = this;
        this.__event_type = this.__url_delete;

        $(this.__mdlPartial).find(".save-changes").hide();
        $(this.__mdlPartial).find(".form-content").empty().load(this.__url_delete, function () {
            $(element_value.__mdlPartial).find(".title").html(element_value.__data.sText.sTitleDelete);
            $(element_value.__mdlPartial).modal("hide");
        });

        Swal.fire({
            title: 'Desea eliminar el registro?',
            icon: 'question',
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: 'Si',
            denyButtonText: 'No'
        }).then((result) => {
            if (result.isConfirmed) {
                var token = $(element_value.__mdlPartial).find(".form-content").find('input[name="__RequestVerificationToken"]').val();
                $.ajax({
                    url: element_value.__event_type + "?id=" + pId,
                    type: "POST",
                    data: {
                        __RequestVerificationToken: token
                    },
                    contentType: "application/x-www-form-urlencoded; charset=utf-8",
                    dataType: "json",
                    beforeSend: function () {
                        Holdon_Open("Procesando...");
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            Swal.fire(result.text, '', result.type)
                            element_value.__ReloadDataTable();
                        }
                        else {
                            Swal.fire(result.text, '', result.type)
                        }
                    },
                    error: function (result) {
                        console.log('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrió un error durante el proceso',
                        });
                    },
                    complete: function () {
                        Holdon_Close();
                        $(element_value.__mdlPartial).find(".form-content").empty();
                    }
                });

            } else {
                $(element_value.__mdlPartial).find(".form-content").empty();
            }
        })
    }

    __CreateModal(pIdModal, modal_size) {
        var element_value = this;

        var HTML_MODAL = '<section><div class="modal fade modal-crud" id="' + pIdModal + '"><div class="modal-dialog ' + (modal_size || '') + '"><div class="modal-content"><div class="modal-header"><a href="#" class="close" data-dismiss="modal">&times;</a><h4 class="title">title</h4></div><div class="modal-body"><div class="form-content"></div></div><div class="modal-footer"><button type="button" role="button" class="btn btn-success btn-sm save-changes"><i class="fa fa-save"></i>&nbsp;Guardar</button></div></div></div></div></section>';
        $("body").append(HTML_MODAL);

        $('#' + pIdModal).on('hidden.bs.modal', function () {
            $(element_value.__mdlPartial).find(".form-content").empty();
        })
    }
}