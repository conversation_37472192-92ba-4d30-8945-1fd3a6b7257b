﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="MasterPrincipal_NoAuth.master.cs" Inherits="SIFAFEL_APP.Master.MasterPrincipal_NoAuth" %>

<%@ Register Src="~/WebUserControls/WUC_Loader.ascx" TagPrefix="uc1" TagName="WUC_Loader" %>

<!DOCTYPE html>
<html lang="es-gt" data-layout-mode="light_mode" data-layout-style="default" data-nav-color="light">
<head runat="server">
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="SIFAFEL" />
    <meta name="keywords" content="Punto de venta, facturación electrónica" />
    <meta name="author" content="SIFAFEL" />
    <meta name="robots" content="noindex, nofollow" />
    <title><%=nombre_pagina %></title>
    <script type="text/javascript">
        (function () {
            const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const htmlElement = document.documentElement;

            htmlElement.setAttribute('data-layout-mode', prefersDarkMode ? 'dark_mode' : 'light_mode');
            htmlElement.setAttribute('data-nav-color', prefersDarkMode ? 'dark' : 'light');
        })();
    </script>
    <link rel="shortcut icon" type="image/x-icon" href="<%=ConfigurationManager.AppSettings["url_cdn"]+infoUsuario?.sucursal_session?.path_directory+"logo-small.png" %>" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/animate.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/buttons.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/all.min.css" />

    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/style.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/main.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/daterangepicker/daterangepicker.css" />

    <asp:ContentPlaceHolder ID="cph_head" runat="server">
    </asp:ContentPlaceHolder>
</head>
<body class="sidebar-main-menu mini-sidebar">
    <script type="text/javascript">
        var _url_redirect = "<%=ConfigurationManager.AppSettings["url_redirect"] %>";
        var _url_cdn = "<%=ConfigurationManager.AppSettings["url_cdn"] %>";
    </script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/jquery-3.7.1.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/sweetalert2/sweetalert2.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/plugin.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/feather.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/jquery.slimscroll.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/dataTables/jquery.dataTables.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/dataTables/dataTables.bootstrap5.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/bootstrap.bundle.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/theme-script.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/dataTables/buttons.html5.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/dataTables/buttons.print.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/dataTables/jszip.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/moment/moment.min.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/daterangepicker/daterangepicker.js"></script>

    <uc1:WUC_Loader runat="server" ID="WUC_Loader" />

    <div class="main-wrapper">

        <div class="header">
            <div class="header-left active">
                <a href="<%=ConfigurationManager.AppSettings["url_redirect"] %>" class="logo logo-normal">
                    <img src="<%=ConfigurationManager.AppSettings["url_cdn"]+infoUsuario?.sucursal_session?.path_directory+"logo.png" %>" alt="" />
                </a>
                <a href="<%=ConfigurationManager.AppSettings["url_redirect"] %>" class="logo logo-white">
                    <img src="<%=ConfigurationManager.AppSettings["url_cdn"]+infoUsuario?.sucursal_session?.path_directory+"logo.png" %>" alt="" />
                </a>
                <a href="<%=ConfigurationManager.AppSettings["url_redirect"] %>" class="logo-small">
                    <img src="<%=ConfigurationManager.AppSettings["url_cdn"]+infoUsuario?.sucursal_session?.path_directory+"logo-small.png" %>" alt="" />
                </a>
                <a id="toggle_btn" href="#!">
                    <i data-feather="chevrons-left" class="feather-16"></i>
                </a>
            </div>

            <a id="mobile_btn" class="mobile_btn" href="#sidebar">
                <span class="bar-icon">
                    <span></span>
                    <span></span>
                    <span></span>
                </span>
            </a>

            <ul class="nav user-menu">
                <li class="nav-item nav-searchinputs">
                    <div class="top-nav-search">
                        <a href="javascript:void(0);" class="responsive-search">
                            <i class="fa fa-search"></i>
                        </a>
                        <form action="#" class="dropdown">
                            <div class="searchinputs dropdown-toggle" id="dropdownMenuClickable" data-bs-toggle="dropdown" data-bs-auto-close="false">
                                <input type="text" placeholder="Buscar" />
                                <div class="search-addon">
                                    <span><i data-feather="x-circle" class="feather-14"></i></span>
                                </div>
                            </div>

                            <%--<div class="dropdown-menu search-dropdown" aria-labelledby="dropdownMenuClickable">
                                <div class="search-info">
                                    <h6><span><i data-feather="search" class="feather-16"></i></span>Busquedas recientes
                                    </h6>
                                    <ul class="search-tags">
                                        <li><a href="javascript:void(0);">Productos</a></li>
                                        <li><a href="javascript:void(0);">Ventas</a></li>
                                    </ul>
                                </div>
                            </div>--%>
                        </form>
                    </div>
                </li>
                <li class="nav-item dropdown ">
                    <a href="#!" class="dropdown-toggle nav-link select-store" data-bs-toggle="dropdown">
                        <span class="user-info">
                            <span class="user-letter">
                                <img src="<%=ConfigurationManager.AppSettings["url_cdn"]+infoUsuario?.sucursal_session?.path_directory+"logo-small.png" %>" alt="" style="border-radius: unset !important;" />
                            </span>
                            <span class="user-detail">
                                <span id="x-selected-store-name" data-id="<%=encript_string(infoUsuario?.sucursal_session?.id.ToString()) %>" class="user-name">
                                    <%=(infoUsuario?.sucursal_session?.descripcion??"Seleccionar tienda") %>
                                </span>
                            </span>
                        </span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <%foreach (var contribuyente in infoUsuario?.contribuyentes)
                            {%>
                        <a href="#!" class="dropdown-item item-contribuyente" title="<%=contribuyente.nombre %>">
                            <img src="<%=ConfigurationManager.AppSettings["url_cdn"]+contribuyente.path_directory+"logo-small.png" %>" />
                            <b><%=contribuyente.nombre %></b>
                        </a>
                        <% foreach (var sucursal in contribuyente.sucursales)
                            {%>
                        <a href="#!" class="dropdown-item <%=(sucursal.id == infoUsuario.sucursal_session.id ? "active" : "") %>"
                            onclick="__fnChangeSucursal(this, '<%=encript_string(sucursal.id.ToString()) %>','<%=encript_string(sucursal.descripcion.ToString()) %>')"><%=sucursal.descripcion %>
                        </a>
                        <%}%>
                        <%}%>
                    </div>
                </li>

                <li class="nav-item nav-item-box">
                    <a href="#!" id="btnFullscreen">
                        <i data-feather="maximize"></i>
                    </a>
                </li>

                <li class="nav-item dropdown nav-item-box">
                    <a href="#!" class="dropdown-toggle nav-link" data-bs-toggle="dropdown">
                        <i data-feather="bell"></i>
                        <%--<span class="badge rounded-pill">2</span>--%>
                    </a>
                    <div class="dropdown-menu notifications">
                        <div class="topnav-dropdown-header">
                            <span class="notification-title">Notificaciones</span>
                            <a href="#!" class="clear-noti">Eliminar todo</a>
                        </div>
                        <div class="noti-content">
                            <ul class="notification-list">
                            </ul>
                        </div>
                        <div class="topnav-dropdown-footer">
                            <a href="activities.html">Ver todas las notificaciones</a>
                        </div>
                    </div>
                </li>

                <li class="nav-item nav-item-box">
                    <a href="<%=ConfigurationManager.AppSettings["url_redirect"] %>Modulos/Configuracion/"><i data-feather="settings"></i></a>
                </li>

                <li class="nav-item dropdown has-arrow main-drop">
                    <a href="#!" class="dropdown-toggle nav-link userset" data-bs-toggle="dropdown">
                        <span class="user-info">
                            <span class="user-letter">
                                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %><%=infoUsuario?.img_perfil %>" alt="" class="img-fluid" />
                            </span>
                            <span class="user-detail">
                                <span class="user-name"><%=infoUsuario?.nombre_completo %></span>
                                <span class="user-role"><%=infoUsuario?.tipo %></span>
                            </span>
                        </span>
                    </a>
                    <div class="dropdown-menu menu-drop-user">
                        <div class="profilename">
                            <hr class="m-0" />
                            <a class="dropdown-item" href="<%=ConfigurationManager.AppSettings["url_redirect"] %>Cuenta">
                                <i class="me-2" data-feather="user"></i>
                                Mi perfil
                            </a>
                            <hr class="m-0" />
                            <a class="dropdown-item logout pb-0" href="<%=ConfigurationManager.AppSettings["url_redirect"] %>Auth/LogOut.aspx">
                                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/log-out.svg" class="me-2" alt="img" />
                                Salir
                            </a>
                        </div>
                    </div>
                </li>
            </ul>

            <div class="dropdown mobile-user-menu">
                <a href="#!" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-ellipsis-v"></i>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <%--<a class="dropdown-item" href="profile.html">Mi Perfil</a>
                    <a class="dropdown-item" href="general-settings.html">Settings</a>
                    <a class="dropdown-item" href="signin.html">Salir</a>--%>
                </div>
            </div>
        </div>

        <asp:PlaceHolder ID="menu_place_holder" runat="server"></asp:PlaceHolder>

        <div class="page-wrapper cardhead">
            <div class="content container-fluid">
                <form id="frm_page" runat="server">
                    <asp:ContentPlaceHolder ID="cph_body" runat="server">
                    </asp:ContentPlaceHolder>
                </form>
            </div>
        </div>

    </div>

    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/script.js"></script>

    <script type="text/javascript">
        //$(document).ready(function () {
        //    var currentUrl = window.location.pathname;

        //    console.log('link page:', currentUrl);

        //    var menuLinks = $('#sidebar-menu a'); // Seleccionar todos los enlaces dentro del menú
        //    if (currentUrl !== '/') {
        //        menuLinks.each(function () {
        //            var $link = $(this); // Convertir el enlace en objeto jQuery
        //            console.log('$link:', $link[0]);
        //            console.log('href:', $link.attr('href'));
        //            console.log('class:', $link.attr('class'));

        //            var contains = $link.hasClass('subdrop'); // Verificar si tiene la clase 'subdrop'
        //            console.log('contains', contains);

        //            if ($link.attr('class')) {
        //                // Comparar el pathname y asegurarse de que no tenga la clase 'subdrop'
        //                if ($link[0].pathname === currentUrl && !$link.hasClass('subdrop')) {
        //                    $link.addClass('active');
        //                    console.log('Procede!!!');

        //                    // Subir por el DOM para agregar 'active' y 'subdrop' a todos los padres
        //                    var $parent = $link.closest('li.submenu');
        //                    while ($parent.length) {
        //                        $parent.children('a').addClass('active subdrop'); // Agregar las clases a los elementos <a> del padre
        //                        $parent = $parent.parent().closest('li.submenu'); // Subir al siguiente nivel
        //                    }
        //                }
        //            }
        //        });
        //    }
        //});


        const __fnChangeSucursal = (element, pIdSucursal, pNombreSucursal) => {
            let idSucursal = $("#x-selected-store-name").data("id");

            if (idSucursal != pIdSucursal) {
                Swal.fire({
                    title: "¿Desea cambiar de sucursal?",
                    imageUrl: "<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/change-store.png",
                    imageHeight: 80,
                    showCancelButton: true,
                    confirmButtonText: "Aceptar",
                    cancelButtonText: `Cancelar`
                }).then((result) => {
                    if (result.isConfirmed) {
                        let nomSucursal = mtdValue(pNombreSucursal);

                        var fmAuth = new FormData();
                        fmAuth.append("mth", mtdEnc("change/sucursal"));
                        fmAuth.append("pIdSucursal", pIdSucursal);

                        $.ajax({
                            url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/master.ashx",//PENDIENTE
                            type: "post",
                            contentType: false,
                            processData: false,
                            ContentType: "text/html;charset=utf-8",
                            dataType: "json",
                            data: fmAuth,
                            async: true,
                            beforeSend: function () {
                                __Progress(`Actualizando Sucursal<br/><br/>"${nomSucursal}"`);
                            },
                            success: function (result, arg) {
                                if (result) {
                                    if (result.type == "success") {
                                        setTimeout(function () {
                                            $("#x-selected-store-name").html(nomSucursal);
                                            $("#x-selected-store-name").data("id", pIdSucursal);

                                            $(element).closest(".dropdown-menu").find("a.dropdown-item").removeClass("active");
                                            $(element).addClass("active");

                                            __ProgressOff();
                                            location.reload();
                                        }, 800);
                                    } else {
                                        Swal.fire({
                                            icon: result.type,
                                            text: result.text,
                                        });
                                    }
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        text: 'Ocurrió un error durante el proceso',
                                    });
                                }
                            },
                            //error: function (result) {
                            //    Swal.fire({
                            //        icon: 'error',
                            //        text: 'Ocurrió un error durante el proceso',
                            //    });
                            //},
                            complete: function () {
                                __ProgressOff();
                            }
                        });
                    }
                });
            }
        };
    </script>
</body>
</html>

