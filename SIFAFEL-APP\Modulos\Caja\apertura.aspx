﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="apertura.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Caja.apertura" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <div class="card-header">
                    <h5 class="card-title"><i class="fas fa-filter"></i>&nbsp;Filtros</h5>
                </div>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item">Módulo de caja</li>
                    <li class="breadcrumb-item active">Apertura</li>
                </ul>
            </div>
        </div>
        <ul class="table-top-head">
            <li>
                <a data-bs-toggle="tooltip" data-bs-placement="top" title="Collapse" id="collapse-header">
                    <i data-feather="chevron-up" class="feather-chevron-up"></i>
                </a>
            </li>
        </ul>
        <div class="page-btn">
            <a href="#!" class="btn btn-sm btn-added">
                <i data-feather="clock"></i>&nbsp;Historial
            </a>
        </div>
        <div class="page-btn import">
            <a href="cierre.aspx" class="btn btn-sm btn-added color">
                <i data-feather="archive"></i>&nbsp;Cierre de caja
            </a>
        </div>
    </div>

    <div class="row apertura">
        <div class="col-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><i class="fas fa-cash-register"></i>&nbsp;Caja</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <label for="ddlCaja" class="form-label">Caja:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i data-feather="airplay"></i>
                                </span>
                                <select id="ddlCaja" name="ddlCaja" class="form-select" <%= lstCajas?.Count > 1 ? "" : "disabled='disabled'" %>>
                                    <% foreach (var caja in lstCajas)
                                        { %>
                                    <option value="<%= caja.id %>"><%= caja.nombre %></option>
                                    <% } %>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="ddlMoneda" class="form-label">Moneda:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i class="fas fa-coins"></i>
                                </span>
                                <select id="ddlMoneda" name="ddlMoneda" class="form-select" <%= lstMonedas.Count > 1 ? "" : "disabled='disabled'" %>>
                                    <% foreach (var caja in lstMonedas)
                                        { %>
                                    <option value="<%= caja.id %>"><%= caja.codigo_iso %></option>
                                    <% } %>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="txtFecha" class="form-label">Fecha:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i data-feather="calendar"></i>
                                </span>
                                <input id="txtFecha" name="txtFecha" class="form-control input-sm" type="date" />
                            </div>
                        </div>
                        <div class="col-md-12 mt-3 d-grid">
                            <button id="btn_consulta_caja" type="button" class="btn btn-block btn-primary">
                                <i data-feather="filter" class="filter-icon"></i>&nbsp;Filtrar
                            </button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
        <div class="col-9">

            <div id="x-div-medios" class="card">
                <div class="card-header">
                    <h5 class="card-title"><i class="fas fa-coins"></i>&nbsp;Medios de pago</h5>
                </div>
                <div class="card-body">

                    <div class="table-responsive">
                        <table id="tbl_medio_pago">
                            <thead>
                                <tr>
                                    <th>Medio</th>
                                    <th>Saldo inicial</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <td class="amount total">Total</td>
                                    <td class="amount total">
                                        <div class="input-group input-group-sm">
                                            <span id="lbl_moneda_total" class="input-group-text"></span>
                                            <input id="lbl_monto_total" class="form-control amount" disabled="disabled" type="number" value="" />
                                        </div>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div id="x-msg-modulo" class="alert alert-warning" role="alert">
                        <h4 class="alert-heading"></h4>
                        <p class="alert-message"></p>
                    </div>

                    <div class="d-flex justify-content-center mt-2">
                        <button id="btn_apertura_caja" type="button" class="btn btn-block btn-primary">
                            <i data-feather="check"></i>Aperturar Caja
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script type="text/javascript">
        var tbl_medio_pago;

        $(document).ready(function () {
            feather.replace();

            $("#x-msg-modulo").hide();
            $("#x-div-medios").hide();

            __setMaxDateToToday("#txtFecha", 5, true);
            $('#txtFecha').val(new Date().toDateInputValue());

            tbl_medio_pago = $('#tbl_medio_pago').DataTable({
                "bFilter": false,
                "ordering": false,
                "bLengthChange": false,
                "bInfo": false,
                "paging": false,
                "language": {
                    search: ' ',
                    searchPlaceholder: "Search",
                    info: " ",
                    paginate: {
                        next: ' <i class=" fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                "columns": [
                    {
                        "data": "descripcion",
                        "render": function (data, type, row) {
                            let iconSrc = '';
                            if (row.url_logo) {
                                iconSrc = _url_cdn + row.url_logo;
                            } else {
                                iconSrc = 'https://via.placeholder.com/50'
                            }
                            return `<img class="medio-icon" src="${iconSrc}" alt="">&nbsp;${data}`;
                        }
                    },
                    {
                        "data": "monto_apertura",
                        "render": function (data, type, row) {
                            let monto = parseFloat(data) || 0;
                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" value="${monto.toFixed(2)}" />
                                    </div>`;
                        }
                    }
                ],
                createdRow: function (row, data, dataIndex) {
                    $(row).find("input.amount").on("input", function () {
                        data.monto_apertura = parseFloat($(this).val()) || 0;
                        var table_data = tbl_medio_pago.rows().data();
                        let total_monto = 0;

                        table_data.each(function (row) {
                            total_monto += parseFloat(row.monto_apertura) || 0;
                        });

                        $('#lbl_moneda_total').text(data.moneda);
                        $('#lbl_monto_total').val(total_monto.toFixed(2));
                    });
                }
            });

            $('#btn_consulta_caja').on('click', function () {
                $("#x-msg-modulo").hide();

                if (!$('#txtFecha').val()) { Swal.fire('Campo requerido', 'Selecciona una fecha.', 'warning'); return; }
                if (!$('#ddlCaja').val()) { Swal.fire('Campo requerido', 'Selecciona una caja.', 'warning'); return; }
                if (!$('#ddlMoneda').val()) { Swal.fire('Campo requerido', 'Selecciona una moneda.', 'warning'); return; }

                __Progress(`Consultando...`);

                setTimeout(function () {
                    var jsonData = JSON.stringify({
                        fecha: __Format($('#txtFecha').val()),
                        id_caja: $('#ddlCaja').val() ? parseInt($('#ddlCaja').val()) : null,
                        id_moneda: $('#ddlMoneda').val() ? parseInt($('#ddlMoneda').val()) : null,
                        tipo: "APE"
                    });

                    var fmAuth = new FormData();
                    fmAuth.append("mth", mtdEnc("get/medio/pago"));
                    fmAuth.append("data", mtdEnc(jsonData));

                    $.ajax({
                        url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmAuth,
                        async: true,
                        beforeSend: function () {
                            tbl_medio_pago.clear();
                            tbl_medio_pago.draw();
                            $('#tbl_medio_pago').hide();

                            $("#x-div-medios").hide();
                            $("#btn_apertura_caja").hide();
                        },
                        success: function (response) {
                            if (response) {

                                const alertType = response.type === "error" ? "danger" : response.type;
                                $("#x-msg-modulo").removeAttr('class').addClass(`alert alert-${alertType}`).show();
                                $("#x-msg-modulo").find(".alert-heading").html(response.title);
                                $("#x-msg-modulo").find(".alert-message").html(response.text);

                                $("#x-div-medios").show();

                                if (response.data_tables.length > 0) {
                                    $('#tbl_medio_pago').show();
                                    tbl_medio_pago.rows.add(response.data_tables[0]);
                                    tbl_medio_pago.draw();

                                    setTimeout(function () {
                                        var firstInput = $('#tbl_medio_pago').find('input.amount').first();
                                        firstInput.trigger('input');
                                    }, 50);

                                    if (response.type === "success") {
                                        $("#btn_apertura_caja").show();
                                    } else {
                                        $("#btn_apertura_caja").hide();

                                        tbl_medio_pago.rows().every(function () {
                                            $(this.node()).find('input').prop('disabled', true);
                                        });
                                    }
                                }
                            }
                        },
                        complete: function () {
                            __ProgressOff();
                        }
                    });
                }, 50);
            });

            $("#btn_apertura_caja").on("click", function () {
                if (!$('#txtFecha').val()) { Swal.fire('Campo requerido', 'Selecciona una fecha.', 'warning'); return; }
                if (!$('#ddlCaja').val()) { Swal.fire('Campo requerido', 'Selecciona una caja.', 'warning'); return; }
                if (!$('#ddlMoneda').val()) { Swal.fire('Campo requerido', 'Selecciona una moneda.', 'warning'); return; }

                var tableData = tbl_medio_pago.rows().data();
                var recalculatedTotal = 0;
                var userEnteredTotal = parseFloat($('#lbl_monto_total').val()) || 0;

                tableData.each(function (row) { recalculatedTotal += parseFloat(row.monto_apertura) || 0; });

                if (userEnteredTotal !== recalculatedTotal) {
                    Swal.fire('Advertencia', 'El total ingresado manualmente no coincide con la suma de los medios de pago. Se usará el total calculado.', 'warning');
                } else {
                    if (recalculatedTotal > 0) {
                        __Aperturar();
                    } else {
                        Swal.fire({
                            title: 'Advertencia',
                            text: 'Desea abrir la caja con saldo cero?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: 'Sí, abrir',
                            cancelButtonText: 'No, cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                __Aperturar();
                            } else if (result.dismiss === Swal.DismissReason.cancel) {
                                Swal.close();
                            }
                        });
                    }
                }
            });

        });

        function __Aperturar() {
            var mediosPago = [];
            var tableData = tbl_medio_pago.rows().data();
            var recalculatedTotal = 0;

            tableData.each(function (row) {
                mediosPago.push({
                    id_medio_pago: row.id_medio_pago,
                    monto: parseFloat(row.monto_apertura) || 0
                });
                recalculatedTotal += parseFloat(row.monto_apertura) || 0;
            });

            var jsonData = {
                fecha: __Format($('#txtFecha').val()),
                id_caja: $('#ddlCaja').val() ? parseInt($('#ddlCaja').val()) : null,
                id_moneda: $('#ddlMoneda').val() ? parseInt($('#ddlMoneda').val()) : null,
                tipo: "APE",
                total: parseFloat(recalculatedTotal.toFixed(2)),
                medios_pago: mediosPago
            };

            __Progress(`Aperturando...`);

            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("realizar/operacion"));
                fmAuth.append("data", mtdEnc(JSON.stringify(jsonData)));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {

                    },
                    success: function (response) {
                        if (response) {
                            Swal.fire(response.title || '', response.text || '', response.type || '');

                            if (response.type == "success") {
                                setTimeout(function () {
                                    $('#btn_consulta_caja').click();
                                }, 50);
                            }
                        }
                    },
                    complete: function () {
                        __ProgressOff();
                    }
                });
            }, 50);
        }
    </script>
</asp:Content>
