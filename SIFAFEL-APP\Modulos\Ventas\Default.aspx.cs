﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static SIFAFEL_CORE.Core.Utility;
using static SIFAFEL_MODEL.Data_Model_API.Autenticacion.InfoUsuario.InfoContribuyente;

namespace SIFAFEL_APP.Modulos.Ventas
{
    public partial class Default : System.Web.UI.Page
    {
        /// <summary>
        /// Listados de cajas por sucursal
        /// </summary>
        public List<InfoCaja> lstCajas = new List<InfoCaja>();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                SessionManager sessionManager = new SessionManager();

                if (sessionManager.Valid())
                {
                    lstCajas = sessionManager.GetCajas();
                }

            }
        }
    }
}