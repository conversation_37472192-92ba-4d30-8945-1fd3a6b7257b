<%@ Page Title="Nueva Compra" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="NuevaCompra.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.NuevaCompra" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
    <meta charset="utf-8">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />

    <%-- MODAL PROVEEDORES --%>
    <div class="modal fade effect-scale" id="modalProveedores">
        <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="truck" class="feather-16"></i>&nbsp;Consultas de proveedores</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">

                    <table id="grvProveedores" class="table table-bordered table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>Código</th>
                                <th>NIT</th>
                                <th>Proveedor</th>
                                <th>Teléfono</th>
                                <th>Correo</th>
                                <th>Direccion</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>

                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL CREAR PROVEEDOR --%>
    <div class="modal fade effect-scale" id="modalCrearProveedor">
        <div class="modal-dialog modal-dialog-centered modal-md" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title"><i data-feather="user-plus" class="feather-16"></i>&nbsp;Crear Proveedor</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">
                    <form id="formCrearProveedor">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtNitNuevoProveedor" class="form-label">NIT <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="txtNitNuevoProveedor" name="txtNitNuevoProveedor" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtNombreNuevoProveedor" class="form-label">Nombre <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="txtNombreNuevoProveedor" name="txtNombreNuevoProveedor" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtTelefonoNuevoProveedor" class="form-label">Tel&eacute;fono</label>
                                    <input type="text" class="form-control" id="txtTelefonoNuevoProveedor" name="txtTelefonoNuevoProveedor">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="txtEmailNuevoProveedor" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="txtEmailNuevoProveedor" name="txtEmailNuevoProveedor">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="txtDireccionNuevoProveedor" class="form-label">Direcci&oacute;n</label>
                                    <textarea class="form-control" id="txtDireccionNuevoProveedor" name="txtDireccionNuevoProveedor" rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" id="btnGuardarNuevoProveedor">
                        <i data-feather="save" class="feather-16"></i>&nbsp;Guardar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <%-- MODAL PRODUCTOS --%>
    <div class="modal fade effect-scale" id="modalProductos">
        <div class="modal-dialog modal-fullscreen" role="document">
            <div class="modal-content modal-content-demo">
                <div class="modal-header">
                    <h4 class="modal-title">Productos</h4>
                    <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-start">
                    <table id="grvProductosConsulta" class="table table-bordered table-hover table-striped table-condensed table-sm">
                        <thead class="table-head">
                            <tr>
                                <th>C&oacute;digo</th>
                                <th>Producto</th>
                                <th>Precio unitario</th>
                                <th>Marca</th>
                                <th>Modelo</th>
                                <th>A&ntilde;o</th>
                                <th>Descripci&oacute;n</th>
                                <th>Existencia</th>
                                <th>En canasta</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer d-flex justify-content-end">
                    <button type="button" class="btn btn-cancel btn-sm" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="shopping-bag" class="me-2"></i>Nueva compra</h4>
                <h6>Crear nueva compra</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="Compras.aspx" class="btn btn-added color">
                <i data-feather="inbox" class="me-2"></i>Listado de compras
            </a>
        </div>
    </div>

    <%-- COMPRAS --%>
    <div class="row compras">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <!-- SECCION DE PROVEEDOR -->
                        <div class="col-md-8">
                            <div class="form-horizontal">

                                <!-- NIT PROVEEDOR -->
                                <div class="mb-1 row">
                                    <div class="add-newplus">
                                        <span id="lblIdProveedor" hidden=""></span>
                                        <label for="txtNitProveedor" class="form-label">Proveedor:</label>

                                        <a href="#!" id="btnBuscarProveedor">
                                            <i data-feather="search" class="plus-down-add"></i>
                                            <span>Buscar</span>
                                        </a>
                                    </div>
                                    <div class="col-12">
                                        <div class="row g-0">
                                            <div class="col-4 input-h-control">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="truck" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtNitProveedor" name="txtNitProveedor" type="text" aria-label="NIT" placeholder="NIT" class="form-control no-right-border">
                                                </div>
                                            </div>
                                            <div class="col-8">
                                                <input id="txtNombreProveedor" type="text" aria-label="Nombre completo" placeholder="Nombre completo" class="form-control no-left-border input-h-control" disabled="disabled">
                                            </div>
                                        </div>
                                    </div>
                                </div>



                            </div>
                        </div>

                        <!-- SECCION DE COMPRA -->
                        <div class="col-md-4">
                            <div class="form-horizontal">

                                <!-- FECHA DE COMPRA -->
                                <div class="mb-1 row">
                                    <div class="col-sm-12">
                                        <label class="form-label">Fecha:</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">
                                                <i data-feather="calendar" class="feather-16"></i>
                                            </span>
                                            <input class="form-control" id="txtFechaCompra" name="txtFechaCompra" type="date" placeholder="Fecha Compra" />
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row segunda-fila">
                        <!-- SEGUNDA FILA RESPONSIVE -->
                        <div class="col-12">
                            <div class="form-horizontal">
                                <div class="mb-2 row">
                                    <div class="col-12">
                                        <div class="row g-2">
                                            <!-- Código de compra -->
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="file-text" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtCodigoCompra" name="txtCodigoCompra" type="text" aria-label="Codigo" placeholder="C&oacute;digo compra" class="form-control">
                                                </div>
                                            </div>
                                            <!-- Póliza/DUCA -->
                                            <div class="col-12 col-sm-6 col-md-4">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="shield" class="feather-16"></i>
                                                    </span>
                                                    <input id="txtPolizaDuca" name="txtPolizaDuca" type="text" aria-label="Poliza/DUCA" placeholder="P&oacute;liza/DUCA" class="form-control">
                                                </div>
                                            </div>
                                            <!-- Forma de pago -->
                                            <div class="col-12 col-sm-12 col-md-4">
                                                <div class="input-group input-group-sm">
                                                    <span class="input-group-text">
                                                        <i data-feather="credit-card" class="feather-16"></i>
                                                    </span>
                                                    <select id="ddlFormaPago" name="ddlFormaPago" class="form-select">
                                                        <option value="">Forma de pago</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <span class="card-title" style="float: left; width: auto !important;">
                            <i data-feather="package" class="feather-16"></i>&nbsp;Productos
                        </span>

                        <%-- CODIGO DE BARRA --%>
                        <div class="col-lg-4 col-sm-12 ms-auto">
                            <div class="add-newplus">
                                <label class="form-label" for="txtCodigoBarraProducto">&nbsp;</label>
                                <a href="#!" id="btnBuscarProducto">
                                    <i data-feather="search" class="plus-down-add"></i>
                                    <span>Consultar</span>
                                </a>
                            </div>
                            <div class="input-blocks">
                                <div class="input-groupicon select-code">
                                    <input id="txtCodigoBarraProducto" name="txtCodigoBarraProducto" class="barcode-search" type="text" placeholder="C&oacute;digo de producto" style="padding: 10px;">
                                    <div class="addonset">
                                        <img src="<%=ConfigurationManager.AppSettings["url_cdn"]%>img/barcode-scanner.gif" alt="img" style="height: 38px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <%-- PRODUCTOS COMPRA --%>
                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="grvProductosCompra" class="table table-striped table-bordered table-sm">
                                <thead class="table-head">
                                    <tr>
                                        <th>Id</th>
                                        <th>C&oacute;digo</th>
                                        <th>Producto</th>
                                        <th>Cantidad</th>
                                        <th>Existencia</th>
                                        <th>Costo U.</th>
                                        <th>Costo Total</th>
                                        <th>Precio Venta</th>
                                        <th>Precio M&iacute;nimo</th>
                                        <th>Eliminar</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-sm-12 col-md-6 col-lg-4 ms-md-auto mt-2">
                            <div class="total-order w-100 max-widthauto m-auto mb-1">
                                <ul>
                                    <li>
                                        <h4>SubTotal</h4>
                                        <h5><span class="lbl-info-moneda">Q.</span>&nbsp;<span class="lbl-info-subtotal">0.00</span></h5>
                                    </li>
                                    <li>
                                        <h4>Descuento</h4>
                                        <h5><span class="lbl-info-moneda">Q.</span>&nbsp;<span class="lbl-info-descuento">0.00</span></h5>
                                    </li>
                                    <li class="total">
                                        <h4>Total a pagar</h4>
                                        <h4><span class="lbl-info-moneda">Q.</span>&nbsp;<span class="lbl-info-total">0.00</span></h4>
                                    </li>
                                </ul>
                            </div>



                            <div class="btn-row d-sm-flex align-items-center justify-content-between mb-4">
                                <a id="btnGuardarCompraFinal" class="btn btn-success btn-icon flex-fill">
                                    <span class="me-1 d-flex align-items-center">
                                        <i data-feather="save" class="feather-16"></i>
                                    </span>
                                    Guardar Compra
                                </a>
                                <a id="btnCancelarCompra" class="btn btn-danger btn-icon flex-fill">
                                    <span class="me-1 d-flex align-items-center">
                                        <i data-feather="trash-2" class="feather-16"></i>
                                    </span>
                                    Cancelar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <style>
        /* Estilos para inputs sin bordes */
        .no-left-border {
            border-left: none !important;
            border-radius: 0 !important;
        }

        .no-right-border {
            border-right: none !important;
            border-radius: 0 !important;
        }

        .input-h-control {
            height: 38px;
        }

        /* Estilos para DataTable */
        #grvProductosCompra td {
            vertical-align: middle !important;
            padding: 8px 6px !important;
        }

        #grvProductosCompra .form-control {
            height: 28px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            border: 1px solid #ced4da !important;
            border-radius: 4px !important;
        }

        #grvProductosCompra .btn-sm {
            padding: 4px 8px !important;
            font-size: 12px !important;
        }

        .productimgname img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product img {
            width: 32px !important;
            height: 32px !important;
            object-fit: cover;
            border-radius: 4px;
        }

        .view-product {
            display: inline-block;
            vertical-align: middle;
        }

        .view-info-product {
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
            font-size: 13px;
            text-decoration: none;
            color: #495057;
        }

        .view-info-product:hover {
            color: #007bff;
            text-decoration: none;
        }

        /* Estilos responsive para la segunda fila */
        @media (max-width: 576px) {
            /* En móviles: cada campo ocupa toda la fila */
            .segunda-fila .col-12 {
                margin-bottom: 0.5rem;
            }

            .segunda-fila .input-group-sm .form-control,
            .segunda-fila .input-group-sm .form-select {
                font-size: 0.875rem;
                padding: 0.375rem 0.75rem;
            }
        }

        @media (min-width: 577px) and (max-width: 768px) {
            /* En tablets: dos campos por fila, el tercero abajo */
            .segunda-fila .col-sm-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }

            .segunda-fila .col-sm-12 {
                flex: 0 0 100%;
                max-width: 100%;
                margin-top: 0.5rem;
            }
        }

        @media (min-width: 769px) {
            /* En desktop: tres campos en una fila */
            .segunda-fila .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }
        }

        /* Espaciado consistente */
        .segunda-fila .input-group {
            margin-bottom: 0;
        }

        .segunda-fila .row.g-2 {
            --bs-gutter-x: 0.5rem;
            --bs-gutter-y: 0.5rem;
        }

        /* Asegurar que los iconos se vean bien en todos los tamaños */
        .segunda-fila .input-group-text {
            min-width: 2.5rem;
            justify-content: center;
        }

        .segunda-fila .feather-16 {
            width: 16px;
            height: 16px;
        }

        /* Estilos para la tabla de productos */
        #grvProductosCompra td {
            white-space: normal !important;
            word-wrap: break-word !important;
            vertical-align: top !important;
            padding: 8px !important;
        }

        #grvProductosCompra .text-wrap {
            white-space: normal !important;
            word-wrap: break-word !important;
            max-width: 300px;
        }

        #grvProductosCompra .view-info-product {
            display: block;
            word-wrap: break-word;
            white-space: normal;
            line-height: 1.4;
        }

        /* Botón eliminar */
        .eliminar-producto {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* Estilos para cantidad (copiados de nueva.aspx) */
        .product-quantity {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .quantity-btn {
            cursor: pointer;
            padding: 2px 5px;
            user-select: none;
        }

        .quntity-input {
            width: 50px;
            text-align: center;
            border: none;
            background: transparent;
            margin: 0 5px;
        }

        /* Evitar expansión vertical de filas */
        #grvProductosCompra td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
            vertical-align: middle;
        }

        /* Controlar altura de filas */
        #grvProductosCompra tbody tr {
            height: 50px;
        }

        /* Evitar responsive collapse */
        #grvProductosCompra.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
        #grvProductosCompra.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
            display: none;
        }

        /* Validación de inputs */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        /* Contenedor de producto con imagen */
        .product-info-container {
            display: flex;
            align-items: center;
            gap: 8px;
            min-height: 40px;
            max-height: 40px;
            overflow: hidden;
        }

        .product-image-container {
            flex-shrink: 0;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-image {
            max-width: 35px;
            max-height: 35px;
            object-fit: cover;
            border-radius: 4px;
        }

        .product-text-container {
            flex: 1;
            overflow: hidden;
            min-width: 0;
        }

        .product-name {
            font-weight: 500;
            color: #333;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }

        /* Botones de flechitas para precios */
        .btn-precio-up, .btn-precio-down, .btn-precio-min-up, .btn-precio-min-down {
            padding: 2px 6px;
            font-size: 10px;
            line-height: 1;
            border: 1px solid #ced4da;
            background-color: #f8f9fa;
        }

        .btn-precio-up:hover, .btn-precio-down:hover,
        .btn-precio-min-up:hover, .btn-precio-min-down:hover {
            background-color: #e9ecef;
        }

        .input-group-append {
            display: flex;
            flex-direction: column;
        }

        .input-group-append .btn {
            border-radius: 0;
            border-left: none;
        }

        .input-group-append .btn:first-child {
            border-top-right-radius: 0.25rem;
        }

        .input-group-append .btn:last-child {
            border-bottom-right-radius: 0.25rem;
        }
    </style>

    <script type="text/javascript">
        // Variables globales
        var tbl_productos_compra;
        var tbl_proveedores;
        var productosCompra = [];

        $(document).ready(function () {
            // Inicializar fecha actual
            $('#txtFechaCompra').val(new Date().toISOString().split('T')[0]);

            // Inicializar DataTable de productos
            inicializarDataTableProductos();

            // Inicializar DataTable de proveedores
            inicializarDataTableProveedores();

            // Inicializar DataTable de productos del modal
            inicializarDataTableProductosModal();

            // Cargar formas de pago
            cargarFormasPago();



            // Eventos
            $('#btnBuscarProveedor').on('click', function() {
                cargarProveedores();
            });

            $('#btnBuscarProducto').on('click', function() {
                cargarProductosModal();
            });

            // Buscar producto por código igual que en nueva.aspx
            $("#txtCodigoBarraProducto").on("click keydown", function (e) {
                if (e.type === "click" || (e.type === "keydown" && (e.which === 13 || e.which === 9))) {
                    e.preventDefault();
                    if ($("#txtCodigoBarraProducto").val().trim() !== "") {
                        fnValidaCodigoBarra();
                    }
                }
            });

            $("#txtCodigoBarraProducto").on("keydown", function (event) {
                if (event.key === "F1") {
                    event.preventDefault();
                    $("#btnBuscarProducto").click();
                }
            });

            $('#btnGuardarCompraFinal').on('click', function() {
                guardarCompra();
            });

            // Evento para guardar nuevo proveedor
            $('#btnGuardarNuevoProveedor').on('click', function() {
                guardarNuevoProveedor();
            });

            // Evento para limpiar formulario al cerrar modal
            $('#modalCrearProveedor').on('hidden.bs.modal', function () {
                $('#formCrearProveedor')[0].reset();
                $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                feather.replace();
            });

            // Evento para el botón Cancelar
            $('#btnCancelarCompra').on('click', function() {
                Swal.fire({
                    title: '\u00BFEst\u00E1 seguro?',
                    text: 'Se perder\u00E1n todos los datos ingresados.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'S\u00ED, cancelar',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        limpiarFormulario();
                        Swal.fire('Cancelado', 'Se han limpiado todos los campos.', 'success');
                    }
                });
            });

            // Buscar proveedor automáticamente igual que en nueva.aspx
            $("#txtNitProveedor").on('keydown', function (e) {
                if (e.which == 13 || e.which == 9) {
                    e.preventDefault();
                    if ($(this).val().trim() != "") {
                        buscarProveedorPorNit($("#txtNitProveedor").val());
                    }
                }
            });

            $("#txtNitProveedor").on("keydown", function (event) {
                if (event.key === "F1") {
                    event.preventDefault();
                    $("#btnBuscarProveedor").click();
                }
            });
        });

        // Función para inicializar DataTable de productos
        function inicializarDataTableProductos() {
            tbl_productos_compra = $('#grvProductosCompra').DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar producto",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    "emptyTable": "No hay productos agregados a la compra",
                    "zeroRecords": "No se encontraron productos",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: false,
                paging: true,
                autoWidth: false,
                searching: false,
                responsive: false,
                scrollX: true,
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [3, 5, 6, 7, 8, 9], // Columnas editables y eliminar
                        orderable: false
                    },
                    {
                        targets: [3], // Columna Cantidad
                        width: "80px"
                    },
                    {
                        targets: [2], // Columna Producto
                        width: "300px",
                        className: "text-wrap"
                    },
                    {
                        targets: [9], // Columna Eliminar
                        width: "60px",
                        className: "text-center"
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            var img_producto;
                            if (!item.img_producto || item.img_producto === '' || item.img_producto === null) {
                                img_producto = '<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';
                            } else {
                                img_producto = item.img_producto;
                            }
                            return '<div class="product-info-container">' +
                                        '<div class="product-image-container">' +
                                            '<img src="' + img_producto + '" alt="" class="product-image" onerror="this.onerror=null;this.src=\'<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png\';">' +
                                        '</div>' +
                                        '<div class="product-text-container">' +
                                            '<span class="product-name">' + item.nombre + '</span>' +
                                        '</div>' +
                                   '</div>';
                        }
                    },
                    // Columna 3: Cantidad (input editable con botones)
                    {
                        data: function (item) {
                            return `<div class="product-quantity">
                                        <span class="quantity-btn"><i data-feather="minus-circle" class="feather-search"></i></span>
                                        <input type="text" class="quntity-input tbl_txt_cantidad" value="1" data-val="true" type="number" min="1" data-previous-value="1">
                                        <span class="quantity-btn">+<i data-feather="plus-circle" class="plus-circle"></i></span>
                                    </div>`;
                        }
                    },
                    // Columna 4: Existencia
                    { data: "stock_actual" },
                    // Columna 5: Costo U. (input editable)
                    {
                        data: function (item) {
                            var costoFormateado = formatearNumero((item.costo_unitario || 0).toFixed(2));
                            return '<input type="text" class="form-control costo-input" value="' + costoFormateado + '" onkeyup="validarCantidadCosto(this)" oninput="validarCantidadCosto(this)" onchange="validarCantidadCosto(this)" onblur="formatearInputCosto(this)" onfocus="limpiarFormatoCosto(this)" />';
                        }
                    },
                    // Columna 6: Costo Total (calculado)
                    {
                        data: function (item) {
                            return '<span class="costo-total">0.00</span>';
                        }
                    },
                    // Columna 7: Precio Venta (input editable con flechitas)
                    {
                        data: function (item) {
                            var precioVenta = item.precio_unitario || item.costo_unitario || 0;
                            var precioFormateado = formatearNumero(precioVenta.toFixed(2));
                            return '<div class="input-group input-group-sm">' +
                                        '<input type="text" class="form-control precio-input" value="' + precioFormateado + '" onblur="formatearInputPrecio(this); validarPreciosManual(this);" onfocus="limpiarFormatoPrecio(this)" />' +
                                        '<div class="input-group-append">' +
                                            '<button class="btn btn-outline-secondary btn-precio-up" type="button">▲</button>' +
                                            '<button class="btn btn-outline-secondary btn-precio-down" type="button">▼</button>' +
                                        '</div>' +
                                   '</div>';
                        }
                    },
                    // Columna 8: Precio mínimo (input editable con flechitas)
                    {
                        data: function (item) {
                            var precioMinimo = item.min_descuento || item.costo_unitario || 0;
                            var precioMinimoFormateado = formatearNumero(precioMinimo.toFixed(2));
                            return '<div class="input-group input-group-sm">' +
                                        '<input type="text" class="form-control precio-min-input" value="' + precioMinimoFormateado + '" onblur="formatearInputPrecioMin(this); validarPreciosManual(this);" onfocus="limpiarFormatoPrecioMin(this)" />' +
                                        '<div class="input-group-append">' +
                                            '<button class="btn btn-outline-secondary btn-precio-min-up" type="button">▲</button>' +
                                            '<button class="btn btn-outline-secondary btn-precio-min-down" type="button">▼</button>' +
                                        '</div>' +
                                   '</div>';
                        }
                    },
                    // Columna 9: Eliminar
                    {
                        data: function (item) {
                            return '<button class="btn btn-danger btn-sm eliminar-producto" type="button" title="Eliminar producto"><i class="fa fa-trash"></i></button>';
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });

                    // Agregar eventos para calcular costo total automáticamente
                    $(row).find('.cantidad-input, .costo-input').on('input', function() {
                        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
                        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
                        var costoTotal = cantidad * costoUnitario;
                        $(row).find('.costo-total').text(costoTotal.toFixed(2));
                    });

                    // Evento para eliminar producto
                    $(row).find('.eliminar-producto').on('click', function() {
                        Swal.fire({
                            title: '\u00BFEliminar producto?',
                            text: '\u00BFEst\u00E1 seguro de que desea eliminar este producto de la compra?',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: 'S\u00ED, eliminar',
                            cancelButtonText: 'Cancelar'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Eliminar la fila de la tabla
                                $('#grvProductosCompra').DataTable().row(row).remove().draw();

                                // Actualizar totales
                                fnUpdateTotales();

                                Swal.fire('Eliminado', 'El producto ha sido eliminado de la compra.', 'success');
                            }
                        });
                    });

                    // Eventos para botones de cantidad (copiado exacto de nueva.aspx)
                    $(row).find(".quantity-btn").off("click").on("click", function () {
                        var $button = $(this);
                        var $inputCantidad = $button.closest('.product-quantity').find("input.quntity-input");

                        var cantidadActual = parseInt($inputCantidad.val()) || 0;
                        var nuevaCantidad = cantidadActual;

                        if ($button.text() === "+") {
                            nuevaCantidad = cantidadActual + 1;
                        } else {
                            if (cantidadActual > 1) {
                                nuevaCantidad = cantidadActual - 1;
                            }
                        }

                        $inputCantidad.val(nuevaCantidad);
                        validarCantidadCosto($inputCantidad[0]);
                    });

                    // Eventos para botones de flechitas de precio venta
                    $(row).find('.btn-precio-up').on('click', function() {
                        var $input = $(row).find('.precio-input');
                        var valorActual = parseFloat(limpiarFormatoNumero($input.val())) || 0;
                        var nuevoValor = valorActual + 0.01;
                        $input.val(formatearNumero(nuevoValor.toFixed(2)));
                        fnUpdateTotales();
                    });

                    $(row).find('.btn-precio-down').on('click', function() {
                        var $input = $(row).find('.precio-input');
                        var $costoInput = $(row).find('.costo-input');
                        var valorActual = parseFloat(limpiarFormatoNumero($input.val())) || 0;
                        var costoMinimo = parseFloat(limpiarFormatoNumero($costoInput.val())) || 0;
                        var nuevoValor = Math.max(valorActual - 0.01, costoMinimo);
                        $input.val(formatearNumero(nuevoValor.toFixed(2)));
                        fnUpdateTotales();
                    });

                    // Eventos para botones de flechitas de precio mínimo
                    $(row).find('.btn-precio-min-up').on('click', function() {
                        var $input = $(row).find('.precio-min-input');
                        var valorActual = parseFloat(limpiarFormatoNumero($input.val())) || 0;
                        var nuevoValor = valorActual + 0.01;
                        $input.val(formatearNumero(nuevoValor.toFixed(2)));
                    });

                    $(row).find('.btn-precio-min-down').on('click', function() {
                        var $input = $(row).find('.precio-min-input');
                        var $costoInput = $(row).find('.costo-input');
                        var valorActual = parseFloat(limpiarFormatoNumero($input.val())) || 0;
                        var costoMinimo = parseFloat(limpiarFormatoNumero($costoInput.val())) || 0;
                        var nuevoValor = Math.max(valorActual - 0.01, costoMinimo);
                        $input.val(formatearNumero(nuevoValor.toFixed(2)));
                    });

                    // Inicializar iconos de Feather en la fila
                    feather.replace();
                }
            });

            // Inicializar iconos de Feather después de crear la tabla
            setTimeout(function() {
                feather.replace();
            }, 100);
        }

        // Función para inicializar DataTable de proveedores
        function inicializarDataTableProveedores() {
            tbl_proveedores = $('#grvProveedores').DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar proveedor",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: false,
                paging: true,
                autoWidth: false,
                columnDefs: [
                    {
                        targets: [0, 3, 4],
                        visible: false
                    }
                ],
                columns: [
                    { data: "id_proveedor" },
                    { data: "nit" },
                    { data: "nombres" },
                    { data: "telefono" },
                    {
                        data: function(item) {
                            return item.email || item.correo || '';
                        }
                    },
                    { data: "direccion" },
                    {
                        data: function (item) {
                            return `<td>
                                        <div class="hstack gap-2 fs-15">
                                            <a href="#!" class="btn btn-icon btn-sm btn-soft-success rounded-pill add-proveedor"><i class="fas fa-user-check"></i></a>
                                        </div>
                                    </td>`;
                        }
                    }
                ],
                createdRow: function (row, data, dataIndex) {
                    let $row = $(row);

                    // Evento de clic en el botón para agregar proveedor
                    $row.find(".add-proveedor").on("click", function () {
                        agregarProveedor($(this), data);
                    });

                    // Evento de doble clic en la fila
                    $row.on("dblclick", function () {
                        agregarProveedor($(this), data);
                    });
                }
            });
        }

        // Función para cargar proveedores
        function cargarProveedores() {
            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/Inventario.ashx',
                type: 'POST',
                data: function() {
                    var fmAuth = new FormData();
                    fmAuth.append("mth", mtdEnc("get/proveedores"));
                    fmAuth.append("data", mtdEnc(JSON.stringify({})));
                    return fmAuth;
                }(),
                contentType: false,
                processData: false,
                beforeSend: function () {
                    // Mostrar loading
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#grvProveedores').dataTable().fnClearTable();
                        $('#grvProveedores').DataTable().search("").draw();
                        $('#grvProveedores').dataTable().fnAddData(result.data);

                        $("#modalProveedores").modal("show");
                    }
                    else if (result.type == "warning") {
                        Swal.fire({ icon: result.type, text: result.text });
                    }
                    else {
                        Swal.fire({ icon: result.type, text: result.text });
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurri\u00F3 un error al cargar los proveedores.', 'error');
                }
            });
        }

        // Función para inicializar DataTable de productos del modal
        function inicializarDataTableProductosModal() {
            $("#grvProductosConsulta").DataTable({
                "language": {
                    "lengthMenu": "_MENU_",
                    "sSearch": "",
                    "searchPlaceholder": "Buscar producto",
                    "sLoadingRecords": "Cargando registros...",
                    "info": " ",
                    paginate: {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: true,
                paging: true,
                autoWidth: true,
                columnDefs: [
                    {
                        targets: 1,
                        className: 'productimgname'
                    },
                    {
                        targets: [2, 3, 4, 5, 6, 7, 8, 9],
                        className: 'text-center'
                    },
                    {
                        targets: [3],
                        className: 'text-right'
                    },
                    {
                        targets: [4, 5, 6],
                        visible: false
                    }
                ],
                columns: [
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    {
                        data: function (item) {
                            return `<span class="currency">${item.moneda || 'Q'}</span>
                                    <span class="price">${(item.precio_unitario || 0).toFixed(2)}</span>`;
                        }
                    },
                    { data: "marca" },
                    { data: "modelo" },
                    { data: "anio" },
                    { data: "descripcion" },
                    { data: "stock_actual" },
                    {
                        data: function (item) {
                            return `<span class="tbl_lbl_canasta"></span>`;
                        }
                    },
                    {
                        data: function (item) {
                            if ((item.stock_actual || 0) > 0) {
                                return '<button class="btn btn-success btn-sm add-product" type="button" role="button" style="cursor: pointer;" title="Agregar producto"><span class="fa fa-plus"></span></button>';
                            } else {
                                return "";
                            }
                        }
                    },
                ],
                rowCallback: function (row, data) {
                    let info_producto = fnBuscarProductoCompra(data.id_producto);
                    let cantidad_canasta = info_producto.producto ? info_producto.producto.cantidad : 0;

                    data.cantidad_canasta = cantidad_canasta;
                    $(row).find(".tbl_lbl_canasta").html(data.cantidad_canasta);

                    if (data.stock_actual < 1 || data.cantidad_canasta >= data.stock_actual) {
                        $(row).addClass("no_stock").find(".add-product").hide();
                    }

                    $(row).find(".add-product").on("click", function () {
                        let info_producto = fnBuscarProductoCompra(data.id_producto);
                        let cantidad_canasta = info_producto.producto ? info_producto.producto.cantidad : 0;

                        if (cantidad_canasta > 0 && data.cantidad_canasta >= data.stock_actual) {
                            console.error("No hay suficiente stock.");
                            return;
                        }

                        cantidad_canasta++;
                        let $btnAddProduct = $(this);
                        $btnAddProduct.html('<span class="fas fa-spinner fa-spin"></span>');
                        fnAgregarProductoCompra(data);

                        setTimeout(function () {
                            $btnAddProduct.html('<span class="fa fa-plus"></span>');
                        }, 200);
                    });

                    $(row).find(".view-info-product").on("click", function () {
                        console.log("Ver info producto:", data);
                    });
                }
            });
        }



        // Función para cargar productos del modal
        function cargarProductosModal() {
            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx?mth=' + mtdEnc("get/products"),
                data: null,
                type: "GET",
                contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                dataType: "json",
                beforeSend: function () {
                    // Mostrar loading
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#grvProductosConsulta').dataTable().fnClearTable();
                        $('#grvProductosConsulta').DataTable().search("").draw();
                        $('#grvProductosConsulta').dataTable().fnAddData(result.data);

                        $("#modalProductos").modal("show");
                    }
                    else {
                        Swal.fire({ icon: result.type, text: result.text });
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurri\u00F3 un error al cargar los productos.', 'error');
                }
            });
        }

        function fnBuscarProductoCompra(p_id_producto, p_tipo_busqueda = "id") {
            const table = $('#grvProductosCompra').DataTable().rows().data().toArray();
            const index = table.findIndex(row => {
                if (p_tipo_busqueda === "id") {
                    return row.id_producto === p_id_producto;
                } else if (p_tipo_busqueda === "codigo") {
                    return row.codigo === p_id_producto;
                }
                return false;
            });

            return {
                producto: index !== -1 ? table[index] : null,
                index: index
            };
        }

        function fnAgregarProductoCompra(p_producto, p_cantidad = 1) {
            const table = $('#grvProductosCompra').DataTable();
            const info_producto = fnBuscarProductoCompra(p_producto.id_producto);
            const producto = info_producto.producto;
            const stock_actual = p_producto.stock_actual ?? p_producto.existencia;
            let _stock_insuficiente = false;

            if (producto) {
                // Producto ya existe, actualizar cantidad
                const nueva_cantidad = producto.cantidad + p_cantidad;
                if (nueva_cantidad > stock_actual) {
                    _stock_insuficiente = true;
                } else {
                    producto.cantidad = nueva_cantidad;
                    table.row(info_producto.index).data(producto).draw(false);
                }
            } else {
                // Producto nuevo
                if (p_cantidad > stock_actual) {
                    _stock_insuficiente = true;
                } else {
                    const nuevo_producto = {
                        id_producto: p_producto.id_producto,
                        codigo: p_producto.codigo,
                        nombre: p_producto.nombre,
                        precio_unitario: p_producto.precio_unitario || 0,
                        cantidad: p_cantidad,
                        stock_actual: stock_actual,
                        costo_unitario: p_producto.costo_unitario || 0,
                        min_descuento: p_producto.min_descuento || 0,
                        img_producto: p_producto.img_producto
                    };
                    table.row.add(nuevo_producto).draw(false);
                }
            }

            if (_stock_insuficiente) {
                Swal.fire('Stock insuficiente', 'No hay suficiente stock para agregar este producto.', 'warning');
                return;
            }

            // Actualizar totales
            fnUpdateTotales();

            setTimeout(() => {
                feather.replace();
                table.columns.adjust().draw();
            }, 50);
        }

        // Función para formatear números con comas
        function formatearNumero(numero) {
            return numero.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // Función para limpiar formato de número (quitar comas)
        function limpiarFormatoNumero(numeroFormateado) {
            return numeroFormateado.toString().replace(/,/g, "");
        }

        // Funciones para formatear inputs de costo
        function formatearInputCosto(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoCosto(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        // Funciones para formatear inputs de precio
        function formatearInputPrecio(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoPrecio(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        // Funciones para formatear inputs de precio mínimo
        function formatearInputPrecioMin(input) {
            var valor = limpiarFormatoNumero(input.value);
            if (!isNaN(valor) && valor !== '') {
                input.value = formatearNumero(parseFloat(valor).toFixed(2));
            }
        }

        function limpiarFormatoPrecioMin(input) {
            input.value = limpiarFormatoNumero(input.value);
        }

        function fnUpdateTotales() {
            let subtotal = 0;
            let descuento = 0;

            // Calcular desde los inputs de la tabla
            $('#grvProductosCompra tbody tr').each(function() {
                var $row = $(this);
                var cantidad = parseFloat($row.find('.quntity-input').val()) || 0;
                var precioTexto = $row.find('.precio-input').val();
                var precio = parseFloat(limpiarFormatoNumero(precioTexto)) || 0;
                subtotal += cantidad * precio;
            });

            const total = subtotal - descuento;

            // Formatear números con comas para miles
            $('.lbl-info-subtotal').text(formatearNumero(subtotal.toFixed(2)));
            $('.lbl-info-descuento').text(formatearNumero(descuento.toFixed(2)));
            $('.lbl-info-total').text(formatearNumero(total.toFixed(2)));
        }

        function fnValidaCodigoBarra() {
            let codigo_producto = $("#txtCodigoBarraProducto").val().trim();
            let cantidad = 1;

            if (codigo_producto.includes('*')) {
                let partes = codigo_producto.split('*');
                if (partes.length === 2 && !isNaN(partes[0]) && !isNaN(partes[1])) {
                    cantidad = parseInt(partes[0]);
                    codigo_producto = partes[1];
                }
            }

            if (codigo_producto) {
                let info_producto = fnBuscarProductoCompra(codigo_producto, "codigo");
                let producto = info_producto.producto;

                if (!producto) {
                    producto = fnGetProductByID(codigo_producto);
                }

                if (producto) {
                    fnAgregarProductoCompra(producto, cantidad);
                }
                $("#txtCodigoBarraProducto").val("");
            }
        }

        function fnGetProductByID(_codigoProducto) {
            var data;
            if (_codigoProducto != "") {
                $.ajax({
                    url: `<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx?mth=${mtdEnc("get/product/by/id")}&productCode=${mtdEnc(_codigoProducto)}`,
                    data: null,
                    type: "GET",
                    async: false,
                    contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    dataType: "json",
                    beforeSend: function () {
                        // Mostrar loading
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            data = result.data[0];
                        }
                        else {
                            Swal.fire({ icon: result.type, text: result.text });
                        }
                    },
                    error: function (result) {
                        console.error('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurri\u00F3 un error al buscar el producto.'
                        });
                    }
                });
            }
            else {
                Swal.fire({ icon: "warning", text: "Por favor ingrese el código del producto." });
            }

            return data;
        }

        // Función para validar email
        function validarEmail(email) {
            var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        }

        // Función para guardar nuevo proveedor
        function guardarNuevoProveedor() {
            // Validar campos requeridos
            var nit = $('#txtNitNuevoProveedor').val().trim();
            var nombre = $('#txtNombreNuevoProveedor').val().trim();
            var telefono = $('#txtTelefonoNuevoProveedor').val().trim();
            var email = $('#txtEmailNuevoProveedor').val().trim();
            var direccion = $('#txtDireccionNuevoProveedor').val().trim();

            if (!nit || !nombre) {
                Swal.fire('Campos requeridos', 'El NIT y nombre del proveedor son obligatorios.', 'warning');
                return;
            }

            // Validar formato del email si se proporciona
            if (email && !validarEmail(email)) {
                Swal.fire('Email inválido', 'Por favor ingrese un email válido.', 'warning');
                return;
            }

            // Preparar datos para enviar
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("create/provider"));
            fmAuth.append("nit", mtdEnc(nit));
            fmAuth.append("nombre", mtdEnc(nombre));
            fmAuth.append("telefono", mtdEnc(telefono));
            fmAuth.append("email", mtdEnc(email));
            fmAuth.append("direccion", mtdEnc(direccion));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                beforeSend: function() {
                    $('#btnGuardarNuevoProveedor').prop('disabled', true).html('<span class="fas fa-spinner fa-spin"></span>&nbsp;Guardando...');
                },
                success: function(response) {
                    console.log('=== RESPUESTA CREAR PROVEEDOR ===');
                    console.log('Response completo:', response);
                    console.log('Response.type:', response.type);
                    console.log('Response.data:', response.data);
                    console.log('=== FIN RESPUESTA ===');

                    // Verificar si se creó correctamente
                    if (response.type === "success" || (response.data && response.data.id_proveedor > 0)) {
                        var mensaje = 'El proveedor se cre\u00F3 correctamente.';
                        if (response.data && response.data.id_proveedor) {
                            mensaje += ' ID: ' + response.data.id_proveedor;
                        }
                        Swal.fire('\u00C9xito', mensaje, 'success');

                        // Limpiar formulario
                        $('#formCrearProveedor')[0].reset();

                        // Cerrar modal
                        $('#modalCrearProveedor').modal('hide');

                        // Si tenemos los datos del proveedor, cargarlos directamente
                        if (response.data && response.data.id_proveedor) {
                            $('#lblIdProveedor').text(response.data.id_proveedor);
                            $('#txtNitProveedor').val(response.data.nit);
                            $('#txtNombreProveedor').val(response.data.nombres);
                        } else {
                            // Si no, buscar el proveedor recién creado
                            setTimeout(function() {
                                buscarProveedorPorNit(nit);
                            }, 500);
                        }
                    } else {
                        Swal.fire('Error', response.text || 'Ocurri\u00F3 un error al crear el proveedor.', 'error');
                        // Restaurar botón en caso de error
                        $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurri\u00F3 un error al crear el proveedor.', 'error');
                    // Restaurar botón en caso de error de conexión
                    $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                },
                complete: function() {
                    // Siempre restaurar el botón sin importar el resultado
                    $('#btnGuardarNuevoProveedor').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                    setTimeout(function() {
                        feather.replace();
                    }, 100);
                }
            });
        }

        // Función para cargar formas de pago
        function cargarFormasPago() {
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/medio/pago/sucursal"));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/venta.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    console.log('=== RESPUESTA FORMAS DE PAGO ===');
                    console.log('Response completo:', response);
                    console.log('Response.data_tables:', response.data_tables);
                    console.log('=== FIN RESPUESTA ===');

                    // La respuesta viene en data_tables como array
                    if (response.data_tables && Array.isArray(response.data_tables[0]) && response.data_tables.length > 0) {
                        $('#ddlFormaPago').empty().append('<option value="">Forma de pago</option>');

                        response.data_tables[0].forEach(function(item) {
                            console.log('Agregando opción:', item);
                            // Usar codigo como value y descripcion como texto
                            $('#ddlFormaPago').append('<option value="' + item.codigo + '">' + item.descripcion + '</option>');
                        });
                        console.log('Total opciones agregadas:', response.data_tables.length);
                    } else {
                        console.log('No se pudieron cargar las formas de pago - data_tables no encontrado o vacío');
                        console.log('Estructura de respuesta:', Object.keys(response));
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error al cargar formas de pago:', error);
                    console.log('Status:', status);
                    console.log('XHR:', xhr);
                }
            });
        }

        // Función para buscar proveedor por NIT
        function buscarProveedorPorNit(pNumeroNIT) {
            var nit = pNumeroNIT || $('#txtNitProveedor').val().trim();
            if (!nit) {
                Swal.fire('Campo requerido', 'Ingrese el NIT del proveedor.', 'warning');
                return;
            }

            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("get/provider/by/nit"));
            fmAuth.append("nit", mtdEnc(nit));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.type === "success" && response.data) {
                        $('#lblIdProveedor').text(response.data.id_proveedor);
                        $('#txtNombreProveedor').val(response.data.nombres);
                    } else {
                        Swal.fire({
                            title: 'Proveedor no encontrado',
                            text: '\u00BFDesea crear un nuevo proveedor con este NIT?',
                            icon: 'question',
                            showDenyButton: true,
                            showCancelButton: false,
                            confirmButtonText: 'S\u00ED',
                            denyButtonText: 'No'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                $('#txtNitNuevoProveedor').val(nit);
                                $('#modalCrearProveedor').modal('show');
                            }
                        });
                        $('#txtNombreProveedor').val('');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurri&oacute; un error al buscar el proveedor.', 'error');
                }
            });
        }

        // Función para seleccionar proveedor del modal
        function seleccionarProveedor(id, nit, nombre) {
            $('#lblIdProveedor').text(id);
            $('#txtNitProveedor').val(nit);
            $('#txtNombreProveedor').val(nombre);
            $('#modalProveedores').modal('hide');
        }

        function agregarProveedor($btn, data) {
            $btn.html('<span class="fas fa-spinner fa-spin"></span>');
            seleccionarProveedor(data.id_proveedor, data.nit, data.nombres);

            setTimeout(function () {
                $btn.html('<i class="fas fa-user-check"></i>');
                $("#modalProveedores").modal("hide");
            }, 200);
        }





        // Función para validar precios cuando el usuario escribe manualmente
        function validarPreciosManual(input) {
            var $row = $(input).closest('tr');
            var costoUnitarioTexto = $row.find('.costo-input').val();
            var precioTexto = $(input).val();

            var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
            var precio = parseFloat(limpiarFormatoNumero(precioTexto)) || 0;

            if (precio > 0 && costoUnitario > 0 && precio < costoUnitario) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Precio menor al costo',
                    text: 'El precio no debe ser menor al costo del producto (' + formatearNumero(costoUnitario.toFixed(2)) + ')',
                    showConfirmButton: true,
                    confirmButtonText: 'Entendido'
                });

                // Mostrar borde rojo temporal
                $(input).addClass('is-invalid');
                setTimeout(function() {
                    $(input).removeClass('is-invalid');
                }, 3000);
            } else {
                $(input).removeClass('is-invalid');
            }
        }

        // Función para validar cantidad y costo
        function validarCantidadCosto(input) {
            var $row = $(input).closest('tr');
            var cantidad = parseFloat($row.find('.quntity-input').val()) || 0;
            var costoUnitarioTexto = $row.find('.costo-input').val();
            var costoUnitario = parseFloat(limpiarFormatoNumero(costoUnitarioTexto)) || 0;
            var costoTotal = cantidad * costoUnitario;
            $row.find('.costo-total').text(formatearNumero(costoTotal.toFixed(2)));

            // Actualizar totales generales
            fnUpdateTotales();
        }



        // Función para guardar compra
        function guardarCompra() {
            // Validar campos requeridos
            if (!$('#lblIdProveedor').text() || $('#lblIdProveedor').text() === '') {
                Swal.fire('Campo requerido', 'Seleccione un proveedor.', 'warning');
                return;
            }

            if (!$('#txtFechaCompra').val()) {
                Swal.fire('Campo requerido', 'Ingrese la fecha de compra.', 'warning');
                return;
            }

            // Validar que haya productos en la tabla
            const table = $('#grvProductosCompra').DataTable();
            const productos = table.rows().data().toArray();

            if (productos.length === 0) {
                Swal.fire('Sin productos', 'Debe agregar al menos un producto a la compra.', 'warning');
                return;
            }

            // Preparar datos de la compra
            const datosCompra = {
                id_contribuyente: null, // Se asigna en el backend
                id_sucursal: null, // Se asigna en el backend
                id_usuario: null, // Se asigna en el backend
                moneda: 'Q',
                mto_subtotal: calcularSubtotal(),
                mto_recargo: 0,
                mto_descuento: 0,
                mto_total: calcularTotal(),
                id_moneda: 1,
                productos: productos.map(producto => ({
                    id_producto: producto.id_producto,
                    id_moneda: 1,
                    codigo: producto.codigo,
                    nombre: producto.nombre,
                    cantidad: parseInt(producto.cantidad) || 1,
                    moneda: 'Q',
                    precio_venta: parseFloat(producto.precio_unitario) || 0,
                    costo_unitario: parseFloat(producto.costo_unitario) || 0,
                    descripcion: producto.descripcion || '',
                    categoria: producto.categoria || '',
                    estado: true,
                    existencia: producto.stock_actual || 0,
                    precio_unitario: parseFloat(producto.precio_unitario) || 0,
                    precio_minimo: parseFloat(producto.min_descuento) || 0,
                    recargo: 0,
                    descuento: 0,
                    img_producto: producto.img_producto || ''
                })),
                medios_pago: [{
                    codigo: 'EFE',
                    descripcion: 'Efectivo',
                    moneda: 'Q',
                    id_medio_pago: parseInt($('#ddlFormaPago').val()) || 1,
                    cod_entidad: '',
                    cod_referencia: '',
                    id_banco: 0,
                    fecha_referencia: new Date().toISOString(),
                    num_referencia: '',
                    num_autorizacion: '',
                    monto: calcularTotal()
                }],
                encabezado: {
                    id_referencia: $('#txtCodigoCompra').val() || '',
                    tipo_transaccion: 'COMPRA',
                    fecha: $('#txtFechaCompra').val(),
                    id_caja: null,
                    id_cliente: $('#lblIdProveedor').text()
                }
            };

            // Enviar datos al servidor
            var fmAuth = new FormData();
            fmAuth.append("mth", mtdEnc("save/purchase"));
            fmAuth.append("data", mtdEnc(JSON.stringify(datosCompra)));

            $.ajax({
                url: '<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/compra.ashx',
                type: 'POST',
                data: fmAuth,
                contentType: false,
                processData: false,
                beforeSend: function() {
                    $('#btnGuardarCompraFinal').prop('disabled', true).html('<span class="fas fa-spinner fa-spin"></span>&nbsp;Guardando...');
                },
                success: function(response) {
                    if (response.type === "success") {
                        Swal.fire('\u00C9xito', response.text || 'Compra guardada correctamente.', 'success').then(() => {
                            // Limpiar formulario
                            limpiarFormulario();
                        });
                    } else {
                        Swal.fire('Error', response.text || 'Ocurri\u00F3 un error al guardar la compra.', 'error');
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Ocurri\u00F3 un error al guardar la compra.', 'error');
                },
                complete: function() {
                    $('#btnGuardarCompraFinal').prop('disabled', false).html('<i data-feather="save" class="feather-16"></i>&nbsp;Guardar');
                    feather.replace();
                }
            });
        }

        // Función para calcular subtotal
        function calcularSubtotal() {
            const table = $('#grvProductosCompra').DataTable();
            const productos = table.rows().data().toArray();
            let subtotal = 0;

            productos.forEach(producto => {
                const cantidad = parseInt(producto.cantidad) || 1;
                const precio = parseFloat(producto.costo_unitario) || 0;
                subtotal += cantidad * precio;
            });

            return subtotal;
        }

        // Función para calcular total
        function calcularTotal() {
            return calcularSubtotal(); // Por ahora sin descuentos ni recargos
        }

        // Función para limpiar formulario
        function limpiarFormulario() {
            // Limpiar campos del proveedor
            $('#txtNitProveedor').val('');
            $('#txtNombreProveedor').val('');
            $('#lblIdProveedor').text('');

            // Limpiar campos de la compra
            $('#txtFechaCompra').val('');
            $('#txtCodigoCompra').val('');
            $('#txtPolizaDuca').val('');
            $('#ddlFormaPago').val('').trigger('change');

            // Limpiar campo de búsqueda de productos
            $('#txtCodigoBarraProducto').val('');

            // Limpiar campos de búsqueda de proveedores
            $('#txtBuscarProveedor').val('');
            $('#txtBuscarProducto').val('');

            // Limpiar tabla de productos de compra
            $('#grvProductosCompra').DataTable().clear().draw();

            // Limpiar tabla de búsqueda de proveedores si existe
            if ($.fn.DataTable.isDataTable('#grvProveedores')) {
                $('#grvProveedores').DataTable().clear().draw();
            }

            // Limpiar tabla de búsqueda de productos si existe
            if ($.fn.DataTable.isDataTable('#grvProductos')) {
                $('#grvProductos').DataTable().clear().draw();
            }

            // Actualizar totales
            fnUpdateTotales();

            // Remover clases de validación
            $('.form-control').removeClass('is-invalid');
            $('.invalid-feedback').hide();

            // Cerrar modales si están abiertos
            $('.modal').modal('hide');
        }
    </script>

</asp:Content>
