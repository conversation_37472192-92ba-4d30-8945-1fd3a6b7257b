﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="SIFAFEL_APP.Default" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">

    <div class="welcome d-lg-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center welcome-text">
            <h3 class="d-flex align-items-center">
                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/hi.svg" alt="img" />
                &nbsp;Hola <%=info_usuario?.nombre_completo %>,
            </h3>
            &nbsp;
            <h6>Aquí tienes lo que está sucediendo con tu tienda hoy.</h6>
        </div>
    </div>
    <div class="row sales-cards">
        <div class="col-xl-6 col-sm-12 col-12">
            <div class="card d-flex align-items-center justify-content-between default-cover mb-4">
                <div>
                    <h6>Ganancias Semanales</h6>
                    <h3>Q<span class="counters" data-count="95000.45">95000.45</span></h3>
                    <p class="sales-range">
                        <span class="text-success">
                            <i data-feather="chevron-up" class="feather-16"></i>48%&nbsp;</span>
                        de aumento en comparación con la semana pasada
                    </p>
                </div>
                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/weekly-earning.svg" alt="img" />
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card color-info bg-secondary mb-4">
                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/purchased-earnings.svg" alt="img" />
                <h3 class="counters" data-count="800.00">800+</h3>
                <p>Total de Ganancias</p>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card color-info bg-primary mb-4">
                <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/icons/total-sales.svg" alt="img" />
                <h3 class="counters" data-count="10000.00">10,000+</h3>
                <p>Total de Ventas</p>
            </div>
        </div>
    </div>
</asp:Content>
