﻿using Newtonsoft.Json;
using SIFAFEL_CORE.web_request;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static SIFAFEL_CORE.Core.Utility;
using static SIFAFEL_MODEL.Data_Model_API.Autenticacion.InfoUsuario.InfoContribuyente;

namespace SIFAFEL_APP.Master
{
    public partial class MasterPrincipal : System.Web.UI.MasterPage
    {
        /// <summary>
        /// Información del usuario autenticado.
        /// </summary>
        public InfoUsuario infoUsuario = new InfoUsuario();

        /// <summary>
        /// Nombre del contribuyente & sucursal
        /// </summary>
        public string nombre_pagina { get; set; }

        /// <summary>
        /// url de redireccionamiento
        /// </summary>
        private string url_redirect = ConfigurationManager.AppSettings["url_redirect"];

        /// <summary>
        /// Evento que se ejecuta cuando se carga la página.
        /// </summary>
        /// <param name="sender">El objeto que invoca el evento.</param>
        /// <param name="e">Los datos del evento.</param>
        protected void Page_Load(object sender, EventArgs e)
        {
            SessionManager sessionManager = new SessionManager();

            if (sessionManager.Valid())
            {
                infoUsuario = sessionManager.Get();
                CreateMenuHTML();

                if (infoUsuario.sucursal_session == null && infoUsuario.contribuyentes != null)
                {
                    InfoUsuario.InfoContribuyente.InfoSucursal sucursal = null;

                    sucursal = infoUsuario.id_sucursal_default.HasValue && infoUsuario.id_sucursal_default > 0
                            ? infoUsuario.contribuyentes.SelectMany(c => c.sucursales).FirstOrDefault(s => s.id == infoUsuario.id_sucursal_default)
                            : infoUsuario.contribuyentes.SelectMany(c => c.sucursales).FirstOrDefault();

                    if (sucursal != null)
                    {
                        sessionManager.SetIdSucursal(sucursal.id.ToString());
                    }
                }

                if (infoUsuario.sucursal_session != null)
                {
                    nombre_pagina = $"{infoUsuario.sucursal_session.nombre_contribuyente} - {infoUsuario.sucursal_session.descripcion}";
                }
            }
            else
            {
                string url_redirect = ConfigurationManager.AppSettings["url_redirect"] + "Auth";
                Response.Redirect(url_redirect);
                return;
            }
        }

        /// <summary>
        /// Proceso que genera el Menu
        /// </summary>
        private void CreateMenuHTML()
        {
            string htmlOutput = GenerateHTMLMenu_ModeDefault(infoUsuario.menu);
            menu_place_holder.Controls.Add(new LiteralControl(htmlOutput));
        }

        /// <summary>
        /// Encripta algun string
        /// </summary>
        /// <param name="pString"></param>
        /// <returns></returns>
        public string encript_string(string pString)
        {
            return encript_core.SetValue(pString);
        }

        /// <summary>
        /// Genera el HTML del menu (Collapse)
        /// </summary>
        /// <param name="menuList"></param>
        /// <returns></returns>
        private string GenerateHTMLMenu_ModeCollapsed(List<_InfoMenuModulo> menuList)
        {
            var html = @"<div class='sidebar collapsed-sidebar' id='collapsed-sidebar'>
                            <div class='sidebar-inner slimscroll'>
                                <div id='sidebar-menu-2' class='sidebar-menu sidebar-menu-three'>
                                    <aside id='aside' class='ui-aside'>
                                        <ul class='tab nav nav-tabs' id='ul_tab_nav' role='tablist'>";

            foreach (var menu in menuList)
            {
                if (menu.visible_web)
                {
                    foreach (var mainMenu in menu.menus)
                    {
                        string hrefValue = mainMenu.sub_menus.Count == 0 ? (url_redirect + mainMenu.url) : $"#{mainMenu.nombre.ToLower()}";
                        string dataTarget = mainMenu.sub_menus.Count == 0 ? "" : $"data-bs-toggle='tab' data-bs-target='#{mainMenu.nombre.ToLower()}' role='tab'";

                        html += $@"<li class='nav-item' role='presentation'>
                                        <a class='tablinks nav-link' href='{hrefValue}' id='{mainMenu.nombre.ToLower()}-tab' {dataTarget}  aria-selected='true'>
                                            <i class='{mainMenu.icono}'></i>
                                            {/*mainMenu.nombre*/""}
                                        </a>
                                    </li>";
                    }
                }
            }

            html += @"</ul>
                </aside>
                <div class='tab-content tab-content-four pt-2'>";

            foreach (var menu in menuList)
            {
                if (menu.visible_web)
                {
                    foreach (var mainMenu in menu.menus)
                    {
                        html += $@"<div class='tab-pane' id='{mainMenu.nombre.ToLower()}' aria-labelledby='{mainMenu.nombre.ToLower()}-tab'>
                                    <ul class='submenu'>";
                        foreach (var subMenu in mainMenu.sub_menus)
                        {
                            //<i class='{subMenu.icono}'></i>&nbsp;
                            html += $@"<li>
                                        <a href='{subMenu.url}'>
                                            {subMenu.nombre}
                                        </a>
                                    </li>";
                        }
                        html += @"</ul>
                            </div>";
                    }
                }
            }

            html += @"</div>
                    </div>
                </div>
            </div>";
            return html;
        }

        #region Diseño|Default
        /// <summary>
        /// Genera el HTML del menu (Default)
        /// </summary>
        /// <param name="modulos"></param>
        /// <returns></returns>
        public string GenerateHTMLMenu_ModeDefault(List<_InfoMenuModulo> modulos)
        {
            var sb = new StringBuilder();

            sb.AppendLine("<div class=\"sidebar\" id=\"sidebar\">");
            sb.AppendLine("<div class=\"sidebar-inner slimscroll\">");
            sb.AppendLine("<div id=\"sidebar-menu\" class=\"sidebar-menu\">");
            sb.AppendLine("<ul>");

            foreach (var modulo in modulos)
            {
                sb.AppendLine("<li class=\"submenu-open\">");
                if (modulo.visible_web)
                {
                    sb.AppendLine($"<h6 class=\"submenu-hdr\">{modulo.modulo}</h6>");
                }
                sb.AppendLine("<ul>");

                foreach (var menu in modulo.menus)
                {
                    sb.AppendLine(GenerateHTMLItemMenu_ModeDefault(menu, 1));
                }

                sb.AppendLine("</ul>");
                sb.AppendLine("</li>");
            }

            sb.AppendLine("</ul>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");
            sb.AppendLine("</div>");

            return sb.ToString();
        }

        /// <summary>
        /// Genera submenu de menu (Default)
        /// </summary>
        /// <param name="menu"></param>
        /// <param name="nivel"></param>
        /// <returns></returns>
        public string GenerateHTMLItemMenu_ModeDefault(_InfoMenu menu, int nivel)
        {
            var sb = new StringBuilder();

            var classLevel = "";

            if (menu.sub_menus.Any())
            {
                classLevel = nivel == 1 ? "submenu"
                            : nivel == 2 ? "submenu submenu-two"
                            : nivel == 3 ? "submenu submenu-two submenu-three"
                            : "submenu";
            }

            sb.AppendLine($"<li class=\"{classLevel}\">");
            sb.AppendLine($"<a href=\"{(menu.sub_menus.Count == 0 ? url_redirect + menu.url : "")}\">");

            if (!string.IsNullOrEmpty(menu.icono))
            {
                switch (menu.tipo_icono)
                {
                    case "svg":
                        sb.AppendLine($"<img src=\"{menu.icono}\" />");
                        break;
                    case "feather":
                        sb.AppendLine($"<i data-feather=\"{menu.icono}\"></i>");
                        break;
                    case "class":
                    default:
                        sb.AppendLine($"<i class=\"{menu.icono}\"></i>");
                        break;
                }
            }

            sb.AppendLine($"&nbsp;<span>{menu.nombre}</span>");

            if (menu.sub_menus.Any())
            {
                sb.AppendLine($"<span class=\"menu-arrow {(nivel > 1 ? "inside-submenu" : "")}{(nivel > 2 ? " inside-submenu-two" : "")}\"/></a>");
                sb.AppendLine("<ul>");

                foreach (var subMenu in menu.sub_menus)
                {
                    sb.AppendLine(GenerateHTMLItemMenu_ModeDefault(subMenu, nivel + 1));
                }

                sb.AppendLine("</ul>");
            }
            else
            {
                sb.AppendLine("</a>");
            }

            sb.AppendLine("</li>");

            return sb.ToString();
        }
        #endregion
    }
}