﻿//Este archivo sirve para agregar todas las fuciones que se comparten con el modulo de Compras y Ventas\

//funcion para buscar el producto con el codigo y retorna el resultado para agregarlo en la tabla de compra o venta
function BuscarProducto(_codigoProducto) {
    var data;
    if (_codigoProducto != "") {
        $.ajax({
            url: _url_redirect+"Producto/GetProductByCode" + "?codigo=" + _codigoProducto,
            data: null,
            type: "GET",
            async: false,
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                Holdon_Open("Buscando...");
            },
            success: function (result) {
                if (result.type == "success") {
                    data = result.data[0];
                }
                else {
                    Swal.fire({ icon: result.type, text: result.text });
                }
            },
            error: function (result) {
                console.error('error:', result);
                Swal.fire({
                    icon: 'error',
                    text: 'Ocurrio un error al iniciar el proceso de búsqueda.'
                });
            },
            complete: function () {
                Holdon_Close();
            }
        });
    }
    else {
        Swal.fire({ icon: "warning", text: "Por favor ingrese el codigo del producto." });
    }

    return data;
}

//funcion que obtiene toda la lista de productos
function _ListaProductos() {
    $.ajax({
        url: _url_redirect+"Producto/Listado_Producto",
        data: null,
        type: "GET",
        contentType: 'application/x-www-form-urlencoded; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            Holdon_Open("Buscando...");
        },
        success: function (result) {
            if (result.type == "success") {
                $('#grvProductos').dataTable().fnClearTable();
                $('#grvProductos').DataTable().search("").draw();
                $('#grvProductos').dataTable().fnAddData(result.data);

                $("#mdlListaProductos").modal("show")
            }
            else if (result.type == "warning") {
                Swal.fire({ icon: result.type, text: result.text });
            }
            else {
                Swal.fire({ icon: result.type, text: result.text });
            }
        },
        error: function (result) {
            console.log('error:', result);
            Swal.fire({
                icon: 'error',
                text: 'Ocurrio un error al listar los productos.',
            });
        },
        complete: function () {
            Holdon_Close();
        }
    });
}

function ListaClientes() {
    $.ajax({
        url: _url_redirect + "Cliente/Listado_Clientes",
        data: null,
        type: "GET",
        contentType: 'application/x-www-form-urlencoded; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            Holdon_Open("Buscando...");
        },
        success: function (result) {
            if (result.type == "success") {
                $('#grvClientes').dataTable().fnClearTable();
                $('#grvClientes').DataTable().search("").draw();
                $('#grvClientes').dataTable().fnAddData(result.data);

                $("#mdlListaClientes").modal("show")
            }
            else if (result.type == "warning") {
                Swal.fire({ icon: result.type, text: result.text });
            }
            else {
                Swal.fire({ icon: result.type, text: result.text });
            }
        },
        error: function (result) {
            console.log('error:', result);
            Swal.fire({
                icon: 'error',
                text: 'Ocurrio un error al obtener la lista de clientes.',
            });
        },
        complete: function () {
            Holdon_Close();
        }
    });
}

//Function para obtener la lista de proveeodres
function ListaProveedores() {
    $.ajax({
        url: _url_redirect + "Proveedor/Listado_Proveedores",
        data: null,
        type: "GET",
        contentType: 'application/x-www-form-urlencoded; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            Holdon_Open("Buscando...");
        },
        success: function (result) {
            if (result.type == "success") {
                $('#dtLstProveedor').dataTable().fnClearTable();
                $('#dtLstProveedor').DataTable().search("").draw();
                $('#dtLstProveedor').dataTable().fnAddData(result.data);

                $("#mdlListaProveedores").modal("show")
            }
            else if (result.type == "warning") {
                Swal.fire({ icon: result.type, text: result.text });
            }
            else {
                Swal.fire({ icon: result.type, text: result.text });
            }
        },
        error: function (result) {
            console.log('error:', result);
            Swal.fire({
                icon: 'error',
                text: 'Ocurrio un error al obtener la lista de proveedores.',
            });
        },
        complete: function () {
            Holdon_Close();
        }
    });
}

//funcion que sirve para actualizar los campos de subtotal, descuento y total
function updateTotalPagar(total, descuento, moneda) {
    $("#subTotal").removeClass('far fa-money-bill-alt').html(moneda);
    $("#descuento").removeClass('far fa-money-bill-alt').html(moneda);
    $("#total").removeClass('far fa-money-bill-alt').html(moneda);

    $("#txtSubTotal").val(PriceNumberFormat(total+descuento));
    $("#txtDescuento").val(PriceNumberFormat(descuento));
    //$("#txtTotal").val(PriceNumberFormat(total - descuento));
    $("#txtTotal").val(PriceNumberFormat(total));

    var totalPaga = parseFloat($("#txtTotalPagaCliente").val());
    if (!isNaN(totalPaga)) {
        //agregar valor del vuelto
        $("#txtVueltoCliente").val(PriceNumberFormat(totalPaga-total));
    }
}

//funcion que perimete agregar la moenda y el formado a los campos
function MoneyFormat(moneda, price) {
    return "<span class=\"currency\">" + moneda + "</span>" +
        "<span class=\"price\">" + PriceNumberFormat(price) + "</span>";
}

function onKeyPressOnlyNumber(isDecimal) {
    var charCode = event.which;
    if (isDecimal) {
        if (charCode < 46 || charCode > 57 || charCode == 47) {
            event.preventDefault();
        }
    }
    else {
        if (charCode < 48 || charCode > 57) {
            event.preventDefault();
        }
    }
}

function validarExistenciaProducto(existencia, cantidad) {
    if (cantidad > existencia) {
        Swal.fire({
            icon: 'warning',
            title: 'Producto insuficiente',
            text: 'Lo sentimos, no hay existencias disponibles de este producto. Por favor comuniquese con el Administrador para actualizar el inventario.'
        });
        return false;
    }
    return true;
}

function validarPrecioMinimo(precio_minimo, nuevoPrecio) {
    if (nuevoPrecio < precio_minimo) {
        Swal.fire({
            icon: 'warning',
            title: '',
            text: 'El precio minimo del producto es Q ' + PriceNumberFormat(precio_minimo)+'. Por favor comuniquese con el Administrador.'
        });
        return false;
    }
    return true;
}

//metodo generico para obtener la lista de datos agregados en una tabla
function getDTProductos(tblGeneric) {
    var table = $("#" + tblGeneric).DataTable();
    var data = table.rows().data();
    return data.toArray();
}