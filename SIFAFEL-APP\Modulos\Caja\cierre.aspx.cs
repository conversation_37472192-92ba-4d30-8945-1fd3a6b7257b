﻿using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static SIFAFEL_CORE.Core.Utility;
using static SIFAFEL_MODEL.Data_Model_API.Autenticacion.InfoUsuario.InfoContribuyente;

namespace SIFAFEL_APP.Modulos.Caja
{
    public partial class cierre : System.Web.UI.Page
    {
        /// <summary>
        /// Información del usuario autenticado.
        /// </summary>
        public InfoUsuario info_usuario = new InfoUsuario();

        /// <summary>
        /// Listado de sucursales
        /// </summary>
        public List<InfoSucursal> lst_sucursales = new List<InfoSucursal>();

        /// <summary>
        /// Listados de cajas por sucursal
        /// </summary>
        public List<InfoCaja> lstCajas = new List<InfoCaja>();

        public List<InfoMoneda> lstMonedas = new List<InfoMoneda>();
        protected void Page_Load(object sender, EventArgs e)
        {
            Response.Redirect("./"); //TEMPORAL
            if (!IsPostBack)
            {
                SessionManager sessionManager = new SessionManager();

                if (sessionManager.Valid())
                {
                    lstCajas = sessionManager.GetCajas();
                    lstMonedas = sessionManager.GetMonedas();
                }

                if (Session["AuthInfoUser"] != null)
                {
                    info_usuario = Session["AuthInfoUser"] as InfoUsuario;

                    if (info_usuario != null && info_usuario.contribuyentes != null && info_usuario.sucursal_session != null)
                    {
                        int id_contribuyente = info_usuario.sucursal_session.id_contribuyente;
                        var contribuyente = info_usuario.contribuyentes.FirstOrDefault(c => c.id == id_contribuyente);

                        if (contribuyente != null)
                        {
                            lst_sucursales = contribuyente.sucursales.ToList();
                        }
                    }
                    else
                    {
                        lst_sucursales = new List<InfoSucursal>(); // Listado vacío
                    }
                }
            }
        }
    }
}