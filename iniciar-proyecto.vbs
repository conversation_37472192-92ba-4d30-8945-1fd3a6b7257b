Set WshShell = CreateObject("WScript.Shell")
strPath = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)

' Iniciar SIFAFEL-APP
appPath = "D:\Personal\Proyectos\Software\SIFAFEL-FRONT\SIFAFEL-APP"
appPort = 54326
WshShell.Run """C:\Program Files\IIS Express\iisexpress.exe"" /path:""" & appPath & """ /port:" & appPort, 0, False

' Iniciar SIFAFEL-CDN
cdnPath = "D:\Personal\Proyectos\Software\SIFAFEL-FRONT\SIFAFEL-CDN"
cdnPort = 57977
WshShell.Run """C:\Program Files\IIS Express\iisexpress.exe"" /path:""" & cdnPath & """ /port:" & cdnPort, 0, False

' Mostrar mensaje de éxito
WScript.Sleep 1000
MsgBox "SIFAFEL-FRONT iniciado correctamente." & vbCrLf & vbCrLf & _
       "APP: http://localhost:" & appPort & vbCrLf & _
       "CDN: http://localhost:" & cdnPort & vbCrLf & vbCrLf & _
       "Para detener los servidores, ejecuta 'detener-sifafel-invisible.vbs'", _
       64, "SIFAFEL-FRONT"
