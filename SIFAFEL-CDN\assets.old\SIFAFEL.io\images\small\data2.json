{"v": "5.4.2", "fr": 30, "ip": 0, "op": 150, "w": 1600, "h": 1200, "nm": "temwork_9", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 207", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [29.5, 42.5, 0], "ix": 2}, "a": {"a": 0, "k": [173, -171.5, 0], "ix": 1}, "s": {"a": 0, "k": [-90, 90, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [6.19, 2.923], [2.12, 0.111], [3.307, -1.665]], "o": [[0, 0], [0, 0], [-6.19, -2.923], [-2.12, -0.111], [-3.307, 1.666]], "v": [[154.847, -166.03], [190.723, -166.03], [183.981, -176.427], [168.972, -175.798], [157.778, -178.018]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [125, 125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Mouth", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -5, "ix": 10}, "p": {"a": 0, "k": [29.329, 51.255, 0], "ix": 2}, "a": {"a": 0, "k": [-74.877, -444.489, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 44, "s": [100, 100, 100], "e": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 62.5, "s": [100, 100, 100], "e": [72, 72, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 66, "s": [72, 72, 100], "e": [72, 72, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 96.5, "s": [72, 72, 100], "e": [100, 100, 100]}, {"t": 110}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 44, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-76, -437.875], [-66.125, -447.75]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 49, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 62.5, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}], "e": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 66, "s": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}], "e": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 96.5, "s": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-76, -437.875], [-66.125, -447.75]], "c": true}]}, {"t": 110}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [41.75, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-16.875, 7.25], [-10.875, 7.25]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960784314, 0.552941176471, 0.360784313725, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "S<PERSON>pe Layer 5", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.75, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-26.25, 7.25], [-0.25, 7.25]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960784314, 0.552941176471, 0.360784313725, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "glass", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "n": "0p667_0p667_0p333_0p333", "t": 0, "s": [41, 9.25, 0], "e": [41, 9.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 19, "s": [41, 9.25, 0], "e": [41.333, 9.25, 0], "to": [0.05541682243347, -2.96059482056635e-16, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 34, "s": [41.333, 9.25, 0], "e": [41, 9.25, 0], "to": [0, 0, 0], "ti": [7.84628539918231e-08, -2.96059482056635e-16, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 48, "s": [41, 9.25, 0], "e": [41.333, 9.25, 0], "to": [-7.84628539918231e-08, 2.96059482056635e-16, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 63, "s": [41.333, 9.25, 0], "e": [41, 9.25, 0], "to": [0, 0, 0], "ti": [0.05541674420238, 0, 0]}, {"i": {"x": 0.713, "y": 0.713}, "o": {"x": 0.333, "y": 0.333}, "n": "0p713_0p713_0p333_0p333", "t": 77, "s": [41, 9.25, 0], "e": [41, 9.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.283, "y": 1}, "o": {"x": 0.664, "y": 0}, "n": "0p283_1_0p664_0", "t": 93, "s": [41, 9.25, 0], "e": [43.5, 9.25, 0], "to": [0.06986629962921, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "n": "0p667_0p667_0p333_0p333", "t": 115, "s": [43.5, 9.25, 0], "e": [43.5, 9.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.184, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p184_1_0p333_0", "t": 129, "s": [43.5, 9.25, 0], "e": [41, 9.25, 0], "to": [0, 0, 0], "ti": [0.14237077534199, 0, 0]}, {"t": 150}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [23, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "glass", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [13.25, 9.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [23, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "<PERSON><PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [123.25, 125, 0], "e": [130.75, 125, 0], "to": [1.25, 0, 0], "ti": [-0.98701506853104, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 19, "s": [130.75, 125, 0], "e": [129.172, 125, 0], "to": [0.98701506853104, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 34, "s": [129.172, 125, 0], "e": [130.75, 125, 0], "to": [0, 0, 0], "ti": [3.2029336693995e-07, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 48, "s": [130.75, 125, 0], "e": [129.172, 125, 0], "to": [-3.2029336693995e-07, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 63, "s": [129.172, 125, 0], "e": [130.75, 125, 0], "to": [0, 0, 0], "ti": [-0.26298522949219, 0, 0]}, {"i": {"x": 0.706, "y": 0.706}, "o": {"x": 0.333, "y": 0.333}, "n": "0p706_0p706_0p333_0p333", "t": 77, "s": [130.75, 125, 0], "e": [130.75, 125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.283, "y": 1}, "o": {"x": 0.664, "y": 0}, "n": "0p283_1_0p664_0", "t": 93, "s": [130.75, 125, 0], "e": [118.75, 125, 0], "to": [-0.3473002910614, 0, 0], "ti": [1.0329372882843, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 115, "s": [118.75, 125, 0], "e": [118.75, 125, 0], "to": [-0.84913778305054, 0, 0], "ti": [1.0329372882843, 0, 0]}, {"i": {"x": 0.184, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p184_1_0p333_0", "t": 129, "s": [118.75, 125, 0], "e": [123.25, 125, 0], "to": [-1.25, 0, 0], "ti": [-0.24051733314991, 0, 0]}, {"t": 150}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "deep of ear", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.5, 1.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-7.375, 5.125], [-5.072, -11.704], [0, 0]], "o": [[0.125, -0.375], [1.625, 3.75], [0, 0]], "v": [[-37, 11.5], [-43.25, 22.625], [-37.125, 25.625]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.901960784314, 0.552941176471, 0.360784313725, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "ear", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -4, "ix": 10}, "p": {"a": 0, "k": [-24.5, 13.5, 0], "ix": 2}, "a": {"a": 0, "k": [-25.5, 17.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.5, 0], [0, 0], [-16, 0], [0, 0]], "o": [[-11.5, 0], [0, 0], [9.5, 0], [0, 0]], "v": [[-39.25, 2.25], [-50.5, 21.5], [-30.75, 37.5], [-26, 10.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-24.75, 17.5], "ix": 2}, "a": {"a": 0, "k": [-24.75, 17.5], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Shape Layer 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [125, 125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -13.5]], "v": [[18.5, -77.25], [20.25, -66.75], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 19, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-16.358, 0.738], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [9.988, 0], [0, -14.486]], "v": [[21.461, -76.599], [22.343, -66.349], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [29.554, -27.717], [48.158, -46.704]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 34, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-16.358, 0.738], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [9.988, 0], [0, -14.486]], "v": [[21.461, -76.599], [22.343, -66.349], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [29.554, -27.717], [48.158, -46.704]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 48, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-16.358, 0.738], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [9.988, 0], [0, -14.486]], "v": [[21.461, -76.599], [22.343, -66.349], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [29.554, -27.717], [48.158, -46.704]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 63, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-16.358, 0.738], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [9.988, 0], [0, -14.486]], "v": [[21.461, -76.599], [22.343, -66.349], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [29.554, -27.717], [48.158, -46.704]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 77, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}]}, {"i": {"x": 0.283, "y": 1}, "o": {"x": 0.664, "y": 0}, "n": "0p283_1_0p664_0", "t": 93, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [-18.834, 0.85], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [11.5, 0], [0, -13.5]], "v": [[22.25, -76.5], [23, -66.25], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -21]], "v": [[16.25, -77.25], [18, -67], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [20, -27.5], [44.25, -44.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 115, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -21]], "v": [[16.25, -77.25], [18, -67], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [20, -27.5], [44.25, -44.75]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -21]], "v": [[16.25, -77.25], [18, -67], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [20, -27.5], [44.25, -44.75]], "c": true}]}, {"i": {"x": 0.184, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p184_1_0p333_0", "t": 129, "s": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -21]], "v": [[16.25, -77.25], [18, -67], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [20, -27.5], [44.25, -44.75]], "c": true}], "e": [{"i": [[0, 0], [0, 0], [30, 0.25], [0, 0], [0, 0], [0, 0], [0, 13.5]], "o": [[0, 0], [0, 0], [-30, -0.25], [0, 0], [0, 0], [0, 0], [0, -13.5]], "v": [[18.5, -77.25], [20.25, -66.75], [-29.5, -71], [-55.25, -29.5], [-23.25, -43.75], [31, -27.75], [48.75, -47]], "c": true}]}, {"t": 150}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "head 3", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.75, 16], [-5.796, -61.231], [0, 0], [-32, 3.25], [-5.5, 32.75]], "o": [[-11.977, -17.827], [5.75, 60.75], [0, 0], [11.291, -1.147], [5.5, -32.75]], "v": [[30.25, -52], [-54.75, -23], [-32, 59.75], [19.75, 80.75], [45.5, 41.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Shape Layer 3", "parent": 8, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-8.379, 8.82], [-12.75, 10], [-10.428, -4.345], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [4.75, -5], [7.688, -6.029], [15, 6.25], [0, 0]], "v": [[49.75, -74], [-85.25, -71.25], [-75, 17.25], [-21.25, 3.5], [-17.5, -31], [13.75, -27.75], [48.75, -37.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "head 2", "parent": 16, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.75, 16], [-5.796, -61.231], [0, 0], [-32, 3.25], [-5.5, 32.75]], "o": [[-11.977, -17.827], [5.75, 60.75], [0, 0], [11.291, -1.147], [5.5, -32.75]], "v": [[30.25, -52], [-54.75, -23], [-32, 59.75], [19.75, 80.75], [45.5, 41.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Shape Layer 1", "parent": 8, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [15.25, 0], [11.75, 0.25]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-20.5, 0], [-11.75, -0.25]], "v": [[-25.5, 9.25], [-70.75, 15.25], [-69, 62], [-12, 84.75], [44.75, 85], [56.5, 44], [32, 36.75], [-6.25, 47]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.847058823529, 0.596078431373, 0.478431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "head", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [125, 125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.75, 16], [-5.796, -61.231], [0, 0], [-32, 3.25], [-5.5, 32.75]], "o": [[-11.977, -17.827], [5.75, 60.75], [0, 0], [11.291, -1.147], [5.5, -32.75]], "v": [[30.25, -52], [-54.75, -23], [-32, 59.75], [19.75, 80.75], [45.5, 41.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [125, 125, 0], "e": [121.75, 125, 0], "to": [-0.54166668653488, 0, 0], "ti": [0.4477775990963, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 19, "s": [121.75, 125, 0], "e": [122.313, 125, 0], "to": [-0.4477775990963, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 34, "s": [122.313, 125, 0], "e": [121.75, 125, 0], "to": [0, 0, 0], "ti": [4.68338839709759e-07, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 48, "s": [121.75, 125, 0], "e": [122.313, 125, 0], "to": [-4.68338839709759e-07, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 63, "s": [122.313, 125, 0], "e": [121.75, 125, 0], "to": [0, 0, 0], "ti": [0.09388860315084, 0, 0]}, {"i": {"x": 0.706, "y": 0.706}, "o": {"x": 0.333, "y": 0.333}, "n": "0p706_0p706_0p333_0p333", "t": 77, "s": [121.75, 125, 0], "e": [121.75, 125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.283, "y": 1}, "o": {"x": 0.664, "y": 0}, "n": "0p283_1_0p664_0", "t": 93, "s": [121.75, 125, 0], "e": [126, 125, 0], "to": [0.12505832314491, 0, 0], "ti": [-0.44603383541107, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 115, "s": [126, 125, 0], "e": [126, 125, 0], "to": [0.36796680092812, 0, 0], "ti": [-0.44603383541107, 0, 0]}, {"i": {"x": 0.184, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p184_1_0p333_0", "t": 129, "s": [126, 125, 0], "e": [125, 125, 0], "to": [0.54166668653488, 0, 0], "ti": [0.05344611033797, 0, 0]}, {"t": 150}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.657, 13.758], [0, 0], [0, 0]], "o": [[-2.625, -9.875], [0, 0], [0, 0]], "v": [[43, -36.125], [33.875, -36.75], [44, -5.125]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 7", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [63.75, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-26.875, 7.25], [-10.875, 7.25]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960784314, 0.552941176471, 0.360784313725, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 150, "op": 181, "st": 150, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 120, "op": 151, "st": 120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 90, "op": 120, "st": 90, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 60, "op": 90, "st": 60, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 30, "op": 60, "st": 30, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "f1", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 30, "st": 0, "bm": 0}]}, {"id": "comp_2", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "foot 2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [-29.85], "e": [-16.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [-16.925], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 8, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [7], "e": [-3.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-3.259], "e": [-44.517]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-44.517], "e": [-51.85]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 23, "s": [-51.85], "e": [-46.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 26, "s": [-46.136], "e": [-33.85]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [-0.069, 11.397, 0], "ix": 2}, "a": {"a": 0, "k": [137.805, 154.981, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [4.087, 2.86], [0, 0], [7.084, -0.17]], "o": [[0, 0], [0, 0], [0, 0], [1.622, -4.718], [0, 0], [0, 0], [-6.696, -0.162]], "v": [[128.44, 154.618], [124.484, 167.78], [139.089, 173.19], [166.264, 182.565], [161.765, 167.473], [148.093, 155.019], [137.61, 149.237]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100.2, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "foot", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -10.85, "ix": 10}, "p": {"a": 0, "k": [-1.547, -2.758, 0], "ix": 2}, "a": {"a": 0, "k": [139, 141, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-7.148, -2.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.208, 1.267], [0, 0], [0, 0]], "v": [[130.646, 137.268], [129.617, 157.801], [137.177, 163.401], [146.021, 159.006], [147.729, 138.633]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [923.5, 739.5, 0], "e": [936.843, 762.752, 0], "to": [6.44401264190674, 8.28515911102295, 0], "ti": [-2.63418841362, -7.4302830696106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 3, "s": [936.843, 762.752, 0], "e": [940.378, 781.06, 0], "to": [2.29588556289673, 6.47602891921997, 0], "ti": [-0.18238441646099, -5.31688356399536, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 5, "s": [940.378, 781.06, 0], "e": [928.989, 814.364, 0], "to": [0.52117866277695, 15.1934385299683, 0], "ti": [5.28653335571289, -6.2158350944519, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 9, "s": [928.989, 814.364, 0], "e": [922.705, 819.451, 0], "to": [-3.42243218421936, 4.02405023574829, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 10, "s": [922.705, 819.451, 0], "e": [913.271, 823.657, 0], "to": [0, 0, 0], "ti": [5.51117610931396, -1.33640682697296, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 11, "s": [913.271, 823.657, 0], "e": [893.885, 824.653, 0], "to": [-5.63313817977905, 1.36598134040833, 0], "ti": [6.46023893356323, 0.20196075737476, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 13, "s": [893.885, 824.653, 0], "e": [876.756, 817.605, 0], "to": [-8.20620059967041, -0.25654321908951, 0], "ti": [2.44176959991455, 0.82832980155945, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [876.756, 817.605, 0], "e": [861.562, 804.992, 0], "to": [-2.84095358848572, -0.96374636888504, 0], "ti": [6.07430553436279, 6.88331842422485, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 17, "s": [861.562, 804.992, 0], "e": [855.068, 796.053, 0], "to": [-2.25484848022461, -2.55516290664673, 0], "ti": [2.01216197013855, 2.67479276657104, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 18, "s": [855.068, 796.053, 0], "e": [848.866, 778.603, 0], "to": [-4.85461759567261, -6.45330572128296, 0], "ti": [-0.25908613204956, 4.81688117980957, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 20, "s": [848.866, 778.603, 0], "e": [851.388, 769.322, 0], "to": [-1.32184565067291, -3.32807850837708, 0], "ti": [-0.60235822200775, 3.32946515083313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 21, "s": [851.388, 769.322, 0], "e": [868.184, 744.437, 0], "to": [1.54719543457031, -8.55194282531738, 0], "ti": [-8.73117542266846, 6.7682638168335, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 24, "s": [868.184, 744.437, 0], "e": [885.453, 735.168, 0], "to": [5.17089986801147, -4.0083966255188, 0], "ti": [-6.15042924880981, 2.25092148780823, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 26, "s": [885.453, 735.168, 0], "e": [904.83, 732.714, 0], "to": [6.45584583282471, -2.36269736289978, 0], "ti": [-6.24136257171631, -0.1597348600626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 28, "s": [904.83, 732.714, 0], "e": [923.5, 739.5, 0], "to": [7.71555662155151, 0.19746382534504, 0], "ti": [-3.87464928627014, -5.58405351638794, 0]}, {"t": 30}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [360], "e": [360]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [360], "e": [320]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [320], "e": [360]}, {"t": 30}], "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [304, -338], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-75, 0], [-0.532, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 4", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 76.971, "ix": 10}, "p": {"a": 0, "k": [3.591, -11.095, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 23, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"t": 30}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Hose 1::Shoulder", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [826, 585, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 32, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 2", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [60], "e": [2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [2], "e": [-38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [-38], "e": [-96.25]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-96.25], "e": [-186.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-186.5], "e": [-244.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 25, "s": [-244.5], "e": [-300]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [0, -47, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [360]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [899, 810.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [7, 7], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Shape Layer 1", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 33, "ix": 10}, "p": {"a": 0, "k": [0.5, -0.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 55], "ix": 2}, "p": {"a": 0, "k": [0, -23], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_3", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "<PERSON><PERSON><PERSON><PERSON> 26", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 2.732, "ix": 10}, "p": {"a": 0, "k": [-149.286, -154.358, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [80.645, 80.645, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [14, 14], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 31", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [5.645, 7.661, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [104.839, 104.839, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-111.771, -170.036], [-147.991, -170.036], [-147.991, -171.036], [-111.771, -171.036]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 30", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [5.645, 7.661, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [104.839, 104.839, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.553, 0], [-0.198, 5.479]], "o": [[0.198, 5.48], [4.554, 0], [0, 0]], "v": [[-134.335, -170.036], [-125.856, -160.174], [-117.377, -170.036]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[5.231, 0], [0, 6.241], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-5.231, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.001, 6.284]], "v": [[-125.856, -159.174], [-135.344, -170.493], [-135.344, -170.993], [-134.843, -171.036], [-116.37, -171.036], [-116.37, -170.536]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 26", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [5.645, 7.661, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [104.839, 104.839, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.553, 0], [-0.198, 5.479]], "o": [[0.198, 5.48], [4.554, 0], [0, 0]], "v": [[-112.177, -170.036], [-103.698, -160.174], [-95.219, -170.036]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[5.231, 0], [0, 6.241], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-5.231, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.001, 6.284]], "v": [[-103.698, -159.174], [-113.186, -170.493], [-113.186, -170.993], [-112.685, -171.036], [-94.212, -171.036], [-94.212, -170.536]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 22", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -0.397, "ix": 10}, "p": {"a": 0, "k": [-132.459, -171.141, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100.806, 100.806, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-18.75, -1.75], [-23.75, -1.75], [-27.25, 2.75], [-27.25, 7.5], [-24.75, 11.25], [-19, 14.25], [-15, 12.5], [-14.25, 7.75], [-14.5, 2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 3, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 21", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [-2.397], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25.5, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38.5, "s": [-2.397], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 51, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 64, "s": [-2.397], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 76.5, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88.99, "s": [-2.397], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 101, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 113.49, "s": [-2.397], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125.5, "s": [-0.397], "e": [-2.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.99, "s": [-2.397], "e": [-0.397]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [-171.755, -197.902, 0], "ix": 2}, "a": {"a": 0, "k": [-38.797, -26.317, 0], "ix": 1}, "s": {"a": 0, "k": [100.806, 100.806, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.293, -22.475], [0, 0], [0, 0], [0, 0]], "o": [[-2.21, 24.214], [0, 0], [0, 0], [0, 0]], "v": [[-38.825, -27.513], [-41.5, 37.5], [-13, 47], [-10, -20.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 29", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-16.482, 31.096], [-14.813, -5.575], [-0.315, -2.527], [7.782, -1.448], [5.61, 0.673], [0, 0]], "o": [[8.376, -15.803], [21.556, 8.113], [0, 0], [-5.555, 1.033], [0, 0], [0, 0]], "v": [[-167.792, -211.137], [-121.36, -226.456], [-99.805, -187.196], [-114.939, -187.302], [-131.791, -186.81], [-142.365, -188.079]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "head", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [1], "e": [0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25.5, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38.5, "s": [1], "e": [0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 51, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 64, "s": [1], "e": [0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 76.5, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88.99, "s": [1], "e": [0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 101, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 113.49, "s": [1], "e": [0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125.5, "s": [0.397], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.99, "s": [1], "e": [0.397]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [-4.01, -29.416, 0], "ix": 2}, "a": {"a": 0, "k": [-153.026, -148.965, 0], "ix": 1}, "s": {"a": 0, "k": [99.2, 99.2, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.709, -22.529], [11.382, 11.358], [-3.15, 8.221], [-13.873, -5.307]], "o": [[-10.71, 22.528], [-11.382, -11.358], [3.151, -8.22], [13.874, 5.307]], "v": [[-106.146, -143.84], [-155.206, -150.798], [-161.472, -208.971], [-118.144, -218.558]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "<PERSON><PERSON>pe Layer 20", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.545, -51.273, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-26.25, -32], [-31, -22.25], [-31, -2.5], [-25, 17], [-10, 30.5], [-4.25, -17.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 20, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "rotate neck", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [609.417, 495.477, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "neck 6", "parent": 13, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-5.625, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [9.5, 0], [0, 0], [0, 0]], "v": [[-9.75, -30.5], [-11.375, -4.375], [0.75, 0.5], [15.25, -4.5], [17.75, -21.625]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "sh", "parent": 13, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [148.045, 118.727, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0.25], [0, 0], [0, 0]], "o": [[0, 14.502], [0, 0], [0, 0]], "v": [[-149, -144.25], [-127.375, -125.375], [-127.375, -133.625]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.949019607843, 0.607930800494, 0.424267548206, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "neck 4", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [0.603], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25.5, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38.5, "s": [0.603], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 51, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 64, "s": [0.603], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 76.5, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88.99, "s": [0.603], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 101, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 113.49, "s": [0.603], "e": [-0.397]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125.5, "s": [-0.397], "e": [0.603]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.99, "s": [0.603], "e": [-0.397]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [-2.653, 2.041, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [125, 125, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-5.625, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [9.5, 0], [0, 0], [0, 0]], "v": [[-9.75, -30.5], [-11.375, -4.375], [0.75, 0.5], [15.25, -4.5], [17.75, -21.625]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.513725490196, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "hair", "parent": 13, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 12.745, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 37.745, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 50, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 62.745, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 87.745, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 112.745, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.745, "s": [-3], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [26.076, -48.212, 0], "ix": 2}, "a": {"a": 0, "k": [9.28, 1.061, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[26, -6], [-5.25, 38.75], [26.25, 44.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.5, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}]}, {"id": "comp_4", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 150, "op": 181, "st": 150, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 120, "op": 151, "st": 120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 90, "op": 120, "st": 90, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 60, "op": 90, "st": 60, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 30, "op": 60, "st": 30, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "f3", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 30, "st": 0, "bm": 0}]}, {"id": "comp_5", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [923.5, 739.5, 0], "e": [936.843, 762.752, 0], "to": [6.44401264190674, 8.28515911102295, 0], "ti": [-2.63418841362, -7.4302830696106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 3, "s": [936.843, 762.752, 0], "e": [940.378, 781.06, 0], "to": [2.29588556289673, 6.47602891921997, 0], "ti": [-0.18238441646099, -5.31688356399536, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 5, "s": [940.378, 781.06, 0], "e": [928.989, 814.364, 0], "to": [0.52117866277695, 15.1934385299683, 0], "ti": [5.28653335571289, -6.2158350944519, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 9, "s": [928.989, 814.364, 0], "e": [922.705, 819.451, 0], "to": [-3.42243218421936, 4.02405023574829, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 10, "s": [922.705, 819.451, 0], "e": [913.271, 823.657, 0], "to": [0, 0, 0], "ti": [5.51117610931396, -1.33640682697296, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 11, "s": [913.271, 823.657, 0], "e": [893.885, 824.653, 0], "to": [-5.63313817977905, 1.36598134040833, 0], "ti": [6.46023893356323, 0.20196075737476, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 13, "s": [893.885, 824.653, 0], "e": [876.756, 817.605, 0], "to": [-8.20620059967041, -0.25654321908951, 0], "ti": [2.44176959991455, 0.82832980155945, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [876.756, 817.605, 0], "e": [861.562, 804.992, 0], "to": [-2.84095358848572, -0.96374636888504, 0], "ti": [6.07430553436279, 6.88331842422485, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 17, "s": [861.562, 804.992, 0], "e": [855.068, 796.053, 0], "to": [-2.25484848022461, -2.55516290664673, 0], "ti": [2.01216197013855, 2.67479276657104, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 18, "s": [855.068, 796.053, 0], "e": [848.866, 778.603, 0], "to": [-4.85461759567261, -6.45330572128296, 0], "ti": [-0.25908613204956, 4.81688117980957, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 20, "s": [848.866, 778.603, 0], "e": [851.388, 769.322, 0], "to": [-1.32184565067291, -3.32807850837708, 0], "ti": [-0.60235822200775, 3.32946515083313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 21, "s": [851.388, 769.322, 0], "e": [868.184, 744.437, 0], "to": [1.54719543457031, -8.55194282531738, 0], "ti": [-8.73117542266846, 6.7682638168335, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 24, "s": [868.184, 744.437, 0], "e": [885.453, 735.168, 0], "to": [5.17089986801147, -4.0083966255188, 0], "ti": [-6.15042924880981, 2.25092148780823, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 26, "s": [885.453, 735.168, 0], "e": [904.83, 732.714, 0], "to": [6.45584583282471, -2.36269736289978, 0], "ti": [-6.24136257171631, -0.1597348600626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 28, "s": [904.83, 732.714, 0], "e": [923.5, 739.5, 0], "to": [7.71555662155151, 0.19746382534504, 0], "ti": [-3.87464928627014, -5.58405351638794, 0]}, {"t": 30}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [360], "e": [360]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [360], "e": [320]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [320], "e": [360]}, {"t": 30}], "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [304, -338], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-75, 0], [-0.532, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 4", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 76.971, "ix": 10}, "p": {"a": 0, "k": [3.591, -11.095, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 23, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"t": 30}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Hose 1::Shoulder", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [826, 585, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 32, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape Layer 2", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [60], "e": [2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [2], "e": [-38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [-38], "e": [-96.25]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-96.25], "e": [-186.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-186.5], "e": [-244.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 25, "s": [-244.5], "e": [-300]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [0, -47, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [360]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [899, 810.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [7, 7], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 1", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 33, "ix": 10}, "p": {"a": 0, "k": [0.5, -0.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 55], "ix": 2}, "p": {"a": 0, "k": [0, -23], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "foot 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [-29.85], "e": [-16.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [-16.925], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 8, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [7], "e": [-3.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-3.259], "e": [-44.517]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-44.517], "e": [-51.85]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 23, "s": [-51.85], "e": [-46.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 26, "s": [-46.136], "e": [-33.85]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [-0.069, 11.397, 0], "ix": 2}, "a": {"a": 0, "k": [137.805, 154.981, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [4.087, 2.86], [0, 0], [7.084, -0.17]], "o": [[0, 0], [0, 0], [0, 0], [1.622, -4.718], [0, 0], [0, 0], [-6.696, -0.162]], "v": [[128.44, 154.618], [124.484, 167.78], [139.089, 173.19], [166.264, 182.565], [161.765, 167.473], [148.093, 155.019], [137.61, 149.237]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100.2, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "foot", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -10.85, "ix": 10}, "p": {"a": 0, "k": [-1.547, -2.758, 0], "ix": 2}, "a": {"a": 0, "k": [139, 141, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-7.148, -2.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.208, 1.267], [0, 0], [0, 0]], "v": [[130.646, 137.268], [129.617, 157.801], [137.177, 163.401], [146.021, 159.006], [147.729, 138.633]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_6", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 150, "op": 181, "st": 150, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 120, "op": 151, "st": 120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 90, "op": 120, "st": 90, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 60, "op": 90, "st": 60, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 30, "op": 60, "st": 30, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "f2", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 30, "st": 0, "bm": 0}]}, {"id": "comp_7", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "foot 2", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [-29.85], "e": [-16.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [-16.925], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 8, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [7], "e": [-3.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-3.259], "e": [-44.517]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-44.517], "e": [-51.85]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 23, "s": [-51.85], "e": [-46.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 26, "s": [-46.136], "e": [-33.85]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [-0.069, 11.397, 0], "ix": 2}, "a": {"a": 0, "k": [137.805, 154.981, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [4.087, 2.86], [0, 0], [7.084, -0.17]], "o": [[0, 0], [0, 0], [0, 0], [1.622, -4.718], [0, 0], [0, 0], [-6.696, -0.162]], "v": [[128.44, 154.618], [124.484, 167.78], [139.089, 173.19], [166.264, 182.565], [161.765, 167.473], [148.093, 155.019], [137.61, 149.237]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039215686, 0.098039215686, 0.16862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100.2, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "foot", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -10.85, "ix": 10}, "p": {"a": 0, "k": [-1.547, -2.758, 0], "ix": 2}, "a": {"a": 0, "k": [139, 141, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-7.148, -2.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.208, 1.267], [0, 0], [0, 0]], "v": [[130.646, 137.268], [129.617, 157.801], [137.177, 163.401], [146.021, 159.006], [147.729, 138.633]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039215686, 0.098039215686, 0.16862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [923.5, 739.5, 0], "e": [936.843, 762.752, 0], "to": [6.44401264190674, 8.28515911102295, 0], "ti": [-2.63418841362, -7.4302830696106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 3, "s": [936.843, 762.752, 0], "e": [940.378, 781.06, 0], "to": [2.29588556289673, 6.47602891921997, 0], "ti": [-0.18238441646099, -5.31688356399536, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 5, "s": [940.378, 781.06, 0], "e": [928.989, 814.364, 0], "to": [0.52117866277695, 15.1934385299683, 0], "ti": [5.28653335571289, -6.2158350944519, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 9, "s": [928.989, 814.364, 0], "e": [922.705, 819.451, 0], "to": [-3.42243218421936, 4.02405023574829, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 10, "s": [922.705, 819.451, 0], "e": [913.271, 823.657, 0], "to": [0, 0, 0], "ti": [5.51117610931396, -1.33640682697296, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 11, "s": [913.271, 823.657, 0], "e": [893.885, 824.653, 0], "to": [-5.63313817977905, 1.36598134040833, 0], "ti": [6.46023893356323, 0.20196075737476, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 13, "s": [893.885, 824.653, 0], "e": [876.756, 817.605, 0], "to": [-8.20620059967041, -0.25654321908951, 0], "ti": [2.44176959991455, 0.82832980155945, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [876.756, 817.605, 0], "e": [861.562, 804.992, 0], "to": [-2.84095358848572, -0.96374636888504, 0], "ti": [6.07430553436279, 6.88331842422485, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 17, "s": [861.562, 804.992, 0], "e": [855.068, 796.053, 0], "to": [-2.25484848022461, -2.55516290664673, 0], "ti": [2.01216197013855, 2.67479276657104, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 18, "s": [855.068, 796.053, 0], "e": [848.866, 778.603, 0], "to": [-4.85461759567261, -6.45330572128296, 0], "ti": [-0.25908613204956, 4.81688117980957, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 20, "s": [848.866, 778.603, 0], "e": [851.388, 769.322, 0], "to": [-1.32184565067291, -3.32807850837708, 0], "ti": [-0.60235822200775, 3.32946515083313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 21, "s": [851.388, 769.322, 0], "e": [868.184, 744.437, 0], "to": [1.54719543457031, -8.55194282531738, 0], "ti": [-8.73117542266846, 6.7682638168335, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 24, "s": [868.184, 744.437, 0], "e": [885.453, 735.168, 0], "to": [5.17089986801147, -4.0083966255188, 0], "ti": [-6.15042924880981, 2.25092148780823, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 26, "s": [885.453, 735.168, 0], "e": [904.83, 732.714, 0], "to": [6.45584583282471, -2.36269736289978, 0], "ti": [-6.24136257171631, -0.1597348600626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 28, "s": [904.83, 732.714, 0], "e": [923.5, 739.5, 0], "to": [7.71555662155151, 0.19746382534504, 0], "ti": [-3.87464928627014, -5.58405351638794, 0]}, {"t": 30}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [360], "e": [360]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [360], "e": [320]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [320], "e": [360]}, {"t": 30}], "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [304, -338], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-75, 0], [-0.532, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 4", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 76.971, "ix": 10}, "p": {"a": 0, "k": [3.591, -11.095, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 23, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"t": 30}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039215686, 0.098039215686, 0.16862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Hose 1::Shoulder", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [826, 585, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.098039215686, 0.098039215686, 0.16862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 30, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 2", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [60], "e": [2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [2], "e": [-38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [-38], "e": [-96.25]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-96.25], "e": [-186.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-186.5], "e": [-244.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 25, "s": [-244.5], "e": [-300]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [0, -47, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [360]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [899, 810.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [7, 7], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Shape Layer 1", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 33, "ix": 10}, "p": {"a": 0, "k": [0.5, -0.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 55], "ix": 2}, "p": {"a": 0, "k": [0, -23], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_8", "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 150, "op": 181, "st": 150, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 120, "op": 151, "st": 120, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 90, "op": 120, "st": 90, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 60, "op": 90, "st": 60, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 30, "op": 60, "st": 30, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "f4", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 30, "st": 0, "bm": 0}]}, {"id": "comp_9", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [923.5, 739.5, 0], "e": [936.843, 762.752, 0], "to": [6.44401264190674, 8.28515911102295, 0], "ti": [-2.63418841362, -7.4302830696106, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 3, "s": [936.843, 762.752, 0], "e": [940.378, 781.06, 0], "to": [2.29588556289673, 6.47602891921997, 0], "ti": [-0.18238441646099, -5.31688356399536, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 5, "s": [940.378, 781.06, 0], "e": [928.989, 814.364, 0], "to": [0.52117866277695, 15.1934385299683, 0], "ti": [5.28653335571289, -6.2158350944519, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 9, "s": [928.989, 814.364, 0], "e": [922.705, 819.451, 0], "to": [-3.42243218421936, 4.02405023574829, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 10, "s": [922.705, 819.451, 0], "e": [913.271, 823.657, 0], "to": [0, 0, 0], "ti": [5.51117610931396, -1.33640682697296, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 11, "s": [913.271, 823.657, 0], "e": [893.885, 824.653, 0], "to": [-5.63313817977905, 1.36598134040833, 0], "ti": [6.46023893356323, 0.20196075737476, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 13, "s": [893.885, 824.653, 0], "e": [876.756, 817.605, 0], "to": [-8.20620059967041, -0.25654321908951, 0], "ti": [2.44176959991455, 0.82832980155945, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [876.756, 817.605, 0], "e": [861.562, 804.992, 0], "to": [-2.84095358848572, -0.96374636888504, 0], "ti": [6.07430553436279, 6.88331842422485, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 17, "s": [861.562, 804.992, 0], "e": [855.068, 796.053, 0], "to": [-2.25484848022461, -2.55516290664673, 0], "ti": [2.01216197013855, 2.67479276657104, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 18, "s": [855.068, 796.053, 0], "e": [848.866, 778.603, 0], "to": [-4.85461759567261, -6.45330572128296, 0], "ti": [-0.25908613204956, 4.81688117980957, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 20, "s": [848.866, 778.603, 0], "e": [851.388, 769.322, 0], "to": [-1.32184565067291, -3.32807850837708, 0], "ti": [-0.60235822200775, 3.32946515083313, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 21, "s": [851.388, 769.322, 0], "e": [868.184, 744.437, 0], "to": [1.54719543457031, -8.55194282531738, 0], "ti": [-8.73117542266846, 6.7682638168335, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 24, "s": [868.184, 744.437, 0], "e": [885.453, 735.168, 0], "to": [5.17089986801147, -4.0083966255188, 0], "ti": [-6.15042924880981, 2.25092148780823, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 26, "s": [885.453, 735.168, 0], "e": [904.83, 732.714, 0], "to": [6.45584583282471, -2.36269736289978, 0], "ti": [-6.24136257171631, -0.1597348600626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 28, "s": [904.83, 732.714, 0], "e": [923.5, 739.5, 0], "to": [7.71555662155151, 0.19746382534504, 0], "ti": [-3.87464928627014, -5.58405351638794, 0]}, {"t": 30}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [360], "e": [360]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [360], "e": [320]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [320], "e": [360]}, {"t": 30}], "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [304, -338], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-75, 0], [-0.532, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 4", "parent": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 76.971, "ix": 10}, "p": {"a": 0, "k": [3.591, -11.095, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 0, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 23, "s": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[13.331, -28.582], [127.528, 16.552], [136.629, 36.379], [121.528, 47.552], [-0.107, 32.688], [-19.925, -0.729]], "c": true}], "e": [{"i": [[-12.822, -1.865], [-41.586, -21.119], [1.23, -6.353], [6.264, 0.285], [43.108, 15.461], [-3.45, 15.988]], "o": [[46.155, 6.714], [5.769, 2.93], [-1.191, 6.156], [-45.75, -2.081], [-11.408, -4.091], [3.811, -17.658]], "v": [[14.438, -30.32], [137.846, 19.848], [146.947, 39.675], [131.847, 50.848], [1, 30.95], [-18.818, -2.466]], "c": true}]}, {"t": 30}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.047058823529, 0.749019607843, 0.819607843137, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Hose 1::Shoulder", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [826, 585, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.047058823529, 0.749019607843, 0.819607843137, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 32, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Shape Layer 2", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [60], "e": [2]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [2], "e": [-38]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [-38], "e": [-96.25]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-96.25], "e": [-186.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-186.5], "e": [-244.5]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 25, "s": [-244.5], "e": [-300]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [0, -47, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 29], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [360]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [899, 810.125, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [7, 7], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 1", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 33, "ix": 10}, "p": {"a": 0, "k": [0.5, -0.625, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7, 55], "ix": 2}, "p": {"a": 0, "k": [0, -23], "ix": 3}, "r": {"a": 0, "k": 76, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745098039, 0.760784313725, 0.862745098039, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "foot 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [-29.85], "e": [-16.925]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 5, "s": [-16.925], "e": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 8, "s": [0], "e": [7]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 10, "s": [7], "e": [-3.259]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [-3.259], "e": [-44.517]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 20, "s": [-44.517], "e": [-51.85]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 23, "s": [-51.85], "e": [-46.136]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 26, "s": [-46.136], "e": [-33.85]}, {"t": 30}], "ix": 10}, "p": {"a": 0, "k": [-0.069, 11.397, 0], "ix": 2}, "a": {"a": 0, "k": [137.805, 154.981, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [4.087, 2.86], [0, 0], [7.084, -0.17]], "o": [[0, 0], [0, 0], [0, 0], [1.622, -4.718], [0, 0], [0, 0], [-6.696, -0.162]], "v": [[128.44, 154.618], [124.484, 167.78], [139.089, 173.19], [166.264, 182.565], [161.765, 167.473], [148.093, 155.019], [137.61, 149.237]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100.2, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "foot", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -10.85, "ix": 10}, "p": {"a": 0, "k": [-1.547, -2.758, 0], "ix": 2}, "a": {"a": 0, "k": [139, 141, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-7.148, -2.03], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [6.208, 1.267], [0, 0], [0, 0]], "v": [[130.646, 137.268], [129.617, 157.801], [137.177, 163.401], [146.021, 159.006], [147.729, 138.633]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_10", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1600, 1200], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.4, 0.4, 0.4, 0.499, 0.498, 0.498, 0.498, 0.998, 0.596, 0.596, 0.596, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-792, 24], "ix": 5}, "e": {"a": 0, "k": [-492, 24], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Shape Layer 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1600, 1200], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "gf", "o": {"a": 0, "k": 100, "ix": 10}, "r": 1, "g": {"p": 3, "k": {"a": 0, "k": [0, 0.4, 0.4, 0.4, 0.499, 0.498, 0.498, 0.498, 0.998, 0.596, 0.596, 0.596, 0, 1, 0.5, 0.5, 1, 0], "ix": 9}}, "s": {"a": 0, "k": [-792, 24], "ix": 5}, "e": {"a": 0, "k": [-492, 24], "ix": 6}, "t": 1, "nm": "Gradient Fill 1", "mn": "ADBE Vector Graphic - G-Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_11", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Null 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [466, 940, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 32", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [334, -340, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.744], [-0.01, -0.355], [0, 0], [-0.863, -1.852], [0, 0], [0.077, 2.867], [0, 0]], "o": [[-0.239, 1.679], [0, 0.357], [0, 0], [0.544, 2.005], [0, 0], [-0.728, -2.658], [0, 0], [0, 0]], "v": [[-353.856, 205.049], [-354.219, 210.189], [-354.203, 211.257], [-352.956, 219.706], [-350.836, 225.499], [-351.729, 219.452], [-352.956, 211.143], [-353.856, 205.049]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 18", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [334, -340, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-364.518, 137.867], [-450.652, 112.715], [-450.302, 111.516], [-364.167, 136.669]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 17", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [334, -340, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-357.882, 177.009], [-358.85, 176.219], [-309.045, 115.258], [-308.077, 116.048], [-357.882, 177.009]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 16", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-2, 0, 0], "ix": 2}, "a": {"a": 0, "k": [-336, 340, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-335.409, 338.526], [-375.253, 68.71], [-374.017, 68.527], [-334.172, 338.342], [-335.409, 338.526]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 15", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [334, -340, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [45.499, 45.499], [-45.498, 45.499], [-45.499, -45.498], [45.499, -45.499]], "o": [[-45.499, 45.499], [-45.498, -45.499], [45.499, -45.498], [45.499, 45.499], [0, 0]], "v": [[-278.933, 229.303], [-443.7, 229.303], [-443.7, 64.536], [-278.933, 64.536], [-278.933, 229.303]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.78823530674, 0.611764729023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_12", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-193.897, 58.632], [-268.933, 30.868], [-268.5, 29.696], [-193.464, 57.46]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-199.362, 105.874], [-200.103, 104.868], [-112.113, 40.143], [-111.372, 41.149]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-228.916, 338.513], [-230.156, 338.354], [-187.729, 6.572], [-186.488, 6.731]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -24.545], [-69.321, 0], [-8.112, 1.647]], "o": [[-12.178, 19.342], [0, 69.321], [8.599, 0], [0, 0]], "v": [[-297.098, -17.731], [-316.328, 49.05], [-190.812, 174.567], [-165.705, 172.053]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.015686275437, 0.658823549747, 0.490196079016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -69.32], [69.32, 0], [0, 69.321], [-69.321, 0]], "o": [[0, 69.321], [-69.321, 0], [0, -69.32], [69.32, 0]], "v": [[-65.296, 49.05], [-190.812, 174.567], [-316.328, 49.05], [-190.812, -76.465]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.72549021244, 0.560784339905, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_13", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[410.238, 97.708], [393.258, 80.728], [394.142, 79.844], [411.121, 96.824]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[403.472, 118.577], [403.042, 117.402], [453.91, 98.798], [454.34, 99.972]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[385.12, 170.485], [383.944, 170.062], [419.619, 70.452], [420.796, 70.874]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-14.136, -34.897], [-34.897, 14.137], [14.136, 34.897], [34.897, -14.136]], "o": [[14.136, 34.896], [34.896, -14.136], [-14.136, -34.896], [-34.896, 14.137]], "v": [[348.465, 132.621], [437.248, 170.21], [474.837, 81.428], [386.055, 43.838]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.78823530674, 0.611764729023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_14", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 41", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.068, -0.488], [0, 0], [-0.053, 0.488]], "o": [[0, 0], [-0.058, 0.491], [0, 0], [0.062, -0.485], [0, 0]], "v": [[374.167, 218.554], [372.839, 219.174], [372.65, 220.642], [373.994, 220.015], [374.167, 218.554]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 40", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.28, -0.799], [0, 0], [0, 0], [0.312, 0.742]], "o": [[0, 0], [0.306, 0.786], [0, 0], [0, 0], [-0.289, -0.753], [0, 0]], "v": [[368.634, 180.448], [368.246, 182.819], [369.126, 185.195], [369.151, 185.041], [369.536, 182.688], [368.634, 180.448]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0.54, -0.649], [0, 0], [-0.513, 0.622], [0, 0]], "o": [[-0.519, 0.667], [0, 0], [0.532, -0.605], [0, 0], [0, 0]], "v": [[357.473, 256.529], [355.883, 258.505], [355.495, 260.88], [357.063, 259.038], [357.473, 256.529]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 39", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-3.82, -9.806], [0, 0], [8.6, 7.608]], "o": [[0, 0], [7.3, 7.335], [0, 0], [-4.52, -10.733], [0, 0]], "v": [[348.623, 152.603], [351.316, 156.857], [368.246, 182.819], [368.634, 180.448], [348.623, 152.603]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.79, 2.057]], "o": [[0, 0], [0, 0], [-0.614, -2.137], [0, 0]], "v": [[369.536, 182.688], [369.151, 185.041], [371.644, 188.98], [369.536, 182.688]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.043137256056, 0.596078455448, 0.482352942228, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 38", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.614, -0.523], [0.402, -0.488], [-4.182, 0], [-13.705, -13.769], [0, 0], [19.497, 0]], "o": [[-3.757, 0], [-0.411, 0.48], [3.999, -0.656], [20.916, 0], [0, 0], [-13.567, -12.003], [0, 0]], "v": [[297.736, 133.306], [286.666, 134.101], [285.448, 135.553], [297.736, 134.555], [351.316, 156.857], [348.623, 152.603], [297.736, 133.306]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -8.754], [0.35, -2.947], [0, 0], [0, 2.825], [1.937, 6.739], [0, 0]], "o": [[0, 0], [2.748, 7.827], [0, 3.039], [0, 0], [0.298, -2.747], [0, -7.354], [0, 0], [0, 0]], "v": [[369.151, 185.041], [369.126, 185.195], [373.37, 210.189], [372.839, 219.174], [374.167, 218.554], [374.619, 210.189], [371.644, 188.98], [369.151, 185.041]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [7.936, -10.207], [0, 0], [-1.883, 14.703]], "o": [[0, 0], [-1.86, 13.397], [0, 0], [8.995, -10.905], [0, 0]], "v": [[373.994, 220.015], [372.65, 220.642], [357.473, 256.529], [357.063, 259.038], [373.994, 220.015]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [17.287, -3.444], [-0.555, -0.306], [-10.639, 12.108], [0, 0]], "o": [[-10.872, 13.062], [0.548, 0.316], [16.292, -3.566], [0, 0], [0, 0]], "v": [[355.883, 258.505], [312.54, 284.367], [314.191, 285.299], [355.495, 260.88], [355.883, 258.505]], "c": false}, "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.027450980619, 0.654901981354, 0.54509806633, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[365.951, 198.983], [307.326, 155.669], [308.068, 154.663], [366.693, 197.977]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[362.266, 225.49], [361.738, 224.356], [434.27, 190.496], [434.799, 191.628], [362.266, 225.49]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[344.076, 338.535], [342.843, 338.332], [373.377, 151.412], [374.612, 151.615], [344.076, 338.535]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [51.247, 0], [12.692, -5.907], [0, 0]], "o": [[0, 0], [-5.158, -49.883], [-14.922, 0], [0, 0], [0, 0]], "v": [[385.066, 210.189], [461.098, 188.169], [362.537, 99.385], [320.783, 108.611], [385.066, 210.189]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [-54.728, 0], [0, 54.73], [54.729, 0], [0, -54.728]], "o": [[0, 54.73], [54.729, 0], [0, -54.728], [-54.728, 0], [0, 0]], "v": [[262.907, 198.48], [362.002, 297.575], [461.098, 198.48], [362.002, 99.385], [262.907, 198.48]], "c": false}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.015686275437, 0.658823549747, 0.490196079016, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-54.728, 0], [0, 54.73], [54.729, 0], [0, -54.728]], "o": [[0, 54.73], [54.729, 0], [0, -54.728], [-54.728, 0], [0, 0]], "v": [[262.907, 198.48], [362.002, 297.575], [461.098, 198.48], [362.002, 99.385], [262.907, 198.48]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.72549021244, 0.560784339905, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "LINE", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[420.207, 339.059], [-435.016, 339.059], [-435.016, 337.809], [420.207, 337.809]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 104", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [9.78, 0.712], [0, 0], [-14.827, -22.877], [-6.602, -3.019]], "o": [[0, 0], [-9.5, -2.388], [0, 0], [0, 0], [2.283, 3.522], [0, 0]], "v": [[52.097, -17.344], [94.782, -24.709], [65.81, -29.371], [-1.81, -34.287], [8.98, 1.582], [22.738, 11.395]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152941182256, 0.203921571374, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 100", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-14.827, -22.877], [0, 0], [0, 0], [0, 0], [2.629, 26.627], [29.827, 8.915], [12.22, 0.889]], "o": [[0, 0], [14.827, 22.877], [0, 0], [0, 0], [0, 0], [-1.902, -19.266], [-11.74, -3.508], [0, 0]], "v": [[-1.81, -34.287], [8.98, 1.582], [147.697, 36.876], [128.382, 139.008], [150.675, 144.989], [187.805, 24.191], [101.86, -22.778], [65.81, -29.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [945.5, 547.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 250, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Hose 1::Shoulder", "parent": 15, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [56, -182, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.941176470588, 0.945098039216, 0.956862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "h1", "parent": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 65.11, "ix": 10}, "p": {"a": 0, "k": [-1.506, -2.057, 0], "ix": 2}, "a": {"a": 0, "k": [143, -52, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.49, -6.309], [3.154, -3.486], [2.656, 0.83], [2.158, 2.712], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [2.49, 6.309], [-3.154, 3.486], [-2.656, -0.83], [-2.159, -2.712], [0, 0], [0, 0], [0, 0]], "v": [[148.819, -59.546], [159.942, -51.411], [156.455, -32.651], [141.679, -31.323], [145.498, -43.386], [141.347, -46.099], [136.554, -46.099], [134.499, -50.706]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "head", "parent": 11, "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [0], "e": [2]}, {"i": {"x": [0.667], "y": [0.929]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_0p929_0p333_0"], "t": 37, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [-0.071]}, "n": ["0p667_1_0p333_-0p071"], "t": 50, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75.001, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 87.5, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 112, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.5, "s": [1], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [93, -234, 0], "ix": 2}, "a": {"a": 0, "k": [102.333, 189.667, 0], "ix": 1}, "s": {"a": 0, "k": [75, 75, 100], "ix": 6}}, "ao": 0, "w": 250, "h": 250, "ip": 0, "op": 1800, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "neck 2", "parent": 11, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [97, -206, 0], "ix": 2}, "a": {"a": 0, "k": [97, -206, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-12.268, 0.71], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [11.346, -0.658], [0, 0], [0, 0], [0, 0]], "v": [[80.032, -253.29], [80.032, -211.772], [99.269, -206.559], [112.421, -211.327], [117.112, -232.859], [80.032, -253.29]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "sh", "parent": 8, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-21.667, 501.667, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [133.333, 133.333, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [5.265, 4.184], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.921, -1.133], [-9.714, -7.723], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[113.212, -214.962], [100.645, -222.466], [88.409, -239.701], [113.287, -234.967], [117.112, -232.859], [113.212, -214.962]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.552941203117, 0.360784322023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "neck", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [0], "e": [2]}, {"i": {"x": [0.667], "y": [0.905]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_0p905_0p333_0"], "t": 37, "s": [2], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [-0.095]}, "n": ["0p667_1_0p333_-0p095"], "t": 50, "s": [-1], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75.001, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 87.5, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 112, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.5, "s": [1], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [97, -206, 0], "ix": 2}, "a": {"a": 0, "k": [97, -206, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-12.268, 0.71], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [11.346, -0.658], [0, 0], [0, 0], [0, 0]], "v": [[80.032, -253.29], [80.032, -211.772], [99.269, -206.559], [112.421, -211.327], [117.112, -232.859], [80.032, -253.29]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "t-shirt", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.896, -0.498], [0, 0]], "o": [[0, 0], [11.896, 0.498], [0, 0]], "v": [[80.032, -211.772], [98.572, -197.507], [112.42, -211.327]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "body_grey 2", "parent": 15, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, -19, 0], "ix": 2}, "a": {"a": 0, "k": [46, -19, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [8.643, -15.304], [6.166, -71.182], [-48.261, 10.534], [-18.076, 31.211], [0, 0]], "o": [[0, 0], [-16.6, 5.778], [-13.5, 23.906], [0, 0], [0, 0], [18.076, -31.212], [0, 0]], "v": [[80.032, -211.772], [68.205, -207.656], [29.043, -174.951], [-7.405, -36.968], [89.761, -30.034], [124.931, -171.442], [112.42, -211.327]], "c": true}, "ix": 2, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('body_grey').content('Group 1').content('Path 1').path;"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941176474094, 0.945098042488, 0.956862747669, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "sh_body", "parent": 15, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.979, -3.503], [6.166, -71.182], [-23.605, -6.381]], "o": [[0, 0], [-2.501, 3.092], [-13.5, 23.906], [0, 0], [0, 0]], "v": [[63.914, -123.23], [35.791, -184.847], [29.044, -174.951], [-7.405, -36.968], [34.109, -21.295]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.886274516582, 0.886274516582, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "body_grey", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 12.5, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 50, "s": [0], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75, "s": [-2], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88, "s": [1], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [-2], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 112, "s": [3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 137.5, "s": [1], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [846, 581, 0], "ix": 2}, "a": {"a": 0, "k": [46, -19, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [8.643, -15.304], [6.166, -71.182], [-48.261, 10.534], [-18.076, 31.211], [0, 0]], "o": [[0, 0], [-16.6, 5.778], [-13.5, 23.906], [0, 0], [0, 0], [18.076, -31.212], [0, 0]], "v": [[80.032, -211.772], [68.205, -207.656], [29.043, -174.951], [-7.405, -36.968], [89.699, -34.034], [124.931, -171.442], [112.42, -211.327]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941176474094, 0.945098042488, 0.956862747669, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Shape Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [8.5, -26.5], [0, 0], [-23.5, 6], [0, 0]], "o": [[0, 0], [-8.5, 26.5], [0, 0], [29.5, -2.5], [0, 0]], "v": [[62.5, -48], [-5, -43], [4.75, 1.25], [52.5, 14.5], [88, -32.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 0, "nm": "footcycle1", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1111, 516], [738, 516], [738, 900], [1111, 900]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 151, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Hose 3::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [745.5, 538.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 260, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "Shape Layer 4", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 13.557, "ix": 10}, "p": {"a": 0, "k": [1.943, 3.358, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [30, 30], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Hose 3::Shoulder", "parent": 26, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [-165.504, -178.998, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Hose 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 26, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 1, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 0, "nm": "whead", "parent": 26, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [2], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [-1], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38, "s": [2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 50, "s": [0], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [3], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 113, "s": [1], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [-2], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 138, "s": [1], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [-121, -196, 0], "ix": 2}, "a": {"a": 0, "k": [609, 496, 0], "ix": 1}, "s": {"a": 0, "k": [93, 93, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[687, 345], [558, 345], [558, 516], [687, 516]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 370.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "Layer 87", "parent": 26, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-11.829, 0.498], [0, 0]], "o": [[0, 0], [11.829, -0.498], [0, 0]], "v": [[-136.983, -200.504], [-116.146, -185.222], [-104.844, -199.078]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "hand", "parent": 18, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 76.94, "ix": 10}, "p": {"a": 0, "k": [-0.304, -3.522, 0], "ix": 2}, "a": {"a": 0, "k": [-58, -62, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.996, -3.652], [-0.694, -3.984], [-8.813, 2.324], [1.328, 7.305], [0, 0]], "o": [[0, 0], [0.996, 3.652], [0.694, 3.984], [8.814, -2.324], [-1.328, -7.305], [0, 0]], "v": [[-60.613, -56.35], [-54.886, -51.121], [-59.232, -35.972], [-45.437, -30.327], [-32.639, -50.249], [-52.561, -69.839]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "sh", "parent": 26, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-192, -39, 0], "ix": 2}, "a": {"a": 0, "k": [-192, -39, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-36.975, -4.315], [0, 0], [1.703, -1.36]], "o": [[0, 0], [0, 0], [0, 0], [-51.663, 41.261]], "v": [[-246.802, -39.167], [-194.306, -37.991], [-149.887, -125.645], [-172.188, -186.116]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.929411768913, 0.713725507259, 0.027450980619, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "body_yellow", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 13, "s": [1], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 25, "s": [-2], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 38, "s": [3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 50, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [1], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 75, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 88, "s": [2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 100, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 113, "s": [1], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 125, "s": [-2], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 138, "s": [1], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [606, 561, 0], "ix": 2}, "a": {"a": 0, "k": [-194, -39, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [16.629, -13.281], [0, 0], [-41.599, -5.474], [0, 0], [0.504, 13.221], [0, 0]], "o": [[0, 0], [-51.663, 41.261], [0, 0], [42.392, 5.577], [0, 0], [-0.505, -13.22], [0, 0]], "v": [[-135.606, -200.25], [-169.443, -190.369], [-246.802, -39.167], [-187.915, -37.2], [-142.542, -32.983], [-92.087, -183.621], [-104.844, -199.078]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470589638, 0.760784327984, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Hose 4::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [804, 534, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 240, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Hose 4::Shoulder", "parent": 26, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [-107, -180, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Hose 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.972549019608, 0.6, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 28, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 4::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "Layer 60", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -11.027], [11.027, 0], [0, 11.027], [-11.027, 0]], "o": [[0, 11.027], [-11.027, 0], [0, -11.027], [11.027, 0]], "v": [[119.477, 210.189], [99.511, 230.155], [79.546, 210.189], [99.511, 190.223]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.168627455831, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "Shape Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [574, 575, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.416, -5.661], [0, 0]], "o": [[0, 0], [0, 0], [0.424, 5.77], [0, 0]], "v": [[26, -17], [-19, -15], [-20, 2], [-16.5, 19]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.972549019608, 0.6, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 28, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.047058823529, 0.749019607843, 0.819607843137, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [577.5, 582.5, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-35.5, 79]], "o": [[0, 0], [0, 0], [19.586, -43.585]], "v": [[1.5, -33.5], [-11.5, 19], [79.5, -16]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.75, -17], [0, 0]], "o": [[0, 0], [-8.28, 29.635], [0, 0]], "v": [[34.75, -23.5], [-21, -25.5], [-14, 19.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.047058823529, 0.749019607843, 0.819607843137, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 0, "nm": "footcycle3", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [559, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1023, 535], [749, 535], [749, 880], [1023, 880]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "Layer 59", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -11.027], [11.027, 0], [0, 11.027], [-11.027, 0]], "o": [[0, 11.027], [-11.027, 0], [0, -11.027], [11.027, 0]], "v": [[-123.573, 210.189], [-143.539, 230.155], [-163.504, 210.189], [-143.539, 190.223]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.168627455831, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "Layer 58", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -7.596], [7.596, 0], [0, 7.595], [-7.596, 0]], "o": [[0, 7.595], [-7.596, 0], [0, -7.596], [7.596, 0]], "v": [[311.49, 210.189], [297.736, 223.943], [283.983, 210.189], [297.736, 196.436]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "Layer 57", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.721, 0.272], [1.357, -6.771], [-0.676, -2.068], [-10.986, 4.943], [-7.38, 3.879], [19.05, 5.243]], "o": [[-6.904, -0.176], [-0.407, 2.035], [2.704, 8.267], [10.985, -4.944], [7.38, -3.878], [-12.813, -3.525]], "v": [[-0.168, -5.45], [-14.718, 5.785], [-14.468, 12.018], [20.731, 15.417], [54.56, 12.268], [41.839, -0.658]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.168627455831, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "Layer 56", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[10.721, 0.272], [1.357, -6.771], [-0.676, -2.068], [-10.986, 4.943], [-7.38, 3.879], [19.05, 5.243]], "o": [[-6.904, -0.176], [-0.408, 2.035], [2.704, 8.267], [10.985, -4.944], [7.38, -3.878], [-12.813, -3.525]], "v": [[-236.808, -5.45], [-251.357, 5.785], [-251.107, 12.018], [-215.909, 15.417], [-182.08, 12.268], [-194.8, -0.658]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.098039217293, 0.168627455831, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "Layer 54", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[290.933, 213.278], [195.764, 3.64], [209.369, -2.535], [304.539, 207.101]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "Layer 53", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -19.738], [19.736, 0], [0, 19.736], [-19.737, 0]], "o": [[0, 19.736], [-19.737, 0], [0, -19.738], [19.736, 0]], "v": [[-107.802, 210.189], [-143.539, 245.925], [-179.275, 210.189], [-143.539, 174.452]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "Layer 52", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -19.738], [19.736, 0], [0, 19.736], [-19.737, 0]], "o": [[0, 19.736], [-19.737, 0], [0, -19.738], [19.736, 0]], "v": [[135.247, 210.189], [99.511, 245.925], [63.775, 210.189], [99.511, 174.452]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.263529429716, 0.316672590667, 0.533333333333, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "Layer 51", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[103.031, 217.66], [-148.78, 217.66], [-214.771, 35.367], [-200.722, 30.282], [-138.298, 202.719], [95.991, 202.719], [222.078, 49.99], [233.6, 59.502]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "Layer 50", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -13.789], [13.788, 0], [0, 13.788], [-13.788, 0]], "o": [[0, 13.788], [-13.788, 0], [0, -13.789], [13.788, 0]], "v": [[-292.892, 210.189], [-317.857, 235.155], [-342.823, 210.189], [-317.857, 185.223]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "Wheel circle", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[59.476, 0], [0, 59.476], [-59.476, 0], [0, -59.475]], "o": [[-59.476, 0], [0, -59.475], [59.476, 0], [0, 59.476]], "v": [[297.736, 319.08], [190.045, 211.389], [297.736, 103.699], [405.427, 211.389]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[70.165, 0], [0, -70.165], [-70.165, 0], [0, 70.165]], "o": [[-70.165, 0], [0, 70.165], [70.165, 0], [0, -70.165]], "v": [[297.736, 84.344], [170.691, 211.389], [297.736, 338.434], [424.781, 211.389]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "wh 2", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [3600]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [481, 810, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [145, 145], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "wh", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 0, "s": [0], "e": [3600]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [1098, 810, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [145, 145], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "w", "parent": 45, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-298, -210, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -38.636], [-42.394, 0], [-5.303, 1.161], [0.548, 0.316], [5.065, 0], [0, 41.705], [-35.883, 5.886], [-0.411, 0.48]], "o": [[-37.169, 5.381], [0, 42.393], [5.646, 0], [-0.555, -0.306], [-4.789, 0.954], [-41.705, 0], [0, -37.522], [0.402, -0.488], [0, 0]], "v": [[286.666, 134.101], [220.853, 210.189], [297.736, 287.072], [314.191, 285.299], [312.54, 284.367], [297.736, 285.822], [222.103, 210.189], [285.448, 135.553], [286.666, 134.101]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039215686, 0.098039215686, 0.16862745098, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "Layer 105", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[44.617, 67.181], [102.425, 217.766], [88.938, 222.936], [29.131, 65.852]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 48, "ty": 0, "nm": "footcycle2", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 604, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[999, 550], [763, 550], [763, 870], [999, 870]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 151, "st": -15, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "Layer 48", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36.617, 47.181], [102.425, 217.766], [88.938, 222.936], [21.131, 45.852]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 50, "ty": 4, "nm": "Layer 47", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[220.768, 47.867], [-202.807, 47.867], [-202.807, 33.424], [220.768, 33.424]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 51, "ty": 4, "nm": "Layer 46", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-143.539, 215.374], [-325.274, 215.374], [-208.121, 38.446], [-201.477, 42.845], [-310.44, 207.405], [-143.539, 207.405]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.941176474094, 0.556862771511, 0.592156887054, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 52, "ty": 4, "nm": "Layer 45", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[59.476, 0], [0, 59.476], [-59.476, 0], [0, -59.475]], "o": [[-59.476, 0], [0, -59.475], [59.476, 0], [0, 59.476]], "v": [[-317.857, 319.08], [-425.548, 211.389], [-317.857, 103.699], [-210.167, 211.389]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[70.165, 0], [0, -70.165], [-70.165, 0], [0, 70.165]], "o": [[-70.165, 0], [0, 70.165], [70.165, 0], [0, -70.165]], "v": [[-317.857, 84.344], [-444.903, 211.389], [-317.857, 338.434], [-190.812, 211.389]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 53, "ty": 4, "nm": "Layer 44", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-209.768, 11.926], [-158.445, 152.653], [-168.868, 156.454], [-220.192, 15.728]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 54, "ty": 4, "nm": "Layer 43", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.862, -18.872], [75.604, 152.137], [65.245, 156.11], [-0.498, -14.9]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 55, "ty": 0, "nm": "footcycle4", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [560, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1011, 535], [748, 535], [748, 886], [1011, 886]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 151, "st": -15, "bm": 0}, {"ddd": 0, "ind": 56, "ty": 4, "nm": "Layer 42", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[262.635, 146.92], [187.805, -17.911], [198.502, -22.767], [273.332, 142.063]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 57, "ty": 4, "nm": "Layer 29", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.648, -24.572], [0, 0], [0, 0], [0, 0], [22.059, 19.923]], "o": [[0, 0], [4.648, 24.57], [0, 0], [0, 0], [0, 0], [-22.06, -19.922]], "v": [[-163.461, -36.968], [-86.097, 33.424], [-162.963, 217.66], [-187.109, 205.75], [-131.585, 61.66], [-209.468, 2.544]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.04705882445, 0.749019622803, 0.819607853889, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 58, "ty": 4, "nm": "Layer 27", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.723, -2.723], [-4.819, -1.81], [-1.957, 2.782], [1.348, 6.315], [0, 0]], "o": [[0, 0], [3.724, 2.722], [4.819, 1.81], [1.957, -2.782], [-1.348, -6.316], [0, 0]], "v": [[2.342, -51.869], [16.15, -46.598], [19.913, -28.158], [33.618, -29.952], [33.695, -51.622], [6.092, -65.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 59, "ty": 4, "nm": "Layer 26", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 3.026], [-3.025, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -3.026], [3.025, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-3.025, 0], [0, -3.026], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.025, 0], [0, 3.026], [0, 0], [0, 0]], "v": [[18.414, -14.556], [-41.498, -14.556], [-46.431, -35.142], [-65.882, -35.142], [-71.36, -40.62], [-65.882, -46.099], [-37.789, -46.099], [-32.856, -25.513], [9.492, -25.513], [13.753, -46.099], [30.324, -46.099], [35.802, -40.62], [30.324, -35.142], [22.675, -35.142]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 60, "ty": 4, "nm": "Velostep", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [901, 811, 0], "ix": 2}, "a": {"a": 0, "k": [101, 211, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.601, 1.619], [-1.619, -0.601], [0, 0], [0, 0], [-1.381, -1.041], [1.038, -1.378]], "o": [[0, 0], [-1.618, -0.601], [0.601, -1.619], [0, 0], [0, 0], [1.037, -1.379], [1.379, 1.038], [0, 0]], "v": [[71.009, 255.271], [46.157, 246.046], [44.315, 242.029], [48.332, 240.187], [68.816, 247.791], [96.203, 211.399], [100.579, 210.781], [101.197, 215.156]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 61, "ty": 4, "nm": "Layer 24", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [3.597, 4.1], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [2.618, -4.785], [0, 0], [0, 0], [0, 0]], "v": [[-183.134, 202.512], [-196.83, 232.404], [-169.189, 251.531], [-154.247, 257.797], [-155.877, 242.999], [-168.238, 228.91], [-163.212, 202.512]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 62, "ty": 4, "nm": "Hose 2::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisLayer(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate End');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sub(sum(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [1021, 544, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 230, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [0, 0], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n            0,\n            0,\n            0\n        ]);\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 63, "ty": 4, "nm": "Hose 2::Shoulder", "parent": 15, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisComp.layer(thisLayer(2)('Admin')(2)('B')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = sub(180, radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (hoseLength > straight) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var parentRot = hasParent ? parentTotal() : 0;\n        var rotCalc = sub(sum(sub(baseRot, bendAngle), bendRad), parentRot);\n        $bm_rt = sum(rotCalc, value);\n    } else {\n        $bm_rt = value;\n    }\n} catch (e) {\n    $bm_rt = value;\n}"}, "p": {"a": 0, "k": [119, -182, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 64, "ty": 4, "nm": "Hose 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\n$bm_rt = value / length(toComp([\n    0,\n    0\n]), toComp([\n    0.7071,\n    0.7071\n])) || 0.001;"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.886274509804, 0.886274509804, 0.886274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 24, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.09", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "09"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 65, "ty": 4, "nm": "Layer 22", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1025, 548, 0], "ix": 2}, "a": {"a": 0, "k": [225, -52, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.562, -6.392], [3.128, -5.295], [0, 0], [0, 0], [4.233, 2.739], [0, 0]], "o": [[0, 0], [5.562, 6.392], [-3.652, 6.184], [0, 0], [0, 0], [-4.233, -2.739], [0, 0]], "v": [[226.349, -58.135], [234.317, -50.83], [235.604, -33.191], [222.447, -28.916], [223.803, -35.142], [221.451, -42.944], [213.814, -47.925]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 66, "ty": 4, "nm": "Layer 21", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 3.026], [-3.026, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -3.026], [3.026, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-3.026, 0], [0, -3.026], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.026, 0], [0, 3.026], [0, 0], [0, 0]], "v": [[219.543, -14.556], [159.632, -14.556], [154.698, -35.142], [135.247, -35.142], [129.769, -40.62], [135.247, -46.099], [163.339, -46.099], [168.274, -25.513], [210.621, -25.513], [214.881, -46.099], [231.452, -46.099], [236.931, -40.62], [231.452, -35.142], [223.803, -35.142]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 67, "ty": 4, "nm": "w 2", "parent": 44, "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-298, -210, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -38.636], [-42.394, 0], [-5.303, 1.161], [0.548, 0.316], [5.065, 0], [0, 41.705], [-35.883, 5.886], [-0.411, 0.48]], "o": [[-37.169, 5.381], [0, 42.393], [5.646, 0], [-0.555, -0.306], [-4.789, 0.954], [-41.705, 0], [0, -37.522], [0.402, -0.488], [0, 0]], "v": [[286.666, 134.101], [220.853, 210.189], [297.736, 287.072], [314.191, 285.299], [312.54, 284.367], [297.736, 285.822], [222.103, 210.189], [285.448, 135.553], [286.666, 134.101]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235294118, 0.239215686275, 0.447058823529, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 68, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 77, "op": 147, "st": 77, "bm": 0}, {"ddd": 0, "ind": 69, "ty": 0, "nm": "Pre-comp 3", "tt": 2, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 77, "s": [22], "e": [-12]}, {"t": 144}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 77, "s": [1669, 935, 0], "e": [-95, 935, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 144}], "ix": 2}, "a": {"a": 0, "k": [465, 935, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[598, 610], [296, 610], [296, 957], [598, 957]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 77, "op": 147, "st": 77, "bm": 0}, {"ddd": 0, "ind": 70, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 15, "op": 95, "st": 15, "bm": 0}, {"ddd": 0, "ind": 71, "ty": 0, "nm": "Pre-comp 4", "tt": 2, "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 15, "s": [15], "e": [-12]}, {"t": 85}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 15, "s": [1774, 937, 0], "e": [-150, 937, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 85}], "ix": 2}, "a": {"a": 0, "k": [570, 937, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[763, 488], [413, 488], [413, 955], [763, 955]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 15, "op": 95, "st": 15, "bm": 0}, {"ddd": 0, "ind": 72, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 78, "op": 183, "st": 78, "bm": 0}, {"ddd": 0, "ind": 73, "ty": 0, "nm": "Pre-comp 4", "tt": 2, "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 78, "s": [15], "e": [-12]}, {"t": 150}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 78, "s": [1774, 937, 0], "e": [-150, 937, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 150}], "ix": 2}, "a": {"a": 0, "k": [570, 937, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[763, 488], [413, 488], [413, 955], [763, 955]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 78, "op": 183, "st": 78, "bm": 0}, {"ddd": 0, "ind": 74, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 35, "op": 113, "st": 35, "bm": 0}, {"ddd": 0, "ind": 75, "ty": 0, "nm": "Pre-comp 3", "tt": 2, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 35, "s": [22], "e": [-12]}, {"t": 104}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 35, "s": [1669, 935, 0], "e": [-95, 935, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 104}], "ix": 2}, "a": {"a": 0, "k": [465, 935, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[598, 610], [296, 610], [296, 957], [598, 957]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 35, "op": 113, "st": 35, "bm": 0}, {"ddd": 0, "ind": 76, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 89, "st": -16, "bm": 0}, {"ddd": 0, "ind": 77, "ty": 0, "nm": "Pre-comp 3", "tt": 2, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": -16, "s": [22], "e": [-12]}, {"t": 56}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": -16, "s": [1669, 935, 0], "e": [-95, 935, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 56}], "ix": 2}, "a": {"a": 0, "k": [465, 935, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[598, 610], [296, 610], [296, 957], [598, 957]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 89, "st": -16, "bm": 0}, {"ddd": 0, "ind": 78, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 89, "st": -16, "bm": 0}, {"ddd": 0, "ind": 79, "ty": 0, "nm": "Pre-comp 4", "tt": 2, "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": -16, "s": [15], "e": [-12]}, {"t": 60}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": -16, "s": [1774, 937, 0], "e": [-150, 937, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60}], "ix": 2}, "a": {"a": 0, "k": [570, 937, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[763, 488], [413, 488], [413, 955], [763, 955]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 0, "op": 89, "st": -16, "bm": 0}, {"ddd": 0, "ind": 80, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 135, "op": 151, "st": 135, "bm": 0}, {"ddd": 0, "ind": 81, "ty": 0, "nm": "Pre-comp 3", "tt": 2, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 135, "s": [22], "e": [-12]}, {"t": 207}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 135, "s": [1669, 935, 0], "e": [-95, 935, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 207}], "ix": 2}, "a": {"a": 0, "k": [465, 935, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[598, 610], [296, 610], [296, 957], [598, 957]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 135, "op": 151, "st": 135, "bm": 0}, {"ddd": 0, "ind": 82, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 135, "op": 151, "st": 135, "bm": 0}, {"ddd": 0, "ind": 83, "ty": 0, "nm": "Pre-comp 4", "tt": 2, "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 135, "s": [15], "e": [-12]}, {"t": 211}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 135, "s": [1774, 937, 0], "e": [-150, 937, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 211}], "ix": 2}, "a": {"a": 0, "k": [570, 937, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[763, 488], [413, 488], [413, 955], [763, 955]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 135, "op": 151, "st": 135, "bm": 0}, {"ddd": 0, "ind": 84, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 45, "op": 150, "st": 45, "bm": 0}, {"ddd": 0, "ind": 85, "ty": 0, "nm": "Pre-comp 1", "tt": 2, "refId": "comp_13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 60, "s": [8], "e": [-27]}, {"t": 136}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 60, "s": [1724, 772, 0], "e": [-116, 772, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 136}], "ix": 2}, "a": {"a": 0, "k": [1184, 772, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1299, 610], [1128, 610], [1128, 788], [1299, 788]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 45, "op": 150, "st": 45, "bm": 0}, {"ddd": 0, "ind": 86, "ty": 0, "nm": "Pre-comp 5", "td": 1, "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 45, "op": 150, "st": 45, "bm": 0}, {"ddd": 0, "ind": 87, "ty": 0, "nm": "Pre-comp 2", "tt": 2, "refId": "comp_14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "n": ["0p833_0p833_0p167_0p167"], "t": 60, "s": [8], "e": [-27]}, {"t": 136}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 60, "s": [1683, 937, 0], "e": [-157, 937, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 136}], "ix": 2}, "a": {"a": 0, "k": [1143, 937, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1282, 651], [1051, 651], [1051, 953], [1282, 953]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "Mask 1"}], "w": 1600, "h": 1200, "ip": 45, "op": 150, "st": 45, "bm": 0}, {"ddd": 0, "ind": 88, "ty": 1, "nm": "White Solid 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "sw": 1600, "sh": 1200, "sc": "transparent", "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": []}