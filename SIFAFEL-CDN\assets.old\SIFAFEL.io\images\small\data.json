{"v": "5.4.2", "fr": 60, "ip": 0, "op": 361, "w": 1600, "h": 1200, "nm": "teamwork_15", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Isolation Mode 63", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1045, 1168, 0], "ix": 2}, "a": {"a": 0, "k": [144, 320, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-3.048, 0.948], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -3.191], [0, 0], [0, 0], [0, 0]], "v": [[146.131, 276.472], [150.47, 295.654], [133.622, 303.063], [114.222, 303.063], [119.345, 296.102], [128.975, 293.102], [132.346, 277.53]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Isolation Mode 30", "parent": 79, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -101.147, "ix": 10}, "p": {"a": 0, "k": [-4.357, -7.056, 0], "ix": 2}, "a": {"a": 0, "k": [39, -141, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.002, 4.013], [1.671, 0.694], [2.614, -1.434], [-3.35, -2.651], [-1.396, 1.535]], "o": [[0.001, -1.811], [-2.467, -1.026], [-4.327, 2.373], [3.35, 2.652], [0.989, -1.088]], "v": [[44.449, -146.367], [41.697, -150.503], [32.981, -151.108], [34.517, -134.22], [43.31, -134.22]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.35686275363, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 4", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 35, "s": [-201.195], "e": [-198.195]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.296], "y": [0]}, "n": ["0p667_1_0p296_0"], "t": 67, "s": [-198.195], "e": [-198.195]}, {"i": {"x": [0.229], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p229_1_0p333_0"], "t": 99, "s": [-198.195], "e": [-202.195]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.83], "y": [0]}, "n": ["0p667_1_0p83_0"], "t": 120, "s": [-202.195], "e": [-198.195]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 146, "s": [-198.195], "e": [-198.195]}, {"i": {"x": [0.097], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p097_1_0p333_0"], "t": 163, "s": [-198.195], "e": [-201.195]}, {"t": 200}], "ix": 10}, "p": {"a": 0, "k": [-0.656, 103.826, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 35, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-56.308, 97.811], [-34.806, 116.41], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 67, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}]}, {"i": {"x": 0.229, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p229_1_0p333_0", "t": 99, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-54.559, 99.149], [-30.374, 106.265], [13.697, 22.749]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.83, "y": 0}, "n": "0p667_1_0p83_0", "t": 120, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-54.559, 99.149], [-30.374, 106.265], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "n": "0p667_1_0p167_0", "t": 146, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}]}, {"i": {"x": 0.097, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p097_1_0p333_0", "t": 163, "s": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-58.991, 109.294], [-34.806, 116.41], [13.697, 22.749]], "c": true}], "e": [{"i": [[19.185, 8.218], [8.969, -20.786], [0, 0], [-17.34, -3.994], [0, 0]], "o": [[-13.353, -5.626], [-13.813, 35.775], [0, 0], [2.691, 0.62], [0, 0]], "v": [[11.751, -17.06], [-24.747, 6.551], [-56.308, 97.811], [-34.806, 116.41], [13.697, 22.749]], "c": true}]}, {"t": 200}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Shape Layer 3", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 164.966, "ix": 10}, "p": {"a": 0, "k": [0.13, 0.483, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [55, 55, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[40.25, 9.5], [14.401, -43.204], [-5.75, -3.25], [0, 0]], "o": [[-25.706, -6.067], [-16.25, 53.75], [15, 6.5], [0, 0]], "v": [[6.25, -15.5], [-36.5, 35.75], [-43, 107.75], [5, 54.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "<PERSON>rist", "parent": 46, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0.269, "ix": 10}, "p": {"a": 0, "k": [-45.913, -140.996, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [55.556, 55.556, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [9, -5], [0, 0], [0, 0], [0, 0], [-2.75, 3], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-9, 5], [0, 0], [0, 0], [0, 0], [2.75, -3], [0, 0], [0, 0], [0, 0]], "v": [[24.75, -18], [6.5, -10], [-1.25, 12.5], [23.75, 24.75], [32.25, 27.5], [46.75, 20.5], [46.5, 13], [21.5, -7], [29.25, -16.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 1, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Hose 1::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [707, 338.5, 0], "e": [681, 328.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.482, "y": 1}, "o": {"x": 0.689, "y": 0}, "n": "0p482_1_0p689_0", "t": 34.957, "s": [681, 328.5, 0], "e": [688, 364.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "n": "0p667_0p667_0p167_0p167", "t": 67, "s": [688, 364.5, 0], "e": [688, 364.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.097, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p097_1_0p333_0", "t": 163, "s": [688, 364.5, 0], "e": [707, 338.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 200, "s": [707, 338.5, 0], "e": [701, 338.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 240.869, "s": [701, 338.5, 0], "e": [707, 338.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 279.881, "s": [707, 338.5, 0], "e": [701, 338.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 320.75, "s": [701, 338.5, 0], "e": [707, 338.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 359.763671875}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 290, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [58.5, -116], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisLayer(4)('RubberHose 2')('B');\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar autoRotate = eff('Auto Rotate End');\nif (autoRotate == 1) {\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = eff('B');\n    var s = length(a, b);\n    var sFac = eff('Parent Scale');\n    var autoFlop = 1;\n    var realism = eff('Realism');\n    var bendDir = div(eff('Bend Direction'), 100);\n    var hoseLength = div(eff('Hose Length'), 2);\n    var bendRad = eff('Bend Radius');\n    var autoFlop = eff('AutoFlop');\n    var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var outerRad = mul(Math.sin(0.78539816339), s);\n    var straight = div(mul(1.4142135623731, outerRad), 2);\n    straight /= Math.max(Math.abs(sFac), 0.001);\n    var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n    var innerRad;\n    if (hoseLength > straight) {\n        innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n        innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n        innerRad = linear(Math.abs(bendDir), straight, innerRad);\n    } else {\n        innerRad = straight;\n    }\n    innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n    var flopDir = 1;\n    if (bendDir < 0) {\n        flopDir = -1;\n    }\n    flopDir *= autoFlop;\n    var opp = mul(sub(innerRad, straight), flopDir);\n    var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n    var bendAngle = radiansToDegrees(theta);\n    if (sFac < 0) {\n        baseRot *= -1;\n    }\n    bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n    var rotCalc = sub(sum(baseRot, bendAngle), bendRad);\n    $bm_rt = rotCalc;\n} else {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Hose 1::Shoulder", "parent": 75, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [-93.889, -204.167, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var autoFlop = 1;\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (straight <= hoseLength) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var rotCalc = sum(sub(baseRot, bendAngle), bendRad);\n        $bm_rt = rotCalc;\n    } else {\n        $bm_rt = 0;\n    }\n    ;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Shape Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Hose 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar s = [\n        100,\n        100\n    ];\nif (hasParent) {\n    var sFactor = parentTotal();\n    s = [\n        s[0] * sFactor[0],\n        s[1] * sFactor[1]\n    ];\n}\n$bm_rt = s;\nfunction parentTotal() {\n    var sFactor = [\n            1,\n            1\n        ];\n    var scaleNorm = [\n            0,\n            0\n        ];\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        scaleNorm = eval([layerChain][0]).scale;\n        if (scaleNorm[0] != 0 && scaleNorm[1] != 0) {\n            scaleNorm = [\n                100 / scaleNorm[0],\n                100 / scaleNorm[1]\n            ];\n        }\n        sFactor = [\n            sFactor[0] * scaleNorm[0],\n            sFactor[1] * scaleNorm[1]\n        ];\n    }\n    return sFactor;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 25, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 1::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Isolation Mode 27", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.487], "y": [1]}, "o": {"x": [0.425], "y": [0]}, "n": ["0p487_1_0p425_0"], "t": 0, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.425], "y": [0]}, "n": ["0p667_1_0p425_0"], "t": 39.273, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 78.545, "s": [-2], "e": [2]}, {"i": {"x": [0.228], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p228_1_0p333_0"], "t": 117.818, "s": [2], "e": [-2]}, {"i": {"x": [0.24], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p24_1_0p333_0"], "t": 157.092, "s": [-2], "e": [2]}, {"i": {"x": [0.487], "y": [1]}, "o": {"x": [0.425], "y": [0]}, "n": ["0p487_1_0p425_0"], "t": 196.365, "s": [2], "e": [-2]}, {"i": {"x": [0.487], "y": [1]}, "o": {"x": [0.425], "y": [0]}, "n": ["0p487_1_0p425_0"], "t": 235.637, "s": [-2], "e": [2]}, {"i": {"x": [0.487], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p487_1_0p167_0"], "t": 266.383, "s": [2], "e": [-2]}, {"i": {"x": [0.487], "y": [1]}, "o": {"x": [0.425], "y": [0]}, "n": ["0p487_1_0p425_0"], "t": 297.818, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 328.564, "s": [2], "e": [-2]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [426, 1135, 0], "ix": 2}, "a": {"a": 0, "k": [-198, 305, 0], "ix": 1}, "s": {"a": 0, "k": [170, 170, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-197.923, 302.07], [-224.617, 121.302], [-223.627, 121.155], [-196.933, 301.924]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Isolation Mode 29", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-217.366, 167.701], [-275.074, 150.851], [-274.793, 149.892], [-217.085, 166.742]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Isolation Mode 28", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-212.835, 193.898], [-213.608, 193.266], [-180.24, 152.424], [-179.467, 153.057]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Isolation Mode 26", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[30.482, -30.482], [30.483, 30.483], [-30.482, 30.482], [-30.483, -30.483]], "o": [[-30.483, 30.483], [-30.482, -30.482], [30.483, -30.483], [30.482, 30.482]], "v": [[-160.004, 228.882], [-270.393, 228.882], [-270.393, 118.494], [-160.004, 118.494]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.78823530674, 0.611764729023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "<PERSON><PERSON><PERSON> 6", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 63.896, "ix": 10}, "p": {"a": 0, "k": [0.977, 100.858, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-9, 0.25], [0, 0]], "o": [[0, 0], [0, 0], [24.252, -0.674], [0, 0]], "v": [[-84.25, -52.25], [-95.25, -33.75], [-3.5, 17.25], [13.25, -3.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 7", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 135, "ix": 10}, "p": {"a": 0, "k": [0, 1, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [57, 57, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [18.5, -26.25], [0, 0], [-18.258, 36.29]], "o": [[0, 0], [-20.997, 29.793], [0, 0], [20.25, -40.25]], "v": [[-5.106, -15.771], [-84.239, 56.645], [-71.239, 83.145], [14.25, 15.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.376470588235, 0.839215686275, 0.901960784314, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Hose 3::<PERSON><PERSON>", "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 1, "k": [{"i": {"x": 0.454, "y": 1}, "o": {"x": 0.524, "y": 0}, "n": "0p454_1_0p524_0", "t": 0, "s": [789.5, 584.5, 0], "e": [839.5, 604.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.327, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p327_1_0p333_0", "t": 63, "s": [839.5, 604.5, 0], "e": [789.5, 584.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 150, "s": [789.5, 584.5, 0], "e": [809.5, 614.5, 0], "to": [3.33333325386047, 5, 0], "ti": [-0.5, -0.83333331346512, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 199, "s": [809.5, 614.5, 0], "e": [792.5, 589.5, 0], "to": [0.5, 0.83333331346512, 0], "ti": [2, 4.16666650772095, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 250, "s": [792.5, 589.5, 0], "e": [797.5, 589.5, 0], "to": [-2, -4.16666650772095, 0], "ti": [0.5, 0.83333331346512, 0]}, {"i": {"x": 0.201, "y": 1}, "o": {"x": 0.732, "y": 0}, "n": "0p201_1_0p732_0", "t": 300, "s": [797.5, 589.5, 0], "e": [789.5, 584.5, 0], "to": [-0.5, -0.83333331346512, 0], "ti": [1.33333337306976, 0.83333331346512, 0]}, {"t": 360}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 300, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": 100, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 1, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [-8.5, -3.5], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [0, 0], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisLayer(4)('RubberHose 2')('B');\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar autoRotate = eff('Auto Rotate End');\nif (autoRotate == 1) {\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = eff('B');\n    var s = length(a, b);\n    var sFac = eff('Parent Scale');\n    var autoFlop = 1;\n    var realism = eff('Realism');\n    var bendDir = div(eff('Bend Direction'), 100);\n    var hoseLength = div(eff('Hose Length'), 2);\n    var bendRad = eff('Bend Radius');\n    var autoFlop = eff('AutoFlop');\n    var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var outerRad = mul(Math.sin(0.78539816339), s);\n    var straight = div(mul(1.4142135623731, outerRad), 2);\n    straight /= Math.max(Math.abs(sFac), 0.001);\n    var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n    var innerRad;\n    if (hoseLength > straight) {\n        innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n        innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n        innerRad = linear(Math.abs(bendDir), straight, innerRad);\n    } else {\n        innerRad = straight;\n    }\n    innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n    var flopDir = 1;\n    if (bendDir < 0) {\n        flopDir = -1;\n    }\n    flopDir *= autoFlop;\n    var opp = mul(sub(innerRad, straight), flopDir);\n    var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n    var bendAngle = radiansToDegrees(theta);\n    if (sFac < 0) {\n        baseRot *= -1;\n    }\n    bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n    var rotCalc = sub(sum(baseRot, bendAngle), bendRad);\n    $bm_rt = rotCalc;\n} else {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Hose 3::Shoulder", "parent": 35, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [96.84, -21.983, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var autoFlop = 1;\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (straight <= hoseLength) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var rotCalc = sum(sub(baseRot, bendAngle), bendRad);\n        $bm_rt = rotCalc;\n    } else {\n        $bm_rt = 0;\n    }\n    ;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Hose 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar s = [\n        100,\n        100\n    ];\nif (hasParent) {\n    var sFactor = parentTotal();\n    s = [\n        s[0] * sFactor[0],\n        s[1] * sFactor[1]\n    ];\n}\n$bm_rt = s;\nfunction parentTotal() {\n    var sFactor = [\n            1,\n            1\n        ];\n    var scaleNorm = [\n            0,\n            0\n        ];\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        scaleNorm = eval([layerChain][0]).scale;\n        if (scaleNorm[0] != 0 && scaleNorm[1] != 0) {\n            scaleNorm = [\n                100 / scaleNorm[0],\n                100 / scaleNorm[1]\n            ];\n        }\n        sFactor = [\n            sFactor[0] * scaleNorm[0],\n            sFactor[1] * scaleNorm[1]\n        ];\n    }\n    return sFactor;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.376470588235, 0.490196078431, 0.545098039216, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 3::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "S<PERSON>pe Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "Isolation Mode 33", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 64.065, "ix": 10}, "p": {"a": 0, "k": [-9.126, -3.902, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [170, 170, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [2.845, -0.653], [1.75, -0.016], [1.055, 0.731], [0, 0], [-1.372, -3.19], [-1.915, 0.255], [0, 0]], "o": [[0, 0], [-1.713, 0.873], [-2.462, 0.565], [-0.404, -4.261], [-1.563, -1.085], [0, 0], [1.372, 3.191], [1.914, -0.256], [0, 0]], "v": [[7.263, -7.609], [1.392, -12.906], [-5.479, -10.407], [-11.883, -9.651], [-14.723, -15.969], [-19.317, -16.507], [-15.903, -4.354], [-0.874, -0.461], [1.934, -0.015]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 4, "nm": "Isolation Mode 32", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -0.457], [-18.653, 0], [-6.181, 6.843]], "o": [[-0.018, 0.452], [0, 18.653], [9.947, 0], [0, 0]], "v": [[-47.23, -44.555], [-47.265, -43.193], [-13.49, -9.419], [11.578, -20.563]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -18.653], [18.653, 0], [0, 18.653], [-18.653, 0]], "o": [[0, 18.653], [-18.653, 0], [0, -18.653], [18.653, 0]], "v": [[20.285, -43.193], [-13.49, -9.419], [-47.265, -43.193], [-13.49, -76.969]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152941182256, 0.203921571374, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "Isolation Mode 31", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -18.653], [18.653, 0], [0, 18.653], [-18.653, 0]], "o": [[0, 18.653], [-18.653, 0], [0, -18.653], [18.653, 0]], "v": [[20.285, -43.193], [-13.49, -9.419], [-47.265, -43.193], [-13.49, -76.969]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "<PERSON><PERSON>pe Layer 10", "parent": 84, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[23.118, -12.003], [-6.25, 0], [0, 0]], "o": [[4.5, 5.75], [12, 0], [0, 0]], "v": [[-137.75, -410.75], [-116, -400.5], [-98.06, -409.372]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "Isolation Mode 42", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[104.258, -67.034], [91.861, -67.034], [91.861, -68.034], [104.258, -68.034]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "Isolation Mode 41", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -9.824], [-9.825, 0], [0, 9.825], [9.824, 0]], "o": [[0, 9.825], [9.824, 0], [0, -9.824], [-9.825, 0]], "v": [[118.451, -88.495], [136.241, -70.706], [154.03, -88.495], [136.241, -106.284]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "Isolation Mode 40", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.284, 1.46], [1.19, -0.231], [-0.283, -1.461], [-1.19, 0.23]], "o": [[-0.283, -1.461], [-1.191, 0.231], [0.283, 1.46], [1.191, -0.231]], "v": [[109.39, -64.046], [105.724, -66.195], [107.939, -63.668], [107.746, -60.983]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.552941203117, 0.360784322023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 4, "nm": "Isolation Mode 39", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.979], [2.431, 0], [0, 2.979], [-2.431, 0]], "o": [[0, 2.979], [-2.43, 0], [0, -2.979], [2.43, 0]], "v": [[111.303, -62.729], [104.913, -57.105], [102.503, -62.729], [106.903, -68.125]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "Isolation Mode 38", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.383, -12.592], [3.638, -3.859], [3.861, 2.105], [-0.383, 4.837], [-0.174, 2.408], [3.31, 0.783], [0, 0], [-6.701, 5.169], [-8.041, -2.106]], "o": [[0, 0], [-0.384, 12.592], [-3.638, 3.859], [-3.861, -2.106], [0.164, -2.076], [0.246, -3.392], [0, 0], [0, 0], [6.701, -5.17], [8.041, 2.105]], "v": [[134.865, -97.976], [138.848, -80.317], [131.955, -54.546], [111.885, -52.983], [101.322, -68.059], [101.86, -75.244], [96.545, -82.485], [95.004, -82.851], [100.748, -100.464], [123.531, -103.91]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "Isolation Mode 35", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.172, 0], [0, -4.251], [-3.172, 0], [0, 4.252]], "o": [[-3.172, 0], [0, 4.252], [3.172, 0], [0, -4.251]], "v": [[113.27, -75.769], [107.517, -68.059], [113.27, -60.348], [119.022, -68.059]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[3.724, 0], [0, 4.804], [-3.724, 0], [0, -4.803]], "o": [[-3.724, 0], [0, -4.803], [3.724, 0], [0, 4.804]], "v": [[113.27, -59.348], [106.517, -68.059], [113.27, -76.769], [120.022, -68.059]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "Isolation Mode 34", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.025], [-2.024, 0], [0, 2.024], [2.025, 0]], "o": [[0, 2.024], [2.025, 0], [0, -2.025], [-2.024, 0]], "v": [[129.425, -70.724], [133.092, -67.057], [136.759, -70.724], [133.092, -74.391]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.376470595598, 0.839215695858, 0.901960790157, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "head", "parent": 39, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 151, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 211, "s": [-2], "e": [0]}, {"t": 251}], "ix": 10}, "p": {"a": 0, "k": [114.588, -46.412, 0], "ix": 2}, "a": {"a": 0, "k": [115, -44, 0], "ix": 1}, "s": {"a": 0, "k": [102.941, 102.941, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-2.947, -10.149], [-8.105, 1.863], [-1.376, 15.142], [2.463, 3.2], [4.065, -3.503]], "o": [[0, 0], [2.946, 10.15], [3.011, -0.692], [1.375, -15.143], [-2.463, -3.2], [-4.064, 3.504]], "v": [[97.939, -86.73], [98.776, -55.625], [119.02, -43.047], [134.308, -71.135], [131.909, -94.041], [103.001, -97.728]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "Isolation Mode 36", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.83, 0], [0, -3.802], [-2.83, 0], [0, 3.802]], "o": [[-2.83, 0], [0, 3.802], [2.83, 0], [0, -3.802]], "v": [[97.493, -72.705], [92.361, -65.811], [97.493, -58.915], [102.625, -65.811]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[3.381, 0], [0, 4.354], [-3.381, 0], [0, -4.354]], "o": [[-3.381, 0], [0, -4.354], [3.381, 0], [0, 4.354]], "v": [[97.493, -57.915], [91.361, -65.811], [97.493, -73.705], [103.625, -65.811]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Layer 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [6]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 63, "s": [6], "e": [0]}, {"t": 150}], "ix": 10}, "p": {"a": 0, "k": [994, 725, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "Isolation Mode 44", "parent": 35, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-13.398, -2.701], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[79.898, 72.316], [103.823, 79.092], [101.203, -16.645], [88.898, -13.237]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.313725501299, 0.792156875134, 0.827450990677, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 4, "nm": "body_blue", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 36, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 72, "s": [0], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 108, "s": [1], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 150, "s": [0], "e": [-10]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 210, "s": [-10], "e": [-12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 250, "s": [-12], "e": [-10]}, {"i": {"x": [0.201], "y": [1]}, "o": {"x": [0.732], "y": [0]}, "n": ["0p201_1_0p732_0"], "t": 300, "s": [-10], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [996, 734, 0], "ix": 2}, "a": {"a": 0, "k": [116, 78, 0], "ix": 1}, "s": {"a": 0, "k": [181, 181, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [6.536, -8.539], [0, 0], [-19.561, 20.512], [0, 0], [4.541, 1.75], [0, 0]], "o": [[0, 0], [-4.801, 31.868], [0, 0], [0, 0], [-0.28, -4.858], [0, 0], [0, 0]], "v": [[112.603, -38.318], [91.852, -29.223], [79.774, 72.316], [152.298, 72.316], [146.783, -23.387], [138.866, -34.239], [130.931, -37.297]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.376470595598, 0.839215695858, 0.901960790157, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 0, "s": [-5], "e": [-12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 36, "s": [-12], "e": [-12]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 150, "s": [-12], "e": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p833_1_0p333_0"], "t": 210, "s": [0], "e": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p833_1_0p167_0"], "t": 300, "s": [0], "e": [-5]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [900, 492.25, 0], "ix": 2}, "a": {"a": 0, "k": [100, -107.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-12, 6.75], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [12, -6.75], [0, 0]], "v": [[108.25, -111.25], [92.5, -104], [125.5, -11.75], [148, -3.75], [157.5, -23.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.047058823529, 0.749019607843, 0.819607843137, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[6.235, 2.283], [-0.384, -0.883], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.75, -1.75], [0, 0], [4.476, 8.057]], "o": [[-0.119, 0.358], [0.384, 0.883], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.25, 3.75], [0, 0], [-2.424, -4.363]], "v": [[88.75, -138], [92.116, -129.945], [93.5, -121.75], [90.75, -117.5], [82.25, -126.5], [77, -127.75], [83, -114.25], [92.75, -103.75], [105.25, -110.25], [103.05, -124.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.98431372549, 0.678431372549, 0.509803921569, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "rd", "nm": "Round Corners 1", "r": {"a": 0, "k": 1, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "Isolation Mode 45", "parent": 31, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [3.686, 1.086, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [97.143, 97.143, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-0.82, 0.313], [-2.688, 8.813]], "o": [[0, 0], [0.796, 0.189], [4.311, -1.647], [0, 0]], "v": [[112.06, -48.613], [112.561, -39.856], [115.066, -40.078], [121.539, -55.371]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.901960790157, 0.552941203117, 0.360784322023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "neck", "parent": 35, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.573], "y": [1]}, "o": {"x": [0.218], "y": [0]}, "n": ["0p573_1_0p218_0"], "t": 0, "s": [0], "e": [2]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.295], "y": [0]}, "n": ["0p833_1_0p295_0"], "t": 36, "s": [2], "e": [0]}, {"i": {"x": [0.573], "y": [1]}, "o": {"x": [0.218], "y": [0]}, "n": ["0p573_1_0p218_0"], "t": 72, "s": [0], "e": [2]}, {"i": {"x": [0.699], "y": [1]}, "o": {"x": [0.295], "y": [0]}, "n": ["0p699_1_0p295_0"], "t": 108, "s": [2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 150, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 210, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 250, "s": [2], "e": [4]}, {"i": {"x": [0.201], "y": [1]}, "o": {"x": [0.732], "y": [0]}, "n": ["0p201_1_0p732_0"], "t": 300, "s": [4], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [122.63, -35.812, 0], "ix": 2}, "a": {"a": 0, "k": [124, -37, 0], "ix": 1}, "s": {"a": 0, "k": [93.923, 93.923, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-9.024, -0.807], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [4.591, 0.409], [0, 0], [0, 0]], "v": [[112.265, -50.901], [112.603, -38.318], [123.072, -34.477], [130.931, -37.297], [132.714, -57.42]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.984313726425, 0.678431391716, 0.51372551918, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 40, "ty": 4, "nm": "Isolation Mode 62", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.21, -29.46], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[75.665, 162.434], [97.493, 92], [130.312, 79.092], [124.515, 70.249], [86.679, 70.249]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152941182256, 0.203921571374, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "Isolation Mode 61", "parent": 42, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.426, -14.272], [0, 0], [0, 0], [0, 0], [6.177, 2.051], [0, 0], [-0.475, 10.695], [0, 0]], "o": [[0, 0], [-3.167, 10.215], [0, 0], [0, 0], [0, 0], [-6.178, -2.052], [0, 0], [0.476, -10.695], [0, 0]], "v": [[147.001, 70.249], [146.258, 94.245], [125.964, 105.481], [98.166, 183.079], [101.77, 278.54], [86.68, 282.135], [80.502, 280.083], [74.325, 176.416], [86.68, 70.249]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "Isolation Mode 60", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [946, 1144, 0], "ix": 2}, "a": {"a": 0, "k": [90, 304, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-4.094, 1.319], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -4.301], [0, 0], [0, 0], [0, 0]], "v": [[99.16, 275.169], [100.692, 303.063], [59.338, 303.063], [66.207, 293.633], [83.461, 288.072], [82.887, 275.169]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "<PERSON><PERSON><PERSON> Lay<PERSON> 14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p35_1_0p333_0"], "t": 150, "s": [0], "e": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 210, "s": [4], "e": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 300, "s": [4], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [1019, 769, 0], "ix": 2}, "a": {"a": 0, "k": [219, 169, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [18.5, -32.5], [0, 0], [-24.5, 21]], "o": [[0, 0], [1, 59], [0, 0], [5, -44]], "v": [[248, 146.5], [188, 172.5], [189.187, 312.252], [231, 313.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "<PERSON><PERSON><PERSON><PERSON> 13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.35], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p35_1_0p333_0"], "t": 150, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 210, "s": [-3], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 300, "s": [-3], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [1036, 1093, 0], "ix": 2}, "a": {"a": 0, "k": [236, 493, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[188.5, 305], [218.369, 496.997], [251.369, 495.997], [230.5, 311]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "Isolation Mode 65", "parent": 46, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [33.674, -46.051], [-12.08, -3.617]], "o": [[0, 0], [-1.964, 1.667], [10.886, 5.185], [0, 0]], "v": [[22.398, -204.543], [22.303, -204.461], [-45.171, -129.439], [-10.669, -116.189]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[34.763, -47.543], [-15.684, -4.003], [-15.265, -0.844], [0, 0]], "o": [[13.803, 6.574], [15.685, 4.003], [-7.729, -58.387], [0, 0]], "v": [[-45.441, -129.439], [-1.121, -113.451], [45.441, -106.245], [22.127, -204.543]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152941182256, 0.203921571374, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "rocket hat", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [1.296], "e": [1.296]}, {"i": {"x": [0.482], "y": [1]}, "o": {"x": [0.689], "y": [0]}, "n": ["0p482_1_0p689_0"], "t": 34.957, "s": [1.296], "e": [-12.704]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p833_1_0p167_0"], "t": 67, "s": [-12.704], "e": [-12.704]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.172], "y": [0]}, "n": ["0p667_1_0p172_0"], "t": 99, "s": [-12.704], "e": [-12.704]}, {"i": {"x": [0.097], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p097_1_0p333_0"], "t": 163, "s": [-12.704], "e": [1.296]}, {"t": 200}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 0, "s": [804.802, 394.35, 0], "e": [775.802, 381.35, 0], "to": [-4.83333349227905, -2.16666674613953, 0], "ti": [5, -2.5, 0]}, {"i": {"x": 0.482, "y": 1}, "o": {"x": 0.689, "y": 0}, "n": "0p482_1_0p689_0", "t": 34.957, "s": [775.802, 381.35, 0], "e": [774.802, 409.35, 0], "to": [-5, 2.5, 0], "ti": [0.16666667163372, -4.66666650772095, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "n": "0p667_0p667_0p167_0p167", "t": 67, "s": [774.802, 409.35, 0], "e": [774.802, 409.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.229, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p229_1_0p333_0", "t": 99, "s": [774.802, 409.35, 0], "e": [770.802, 396.35, 0], "to": [-0.66666668653488, -2.16666674613953, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.83, "y": 0}, "n": "0p667_1_0p83_0", "t": 120, "s": [770.802, 396.35, 0], "e": [774.802, 409.35, 0], "to": [0, 0, 0], "ti": [-0.66666668653488, -2.16666674613953, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.167, "y": 0.167}, "n": "0p667_0p667_0p167_0p167", "t": 146, "s": [774.802, 409.35, 0], "e": [774.802, 409.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.097, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p097_1_0p333_0", "t": 163, "s": [774.802, 409.35, 0], "e": [804.802, 394.35, 0], "to": [5, -2.5, 0], "ti": [-4, 2.5, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 200, "s": [804.802, 394.35, 0], "e": [798.802, 394.35, 0], "to": [4, -2.5, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 240.869, "s": [798.802, 394.35, 0], "e": [804.802, 394.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 279.881, "s": [804.802, 394.35, 0], "e": [798.802, 394.35, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 320.75, "s": [798.802, 394.35, 0], "e": [804.802, 394.35, 0], "to": [0, 0, 0], "ti": [-1, 0, 0]}, {"t": 359.763671875}], "ix": 2}, "a": {"a": 0, "k": [2, -114, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[34.763, -47.543], [-15.684, -4.003], [-15.265, -0.844], [0, 0]], "o": [[13.803, 6.574], [15.685, 4.003], [-7.729, -58.387], [0, 0]], "v": [[-45.441, -129.439], [-1.121, -113.451], [45.441, -106.245], [22.127, -204.543]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "Isolation Mode 54", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.297], [4.298, 0], [0, 4.298], [-4.298, 0]], "o": [[0, 4.298], [-4.298, 0], [0, -4.297], [4.298, 0]], "v": [[6.216, 59.065], [-1.565, 66.847], [-9.347, 59.065], [-1.565, 51.284]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 48, "ty": 4, "nm": "Isolation Mode 53", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.298], [4.298, 0], [0, 4.297], [-4.298, 0]], "o": [[0, 4.297], [-4.298, 0], [0, -4.298], [4.298, 0]], "v": [[6.216, 27.941], [-1.565, 35.723], [-9.347, 27.941], [-1.565, 20.16]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "Isolation Mode 52", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -18.653], [18.653, 0], [0, 18.653], [-18.653, 0]], "o": [[0, 18.653], [-18.653, 0], [0, -18.653], [18.653, 0]], "v": [[32.209, -43.193], [-1.565, -9.419], [-35.34, -43.193], [-1.565, -76.969]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.662745118141, 0.760784327984, 0.86274510622, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 50, "ty": 4, "nm": "Isolation Mode 51", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.271, 301.896], [6.141, 301.896], [6.141, 239.622], [-9.271, 239.622]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.956862747669, 0.509803950787, 0.54509806633, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 51, "ty": 4, "nm": "Isolation Mode 50", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.271, 301.896], [6.141, 301.896], [6.141, 167.893], [-9.271, 167.893]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 52, "ty": 4, "nm": "Isolation Mode 49", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [782, 1076, 0], "ix": 2}, "a": {"a": 0, "k": [-2, 270, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 95.641], [17.161, 42.781], [16.187, 0], [3.533, 0.152], [0, -39.73], [-25.797, -58.438], [0, 0]], "o": [[0, -59.92], [-15, 2.956], [-3.583, 0], [-8.026, 35.127], [0, 106.941], [0, 0], [25.524, -41.079]], "v": [[78.321, 51.395], [45.863, -106.244], [-1.035, -101.713], [-11.707, -101.955], [-24.909, 10.939], [17.657, 271.062], [35.349, 271.062]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972549021244, 0.972549021244, 0.972549021244, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 53, "ty": 4, "nm": "Isolation Mode 48", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 268, 0], "ix": 2}, "a": {"a": 0, "k": [0, 268, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[15, 2.957], [0, -59.92], [-25.523, -41.079], [0, 0], [0, 95.641], [17.161, 42.782], [16.188, 0]], "o": [[-17.161, 42.782], [0, 95.641], [0, 0], [25.523, -41.079], [0, -59.92], [-15, 2.957], [-16.188, 0]], "v": [[-48.464, -106.245], [-80.922, 51.395], [-37.95, 271.062], [34.819, 271.062], [77.791, 51.395], [45.333, -106.245], [-1.565, -101.713]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.886274516582, 0.886274516582, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 54, "ty": 4, "nm": "Isolation Mode 47", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-52.359, 150.381], [-87.451, 182.5], [-75.75, 287], [-37.95, 271.062]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-52.359, 150.381], [-110.976, 203.825], [-110.976, 301.896], [-37.95, 271.062]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.956862747669, 0.509803950787, 0.54509806633, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 55, "ty": 4, "nm": "Isolation Mode 46", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-52.359, 150.381], [-110.976, 203.825], [-110.976, 301.896], [-37.95, 271.062]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 56, "ty": 4, "nm": "Isolation Mode 43", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[80.015, 178.451], [61.25, 282.284], [34.819, 271.062], [49.229, 150.381]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[49.229, 150.381], [107.845, 203.825], [107.845, 301.896], [34.819, 271.062]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.956862747669, 0.509803950787, 0.54509806633, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 57, "ty": 4, "nm": "Isolation Mode 37", "parent": 52, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[49.229, 150.381], [107.845, 203.825], [107.845, 301.896], [34.819, 271.062]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.57647061348, 0.596078455448, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 58, "ty": 4, "nm": "body", "parent": 75, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.222, 3.333, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [55.556, 55.556, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-40.5, 10.5], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [9.5, -1.5], [0, 0]], "v": [[-43, -229], [-106, -222], [-128.5, -163.5], [-48.5, -171.5], [-39.5, -173.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 6, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.976470588235, 0.760784313725, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 59, "ty": 4, "nm": "brows", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.043, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p043_1_0p333_0", "t": 53, "s": [67.778, 241.667, 0], "e": [67.276, 239.502, 0], "to": [-0.08355842530727, -0.36082157492638, 0], "ti": [0.08355842530727, 0.36082157492638, 0]}, {"i": {"x": 0.043, "y": 0.043}, "o": {"x": 0.167, "y": 0.167}, "n": "0p043_0p043_0p167_0p167", "t": 65, "s": [67.276, 239.502, 0], "e": [67.276, 239.502, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.445, "y": 1}, "o": {"x": 0.167, "y": 0}, "n": "0p445_1_0p167_0", "t": 116, "s": [67.276, 239.502, 0], "e": [67.778, 241.667, 0], "to": [0.08355842530727, 0.36082157492638, 0], "ti": [-0.08355842530727, -0.36082157492638, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "n": "0p667_0p667_0p333_0p333", "t": 126, "s": [67.778, 241.667, 0], "e": [67.778, 241.667, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.097, "y": 0.097}, "o": {"x": 0.333, "y": 0.333}, "n": "0p097_0p097_0p333_0p333", "t": 163, "s": [67.778, 241.667, 0], "e": [67.778, 241.667, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 200}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [55.556, 55.556, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.375, -1.875]], "o": [[0, 0], [6.375, 1.875]], "v": [[-73.375, -496.375], [-61.125, -497.75]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 2", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-12, -2.25]], "o": [[0, 0], [12, 2.25]], "v": [[-99, -492.875], [-81.25, -496.375]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101960784314, 0.133333333333, 0.286274509804, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 60, "ty": 4, "nm": "Mouth", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [25.077, -5.136, 0], "ix": 2}, "a": {"a": 0, "k": [-74.877, -444.489, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "n": ["0p667_1_0p167_0", "0p667_1_0p167_0", "0p667_1_0p167_0"], "t": 19, "s": [55.556, 55.556, 100], "e": [55.556, 55.556, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 56, "s": [55.556, 55.556, 100], "e": [40, 40, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 63, "s": [40, 40, 100], "e": [40, 40, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 124, "s": [40, 40, 100], "e": [55.556, 55.556, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p667_1_0p333_0", "0p667_1_0p333_0", "0p667_1_0p333_0"], "t": 144, "s": [55.556, 55.556, 100], "e": [55.556, 55.556, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "n": ["0p833_1_0p333_0", "0p833_1_0p333_0", "0p833_1_0p333_0"], "t": 200, "s": [55.556, 55.556, 100], "e": [55.556, 55.556, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "n": ["0p833_1_0p167_0", "0p833_1_0p167_0", "0p833_1_0p167_0"], "t": 301, "s": [55.556, 55.556, 100], "e": [55.556, 55.556, 100]}, {"t": 326}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0}, "n": "0p833_0p833_0p167_0", "t": 19, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-76, -437.875], [-66.125, -447.75]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 29, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p667_1_0p167_0p167", "t": 56, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}], "e": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 63, "s": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}], "e": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 124, "s": [{"i": [[2.718, -3.325], [-3.798, -0.405], [1.874, 3.126]], "o": [[-2.418, 2.958], [3.624, 0.386], [-2.208, -3.684]], "v": [[-81.073, -448.65], [-75.182, -438.82], [-68.263, -447.285]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-86.55, -446.821], [-75.679, -442.845], [-65.458, -447.143]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "n": "0p667_1_0p333_0", "t": 144, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-86.55, -446.821], [-75.679, -442.845], [-65.458, -447.143]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-86.55, -446.821], [-75.679, -442.845], [-65.458, -447.143]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "n": "0p833_0p833_0p333_0", "t": 200, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-86.55, -446.821], [-75.679, -442.845], [-65.458, -447.143]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_1_0p167_0p167", "t": 301, "s": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-75.065, -442.277], [-66.125, -447.75]], "c": true}], "e": [{"i": [[0, 0], [-10, 0], [0, 0]], "o": [[0, 0], [8.75, 0], [0, 0]], "v": [[-87.125, -449.125], [-76, -437.875], [-66.125, -447.75]], "c": true}]}, {"t": 326}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 61, "ty": 4, "nm": "Isolation Mode 14", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-31.162, -263.835], [-34.288, -263.835], [-34.288, -264.835], [-31.162, -264.835]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 62, "ty": 4, "nm": "Isolation Mode 13", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-46.643, -263.835], [-60.531, -263.835], [-60.531, -264.835], [-46.643, -264.835]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 63, "ty": 4, "nm": "Isolation Mode 12", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.172, 0], [0, -4.252], [-3.172, 0], [0, 4.251]], "o": [[-3.172, 0], [0, 4.251], [3.172, 0], [0, -4.252]], "v": [[-40.541, -270.265], [-46.294, -262.554], [-40.541, -254.844], [-34.788, -262.554]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[3.724, 0], [0, 4.803], [-3.724, 0], [0, -4.804]], "o": [[-3.724, 0], [0, -4.804], [3.724, 0], [0, 4.803]], "v": [[-40.541, -253.844], [-47.294, -262.554], [-40.541, -271.265], [-33.788, -262.554]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 64, "ty": 4, "nm": "Isolation Mode 11", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.172, 0], [0, -4.252], [-3.172, 0], [0, 4.251]], "o": [[-3.172, 0], [0, 4.251], [3.172, 0], [0, -4.252]], "v": [[-24.909, -270.265], [-30.662, -262.554], [-24.909, -254.844], [-19.157, -262.554]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[3.723, 0], [0, 4.803], [-3.724, 0], [0, -4.804]], "o": [[-3.724, 0], [0, -4.804], [3.723, 0], [0, 4.803]], "v": [[-24.909, -253.844], [-31.662, -262.554], [-24.909, -271.265], [-18.157, -262.554]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 65, "ty": 4, "nm": "Isolation Mode 10", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-18.657, -263.835], [-23.954, -263.835], [-23.954, -264.835], [-18.657, -264.835]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 66, "ty": 4, "nm": "Isolation Mode 7", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.418, -1.971], [-1.606, -0.34], [-0.417, 1.97], [1.606, 0.34]], "o": [[-0.417, 1.97], [1.606, 0.341], [0.418, -1.97], [-1.606, -0.341]], "v": [[-71.155, -264.854], [-67.719, -260.241], [-69.147, -264.561], [-67.49, -267.805]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.556862771511, 0.290196090937, 0.184313729405, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 67, "ty": 4, "nm": "Isolation Mode 6", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [2.408, -3.729], [3.616, 1.556], [1.469, 4.746], [0, 0]], "o": [[0, 0], [-2.408, 3.729], [-3.617, -1.556], [-1.47, -4.747], [0, 0]], "v": [[-24.378, -251.676], [-26.811, -227.266], [-40.859, -224.282], [-51.822, -236.193], [-30.397, -247.719]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 68, "ty": 4, "nm": "Isolation Mode 5", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.4], [-3.589, 0], [0, 4.4], [3.589, 0]], "o": [[0, 4.4], [3.589, 0], [0, -4.4], [-3.589, 0]], "v": [[-73.528, -262.799], [-64.092, -254.492], [-60.531, -262.799], [-67.03, -270.767]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.35686275363, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 69, "ty": 4, "nm": "Isolation Mode 4", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.018, 1.48], [-2.945, 8.857], [-7.816, -1.536], [-0.798, 2.271], [2.713, 2.119], [10.595, -4.323], [2.373, -6.866], [-0.848, -1.102]], "o": [[0, 0], [1.017, -1.479], [2.103, -6.321], [7.817, 1.535], [0.798, -2.271], [-2.712, -2.119], [-10.596, 4.322], [-2.374, 6.866], [0.848, 1.102]], "v": [[-61.542, -261.396], [-58.038, -262.63], [-56.993, -281.316], [-37.522, -289.764], [-26.938, -289.811], [-31.988, -299.54], [-58.01, -301.065], [-73.098, -286.995], [-73.098, -261.82]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 70, "ty": 4, "nm": "Isolation Mode 3", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-5.171, -1.512], [-7.968, 0.339], [-1.804, -1.221], [1.618, -3.403], [8.621, 8.604], [0.552, 9.766]], "o": [[0, 0], [4.421, 1.293], [4.433, -0.188], [-0.527, 4.476], [-8.112, 17.065], [-4.173, -4.165], [0, 0]], "v": [[-58.158, -268.168], [-50.748, -247.768], [-32.214, -253.129], [-24.378, -251.676], [-27.551, -239.658], [-64.416, -241.277], [-71.015, -265.06]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 71, "ty": 4, "nm": "Isolation Mode 2", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[8.112, -17.065], [8.622, 8.604], [-2.387, 6.227], [-10.509, -4.02]], "o": [[-8.111, 17.065], [-8.621, -8.604], [2.387, -6.228], [10.51, 4.02]], "v": [[-27.559, -239.658], [-64.424, -241.277], [-69.467, -288.993], [-36.646, -296.256]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.35686275363, 0.227450981736, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 72, "ty": 4, "nm": "Isolation Mode", "parent": 73, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 238, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.273, -9.391], [0, 0], [0, 0]], "o": [[0, 0], [-0.13, 4.455], [0, 0], [0, 0]], "v": [[-28.039, -288.804], [-24.378, -277.943], [-26.174, -265.06], [-36.431, -291.251]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 73, "ty": 4, "nm": "head", "parent": 84, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [-3]}, {"i": {"x": [0.482], "y": [1]}, "o": {"x": [0.689], "y": [0]}, "n": ["0p482_1_0p689_0"], "t": 34.957, "s": [-3], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.337], "y": [0]}, "n": ["0p667_1_0p337_0"], "t": 67, "s": [3], "e": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 89, "s": [7], "e": [7]}, {"i": {"x": [0.392], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p392_1_0p333_0"], "t": 99, "s": [7], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 120, "s": [0], "e": [7]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 146, "s": [7], "e": [7]}, {"i": {"x": [0.097], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p097_1_0p333_0"], "t": 163, "s": [7], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 200, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 240.869, "s": [-3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 279.881, "s": [0], "e": [-3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 320.75, "s": [-3], "e": [0]}, {"t": 359.763671875}], "ix": 10}, "p": {"a": 0, "k": [-126, -435, 0], "ix": 2}, "a": {"a": 0, "k": [-2.222, 0, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 74, "ty": 4, "nm": "Isolation Mode 9", "parent": 75, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-10.16, 4.668], [0.717, 12.536], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-16.477, -0.696]], "o": [[-0.717, -12.537], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.895, 3.569], [24.794, 1.047]], "v": [[-13.277, -84.887], [-15.428, -122.497], [-51.891, -117.666], [-60.531, -86.524], [-77.333, -88.495], [-69.104, -142.221], [-104.039, -153.324], [-106.164, -82.452], [-67.514, -74.102]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.929411768913, 0.713725507259, 0.027450980619, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 75, "ty": 4, "nm": "body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [-5]}, {"i": {"x": [0.482], "y": [1]}, "o": {"x": [0.689], "y": [0]}, "n": ["0p482_1_0p689_0"], "t": 34.957, "s": [-5], "e": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 67, "s": [4], "e": [4]}, {"i": {"x": [0.229], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p229_1_0p333_0"], "t": 99, "s": [4], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.83], "y": [0]}, "n": ["0p667_1_0p83_0"], "t": 120, "s": [2], "e": [4]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 146, "s": [4], "e": [4]}, {"i": {"x": [0.097], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p097_1_0p333_0"], "t": 163, "s": [4], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 200, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 240.25, "s": [-2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 280.502, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 320.75, "s": [-2], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [666, 459, 0], "ix": 2}, "a": {"a": 0, "k": [-67.222, -75, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.08, -2.673], [0, 0], [-16.477, -0.696], [-10.16, 4.669], [2.413, 42.206], [0, 0]], "o": [[0, 0], [-2.292, 0.719], [0, 0], [8.895, 3.569], [24.794, 1.048], [-2.414, -42.206], [0, 0], [0, 0]], "v": [[-67.577, -223.355], [-96.162, -214.395], [-102.4, -207.99], [-106.164, -82.452], [-67.514, -74.102], [-13.277, -84.888], [-20.518, -211.507], [-46.97, -223.037]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.976470589638, 0.760784327984, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 76, "ty": 4, "nm": "Isolation Mode 16", "parent": 78, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.656, -5.573], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.169, 1.237], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-56.348, -75.607], [-101.67, -86.35], [-102.463, -86.126], [-103.835, -75.236], [-73.528, -62.615], [-87.451, -5.5], [-81.413, 52.025], [-72.539, 15.607]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [1.27, -17.175], [0, 0], [0, 0], [-3.908, 1.116], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.269, 17.176], [0, 0], [0, 0], [3.908, -1.117], [0, 0], [0, 0], [0, 0]], "v": [[-102.408, -86.524], [-106.164, -51.498], [-98.5, 16.204], [-113.411, 114.799], [-101.013, 117.537], [-97.104, 116.42], [-72.539, 15.607], [-56.348, -75.607]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.152941182256, 0.203921571374, 0.368627458811, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 77, "ty": 4, "nm": "Isolation Mode 15", "parent": 78, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.27, -17.175], [0, 0], [0, 0], [-3.908, 1.116], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.269, 17.176], [0, 0], [0, 0], [3.908, -1.117], [0, 0], [0, 0], [0, 0]], "v": [[-102.408, -86.524], [-106.164, -51.498], [-98.5, 16.204], [-113.411, 114.799], [-101.013, 117.537], [-97.104, 116.42], [-72.539, 15.607], [-56.348, -75.607]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 78, "ty": 4, "nm": "Isolation Mode 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [611, 828, 0], "ix": 2}, "a": {"a": 0, "k": [-97, 130, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [3.291, 1.063], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, -3.458], [0, 0], [0, 0], [0, 0]], "v": [[-110.976, 112.233], [-114.582, 130.597], [-81.96, 130.597], [-87.481, 123.013], [-99.853, 119.02], [-98.271, 108.465]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.133333340287, 0.152941182256, 0.278431385756, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 79, "ty": 4, "nm": "Hose 2::<PERSON><PERSON>", "parent": 46, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [31.03, -138.057, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "RubberHose 2", "np": 18, "mn": "Pseudo/3bf5uID/RubberHose_2", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Hose Length", "mn": "Pseudo/3bf5uID/RubberHose_2-0001", "ix": 1, "v": {"a": 0, "k": 410, "ix": 1}}, {"ty": 0, "nm": "Bend Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 0, "nm": "Realism", "mn": "Pseudo/3bf5uID/RubberHose_2-0003", "ix": 3, "v": {"a": 0, "k": 0, "ix": 3}}, {"ty": 0, "nm": "Bend Direction", "mn": "Pseudo/3bf5uID/RubberHose_2-0004", "ix": 4, "v": {"a": 0, "k": -22, "ix": 4}}, {"ty": 7, "nm": "Auto Rotate Start", "mn": "Pseudo/3bf5uID/RubberHose_2-0005", "ix": 5, "v": {"a": 0, "k": 0, "ix": 5}}, {"ty": 7, "nm": "Auto Rotate End", "mn": "Pseudo/3bf5uID/RubberHose_2-0006", "ix": 6, "v": {"a": 0, "k": 1, "ix": 6}}, {"ty": 6, "nm": "<PERSON>", "mn": "Pseudo/3bf5uID/RubberHose_2-0007", "ix": 7, "v": 0}, {"ty": 3, "nm": "A", "mn": "Pseudo/3bf5uID/RubberHose_2-0008", "ix": 8, "v": {"a": 0, "k": [21, 4.5], "ix": 8, "x": "var $bm_rt;\n$bm_rt = thisLayer.toComp([\n    0,\n    0,\n    0\n]);"}}, {"ty": 3, "nm": "B", "mn": "Pseudo/3bf5uID/RubberHose_2-0009", "ix": 9, "v": {"a": 0, "k": [-66.239, -49.294], "ix": 9, "x": "var $bm_rt;\ntry {\n    var b = thisLayer(2)('Admin')(2)('B')(2)(1)._name;\n    $bm_rt = thisComp.layer(b).toComp([\n        0,\n        0,\n        0\n    ]);\n} catch (err) {\n    $bm_rt = value;\n}"}}, {"ty": 0, "nm": "Outer Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0010", "ix": 10, "v": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\nvar s = length(a, b);\n$bm_rt = mul(Math.sin(0.78539816339), s);"}}, {"ty": 0, "nm": "Inner Radius", "mn": "Pseudo/3bf5uID/RubberHose_2-0011", "ix": 11, "v": {"a": 0, "k": 0, "ix": 11, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar bendRad = eff('Bend Radius');\nvar hoseLength = div(eff('Hose Length'), 2);\nvar realism = eff('Realism');\nvar bendDir = div(eff('Bend Direction'), 100);\nvar sFac = eff('Parent Scale');\nvar straight = eff('Straight');\nvar autoFlop = eff('AutoFlop');\nvar roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\nvar innerRad;\nif (hoseLength > straight) {\n    innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n    innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n    innerRad = linear(Math.abs(bendDir), straight, innerRad);\n} else {\n    innerRad = straight;\n}\ninnerRad *= Math.abs(sFac);\ninnerRad = linear(Math.abs(autoFlop), mul(straight, Math.max(Math.abs(sFac), 0.001)), innerRad);\n$bm_rt = innerRad;"}}, {"ty": 0, "nm": "Straight", "mn": "Pseudo/3bf5uID/RubberHose_2-0012", "ix": 12, "v": {"a": 0, "k": 0, "ix": 12, "x": "var $bm_rt;\nvar sFac = thisLayer(4)('RubberHose 2')('Parent Scale');\nvar outerRad = div(thisLayer(4)('RubberHose 2')('Outer Radius'), Math.max(Math.abs(sFac), 0.001));\n;\n$bm_rt = div(mul(1.4142135623731, outerRad), 2);"}}, {"ty": 0, "nm": "Base Rotation", "mn": "Pseudo/3bf5uID/RubberHose_2-0013", "ix": 13, "v": {"a": 0, "k": 0, "ix": 13, "x": "var $bm_rt;\nvar a = thisLayer(4)('RubberHose 2')('A');\nvar b = thisLayer(4)('RubberHose 2')('B');\n$bm_rt = radiansToDegrees(Math.atan2(sub(a[1], b[1]), sub(a[0], b[0])));"}}, {"ty": 0, "nm": "AutoFlop", "mn": "Pseudo/3bf5uID/RubberHose_2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14, "x": "var $bm_rt;\nvar hasAF = false, isEnabled = false, output;\ntry {\n    var lyrAF = thisComp.layer(sum(thisLayer._name.split('::')[0], '::AutoFlop'));\n    isEnabled = lyrAF(4)('Enable')(1);\n    var falloffAngle = lyrAF(4)('Falloff')(1);\n    hasAF = true;\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = thisLayer(4)('RubberHose 2')('B');\n} catch (e) {\n}\nif (hasAF && isEnabled == 1) {\n    var threshRot = lyrAF('ADBE Transform Group')('ADBE Rotate Z');\n    threshRot %= 360;\n    var ctrlAngle = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var offsetAngle = sub(threshRot, ctrlAngle);\n    offsetAngle %= 360;\n    var sign = offsetAngle > 0 && offsetAngle < 180 || offsetAngle < -180 ? -1 : 1;\n    var absAngle = Math.abs(offsetAngle);\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    if (absAngle > 90) {\n        absAngle = Math.abs(sub(absAngle, 180));\n    }\n    output = linear(absAngle, 0, falloffAngle, 0, 1);\n    output *= sign;\n} else {\n    output = 1;\n}\n$bm_rt = output;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}}, {"ty": 0, "nm": "Parent Scale", "mn": "Pseudo/3bf5uID/RubberHose_2-0015", "ix": 15, "v": {"a": 0, "k": 0, "ix": 15, "x": "var $bm_rt;\nvar sFactor = 1;\nvar scaleNorm = 0;\nvar layerChain = 'thisLayer';\nwhile (eval([layerChain][0]).hasParent) {\n    layerChain = sum(layerChain, '.parent');\n    scaleNorm = div(eval(layerChain)('ADBE Transform Group')('ADBE Scale')[0], 100);\n    sFactor = mul(sFactor, scaleNorm);\n}\n$bm_rt = sFactor;"}}, {"ty": 6, "nm": "", "mn": "Pseudo/3bf5uID/RubberHose_2-0016", "ix": 16, "v": 0}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\nvar eff = thisLayer(4)('RubberHose 2');\nvar autoRotate = eff('Auto Rotate End');\nif (autoRotate == 1) {\n    var a = thisLayer.toComp([\n            0,\n            0,\n            0\n        ]);\n    var b = eff('B');\n    var s = length(a, b);\n    var sFac = eff('Parent Scale');\n    var autoFlop = 1;\n    var realism = eff('Realism');\n    var bendDir = div(eff('Bend Direction'), 100);\n    var hoseLength = div(eff('Hose Length'), 2);\n    var bendRad = eff('Bend Radius');\n    var autoFlop = eff('AutoFlop');\n    var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n    var outerRad = mul(Math.sin(0.78539816339), s);\n    var straight = div(mul(1.4142135623731, outerRad), 2);\n    straight /= Math.max(Math.abs(sFac), 0.001);\n    var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n    var innerRad;\n    if (hoseLength > straight) {\n        innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n        innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n        innerRad = linear(Math.abs(bendDir), straight, innerRad);\n    } else {\n        innerRad = straight;\n    }\n    innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n    var flopDir = 1;\n    if (bendDir < 0) {\n        flopDir = -1;\n    }\n    flopDir *= autoFlop;\n    var opp = mul(sub(innerRad, straight), flopDir);\n    var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n    var bendAngle = radiansToDegrees(theta);\n    if (sFac < 0) {\n        baseRot *= -1;\n    }\n    bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n    var rotCalc = sub(sum(baseRot, bendAngle), bendRad);\n    $bm_rt = rotCalc;\n} else {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\nif (thisLayer.active) {\n    try {\n        var eff = thisLayer(4)('RubberHose 2');\n        var a = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = eff('B');\n        var straight = eff('Straight');\n        var hoseLength = div(eff('Hose Length'), 2);\n        if (straight > hoseLength) {\n            $bm_rt = [\n                0.51,\n                0.83,\n                0.98,\n                1\n            ];\n        } else {\n            $bm_rt = value;\n        }\n    } catch (err) {\n        $bm_rt = value;\n    }\n} else {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 80, "ty": 4, "nm": "Hose 2::Shoulder", "parent": 75, "hd": true, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\ntry {\n    var parentRot = hasParent ? parentTotal() : 0;\n    var rotCalc = sub(thisLayer('ADBE Root Vectors Group')('Admin')('Transform')('Rotation'), parentRot);\n    $bm_rt = sum(rotCalc, value);\n} catch (err) {\n    $bm_rt = value;\n}\n;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [-28.517, -205.012, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [20, 20], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6, "x": "var $bm_rt;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var autoRotate = eff('Auto Rotate Start');\n    if (autoRotate == 1) {\n        var a = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name).toComp([\n                0,\n                0,\n                0\n            ]);\n        var b = thisLayer.toComp([\n                0,\n                0,\n                0\n            ]);\n        var s = length(a, b);\n        var sFac = eff('Parent Scale');\n        var autoFlop = 1;\n        var realism = eff('Realism');\n        var bendDir = div(eff('Bend Direction'), 100);\n        var hoseLength = div(eff('Hose Length'), 2);\n        var bendRad = eff('Bend Radius');\n        var autoFlop = eff('AutoFlop');\n        var baseRot = $bm_neg(radiansToDegrees(Math.atan2(sub(b[0], a[0]), sub(b[1], a[1]))));\n        var outerRad = mul(Math.sin(0.78539816339), s);\n        var straight = div(mul(1.4142135623731, outerRad), 2);\n        straight /= Math.max(Math.abs(sFac), 0.001);\n        var roundShrink = linear(Math.abs(bendRad), 0, 100, 1, 0.87);\n        var innerRad;\n        if (straight <= hoseLength) {\n            innerRad = sum(straight, mul(Math.sqrt(sub(Math.pow(hoseLength, 2), Math.pow(straight, 2))), roundShrink));\n            innerRad = linear(realism, 0, 100, hoseLength, innerRad);\n            innerRad = linear(Math.abs(bendDir), straight, innerRad);\n        } else {\n            innerRad = straight;\n        }\n        innerRad = linear(Math.abs(autoFlop), straight, innerRad);\n        var flopDir = 1;\n        if (bendDir < 0) {\n            flopDir = -1;\n        }\n        flopDir *= autoFlop;\n        var opp = mul(sub(innerRad, straight), flopDir);\n        var theta = Math.atan(div(opp, Math.max(straight, 0.001)));\n        var bendAngle = radiansToDegrees(theta);\n        if (sFac < 0) {\n            baseRot *= -1;\n        }\n        bendRad *= div(div(theta, $bm_neg(Math.PI)), linear(s, hoseLength, 0, 2, 0.9));\n        var rotCalc = sum(sub(baseRot, bendAngle), bendRad);\n        $bm_rt = rotCalc;\n    } else {\n        $bm_rt = 0;\n    }\n    ;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Circle", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-75, 0], [75, 0]], "c": false}, "ix": 2}, "nm": "01", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -30], [0, 30]], "c": false}, "ix": 2}, "nm": "02", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [30, 30], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ControlShape", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.560000002384, 0, 1], "ix": 3, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name);\n    $bm_rt = ctrl(2)('Control Point')(2)('Stroke 1')('Color');\n} catch (e) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 3, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Control Point", "np": 2, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 81, "ty": 4, "nm": "Hose 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar r = 0;\nif (thisLayer.hasParent) {\n    r = $bm_neg(parentTotal());\n}\n$bm_rt = r;\nfunction parentTotal() {\n    var parentVal = 0;\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        parentVal = sum(parentVal, eval([layerChain][0]).rotation);\n    }\n    return parentVal;\n}"}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2, "x": "var $bm_rt;\nvar p = [\n        0,\n        0\n    ];\ntry {\n    if (thisLayer.hasParent) {\n        p = parent.fromComp([\n            0,\n            0,\n            0\n        ]);\n    }\n    $bm_rt = p;\n} catch (err) {\n    $bm_rt = p;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "x": "var $bm_rt;\nvar s = [\n        100,\n        100\n    ];\nif (hasParent) {\n    var sFactor = parentTotal();\n    s = [\n        s[0] * sFactor[0],\n        s[1] * sFactor[1]\n    ];\n}\n$bm_rt = s;\nfunction parentTotal() {\n    var sFactor = [\n            1,\n            1\n        ];\n    var scaleNorm = [\n            0,\n            0\n        ];\n    var layerChain = 'thisLayer';\n    while (eval([layerChain][0]).hasParent) {\n        layerChain = sum(layerChain, '.parent');\n        scaleNorm = eval([layerChain][0]).scale;\n        if (scaleNorm[0] != 0 && scaleNorm[1] != 0) {\n            scaleNorm = [\n                100 / scaleNorm[0],\n                100 / scaleNorm[1]\n            ];\n        }\n        sFactor = [\n            sFactor[0] * scaleNorm[0],\n            sFactor[1] * scaleNorm[1]\n        ];\n    }\n    return sFactor;\n}"}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 500, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "is": {"a": 0, "k": 0, "ix": 8, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "or": {"a": 0, "k": 113, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "os": {"a": 0, "k": 0, "ix": 9, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "e": {"a": 0, "k": 0, "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(1).propertyIndex;\n$bm_rt = thisLayer(2)('Admin')(2)('ArcMath')(2)(grp)(p);"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "o": {"a": 0, "k": 100, "ix": 7, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar p = thisProperty.propertyIndex;\nvar grp = thisProperty.propertyGroup(2).propertyIndex;\n$bm_rt = content('Admin').content('ArcMath')('ADBE Vector Transform Group')(p);"}, "nm": "Transform"}], "nm": "Arc", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.972549019608, 0.6, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 19, "ix": 5, "x": "var $bm_rt;\nvar sFac = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2')('Parent Scale');\n$bm_rt = mul(value, sFac);"}, "lc": 2, "lj": 2, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "BaseHose", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Style", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::<PERSON><PERSON>", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [135, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "A", "np": 1, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [200, 200], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Hose 2::Shoulder", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "B", "np": 1, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "2.04", "np": 0, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false, "cl": "04"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Version", "np": 1, "cix": 2, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "sr", "sy": 1, "d": 1, "pt": {"a": 0, "k": 4, "ix": 3}, "p": {"a": 0, "k": [0, 0], "ix": 4}, "r": {"a": 0, "k": 0, "ix": 5}, "ir": {"a": 0, "k": 200, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Inner Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "is": {"a": 0, "k": 100, "ix": 8, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Bend Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "or": {"a": 0, "k": 200, "ix": 7, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('Outer Radius');\n} catch (err) {\n    $bm_rt = value;\n}"}, "os": {"a": 0, "k": 0, "ix": 9}, "ix": 1, "nm": "LineForCurve", "mn": "ADBE Vector Shape - Star", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0.01, "ix": 1}, "e": {"a": 0, "k": 24.99, "ix": 2}, "o": {"a": 0, "k": -90, "ix": 3, "x": "var $bm_rt;\n$bm_rt = -90;"}, "m": 1, "ix": 2, "nm": "<PERSON>", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1, "x": "var $bm_rt;\nvar s = thisProperty.propertyGroup(2)(2)(1)(7);\n$bm_rt = [\n    -s,\n    0\n];"}, "s": {"a": 0, "k": [100, 100], "ix": 3, "x": "var $bm_rt;\nvar flop;\ntry {\n    var eff = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var bendDir = eff('Bend Direction');\n    var autoFlop = eff('AutoFlop');\n    flop = bendDir > 0 ? 1 : -1;\n    autoFlop > 0 ? 0 : flop *= -1;\n    var s = flop == 1 ? [\n            -100,\n            100\n        ] : [\n            100,\n            100\n        ];\n    if (eff('Parent Scale') < 0) {\n        s = [\n            -s[0],\n            s[1]\n        ];\n    }\n    $bm_rt = s;\n} catch (err) {\n    $bm_rt = value;\n}\n;"}, "r": {"a": 0, "k": 45, "ix": 6, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    var baseRot = ctrl('Base Rotation');\n    var flop = content('Admin').content('ArcMath').transform.scale[0];\n    var rotOffset = flop < 0 ? -45 : 225;\n    $bm_rt = sum(baseRot, rotOffset);\n} catch (err) {\n    $bm_rt = value;\n}"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "ArcMath", "np": 2, "cix": 2, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2, "x": "var $bm_rt;\ntry {\n    var ctrl = thisComp.layer(thisLayer(2)('Admin')(2)('A')(2)(1)._name)(4)('RubberHose 2');\n    $bm_rt = ctrl('A');\n} catch (err) {\n    $bm_rt = value;\n}"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Admin", "np": 4, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 82, "ty": 4, "nm": "Neck 2", "parent": 84, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-19, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [19, 0], [0, 0], [0, 0]], "v": [[-138, -455.5], [-138, -411.5], [-117.5, -393], [-98.5, -409.5], [-90.5, -437]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 167, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-118.5, -400], "ix": 2}, "a": {"a": 0, "k": [-118.5, -400], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 83, "ty": 4, "nm": "Shadow", "parent": 82, "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-30.25, -3.5], [0, 0]], "o": [[0, 0], [30.25, 3.5], [0, 0]], "v": [[-136.75, -451.5], [-98.25, -412.25], [-83, -424.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 167, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.556862745098, 0.290196078431, 0.18431372549, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 84, "ty": 4, "nm": "Neck", "parent": 75, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [0], "e": [-5]}, {"i": {"x": [0.482], "y": [1]}, "o": {"x": [0.689], "y": [0]}, "n": ["0p482_1_0p689_0"], "t": 34.957, "s": [-5], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "n": ["0p667_1_0p167_0"], "t": 67, "s": [3], "e": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 163, "s": [3], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 200, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 240.93, "s": [-2], "e": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 280, "s": [0], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 320.93, "s": [-2], "e": [0]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [-61.667, -218.333, 0], "ix": 2}, "a": {"a": 0, "k": [-124, -399, 0], "ix": 1}, "s": {"a": 0, "k": [55.556, 55.556, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-19, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [19, 0], [0, 0], [0, 0]], "v": [[-138, -455.5], [-138, -411.5], [-117.5, -393], [-98.5, -409.5], [-90.5, -437]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 167, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "ml2": {"a": 0, "k": 4, "ix": 8}, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": true}, {"ty": "fl", "c": {"a": 0, "k": [0.647058823529, 0.356862745098, 0.227450980392, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-118.5, -400], "ix": 2}, "a": {"a": 0, "k": [-118.5, -400], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 85, "ty": 4, "nm": "Isolation Mode 23", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 47.473, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 94.945, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 142.418, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 189.891, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 237.363, "s": [2], "e": [-2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 284.836, "s": [-2], "e": [2]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 322, "s": [2], "e": [-2]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [1082, 1133, 0], "ix": 2}, "a": {"a": 0, "k": [165, 303, 0], "ix": 1}, "s": {"a": 0, "k": [175, 175, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[167.438, 301.978], [166.451, 301.815], [183.626, 196.679], [184.612, 196.841]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 86, "ty": 4, "nm": "Isolation Mode 25", "parent": 85, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[179.507, 223.579], [146.532, 199.217], [147.126, 198.412], [180.101, 222.774]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 87, "ty": 4, "nm": "Isolation Mode 24", "parent": 85, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[177.586, 238.503], [177.162, 237.597], [217.959, 218.552], [218.383, 219.458]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 88, "ty": 4, "nm": "Isolation Mode 22", "parent": 85, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -30.783], [-30.783, 0], [0, 30.784], [30.783, 0]], "o": [[0, 30.784], [30.783, 0], [0, -30.783], [-30.783, 0]], "v": [[121.636, 223.177], [177.374, 278.915], [233.112, 223.177], [177.374, 167.439]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.72549021244, 0.560784339905, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 89, "ty": 4, "nm": "Isolation Mode 19", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 0, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 34.371, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 72.361, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 106.734, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 144.723, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 179.096, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 217.086, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 251.457, "s": [-1], "e": [1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 289.447, "s": [1], "e": [-1]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "n": ["0p667_1_0p333_0"], "t": 325.629, "s": [-1], "e": [1]}, {"t": 360}], "ix": 10}, "p": {"a": 0, "k": [1043, 1135, 0], "ix": 2}, "a": {"a": 0, "k": [141, 302, 0], "ix": 1}, "s": {"a": 0, "k": [180, 180, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[140.628, 301.912], [135.501, 140.501], [136.501, 140.47], [141.628, 301.881]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 90, "ty": 4, "nm": "Isolation Mode 21", "parent": 89, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[136.307, 156.83], [123.918, 151.45], [124.316, 150.532], [136.705, 155.912]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 91, "ty": 4, "nm": "Isolation Mode 20", "parent": 89, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[137.231, 169.121], [136.567, 168.373], [159.344, 148.141], [160.008, 148.889]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188235297799, 0.239215686917, 0.447058826685, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 92, "ty": 4, "nm": "Isolation Mode 18", "parent": 89, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-6.54, -6.873], [-15.343, 14.599], [3.631, 13.189], [0, 0]], "o": [[14.598, 15.343], [10.604, -10.09], [0, 0], [0.479, 8.785]], "v": [[111.218, 187.709], [165.43, 189.057], [175.961, 151.066], [100.719, 163.386]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-14.599, -15.343], [-15.343, 14.599], [14.599, 15.343], [15.343, -14.599]], "o": [[14.598, 15.342], [15.342, -14.598], [-14.598, -15.342], [-15.342, 14.598]], "v": [[111.249, 187.71], [165.461, 189.057], [166.808, 134.845], [112.596, 133.498]], "c": true}, "ix": 2}, "nm": "Mask", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 4, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.031372550875, 0.749019622803, 0.54509806633, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 93, "ty": 4, "nm": "Isolation Mode 17", "parent": 89, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-14.599, -15.343], [-15.343, 14.599], [14.599, 15.343], [15.343, -14.599]], "o": [[14.598, 15.342], [15.342, -14.598], [-14.598, -15.342], [-15.342, 14.598]], "v": [[111.249, 187.71], [165.461, 189.057], [166.808, 134.845], [112.596, 133.498]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.78823530674, 0.611764729023, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 94, "ty": 4, "nm": "Isolation Mode 59", "parent": 98, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-34.778, 131.097], [-122.364, 131.097], [-122.364, 130.097], [-34.778, 130.097]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 95, "ty": 4, "nm": "Isolation Mode 58", "parent": 98, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-38.993, 189.071], [-126.579, 189.071], [-126.579, 188.071], [-38.993, 188.071]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 96, "ty": 4, "nm": "Isolation Mode 57", "parent": 98, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-43.658, 252.06], [-131.244, 252.06], [-131.244, 251.06], [-43.658, 251.06]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 97, "ty": 4, "nm": "Isolation Mode 56", "parent": 98, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-46.888, 301.934], [-47.886, 301.859], [-31.098, 75.209], [-30.1, 75.283]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}, {"ddd": 0, "ind": 98, "ty": 4, "nm": "Isolation Mode 55", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [616, 1116, 0], "ix": 2}, "a": {"a": 0, "k": [-86, 294, 0], "ix": 1}, "s": {"a": 0, "k": [175, 175, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-126.771, 301.934], [-127.769, 301.859], [-110.98, 75.209], [-109.982, 75.283]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.101960785687, 0.133333340287, 0.286274522543, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 600, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Pre-comp 1", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [87, 87, 100], "ix": 6}}, "ao": 0, "w": 1600, "h": 1200, "ip": 0, "op": 361, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 1, "nm": "White Solid 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [800, 600, 0], "ix": 2}, "a": {"a": 0, "k": [800, 600, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "sw": 1600, "sh": 1200, "sc": "transparent", "ip": 0, "op": 600, "st": 0, "bm": 0}], "markers": []}