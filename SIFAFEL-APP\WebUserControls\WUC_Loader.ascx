﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="WUC_Loader.ascx.cs" Inherits="SIFAFEL_APP.WebUserControls.WUC_Loader" %>
<style type="text/css">
    .swal2-popup {
        font-size: 0.85rem !important;
    }

    .amount {
        text-align: right !important;
    }

        .amount.total {
            font-weight: bold;
        }

    table {
        width: 100% !important;
    }

        table tfoot td.amount {
            text-align: right !important;
        }

            table tfoot td.amount.total {
                font-weight: bold !important;
            }

    tr td {
    }

        tr td .currency {
            float: left;
        }

        tr td .price {
            float: right;
        }
</style>
<%if (IsLoading)
    { %>
<div id="global-loader">
    <div class="whirly-loader"></div>
</div>
<%} %>
<div id="global-loader-msg" <%=(IsShowMessage ? "style='display:flex'" : "") %>>
    <div class="whirly-loader"></div>
    <br />
    <br />
    <h3 id="x-loader-message">...</h3>
</div>

<div class="modal fade" id="unauthorizedModal" tabindex="-1" aria-labelledby="unauthorizedModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unauthorizedModalLabel">Sesión Expirada</h5>
                <%--<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>--%>
            </div>
            <div class="modal-body">
                Tu sesión ha expirado o no tienes autorización para realizar esta acción. Por favor, inicia sesión nuevamente.
                <input type="text" class="form-control" disabled="disabled" placeholder="Usuario" />
                <input type="password" class="form-control" disabled="disabled" placeholder="Clave" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success">Continuar</button>
                <button type="button" class="btn btn-danger">Salir</button>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
    let pendingRequest = null; // Almacena la información de la solicitud pendiente

    $(document).ajaxError(function (event, jqxhr, settings, thrownError) {
        if (jqxhr.status === 401) {
            pendingRequest = {
                url: settings.url,
                type: settings.type,
                data: settings.data || null, // Datos enviados en la solicitud
                contentType: settings.contentType || 'application/json', // Tipo de contenido
                headers: settings.headers || {}, // Encabezados personalizados
            };
            $('#unauthorizedModal').modal('show');
        } else if (thrownError === 'ERR_CONNECTION_REFUSED') {
            console.error('Error: Connection refused. Unable to reach the server.');
        } else {
            console.error(`Unhandled AJAX error: ${thrownError}`);
        }
    });

    $(document).ready(function () {

        $('#loginButton').on('click', function () {
            const username = $('#username').val();
            const password = $('#password').val();

            if (!username || !password) {
                $('#loginError').text('Por favor, ingresa tus credenciales.').removeClass('d-none');
                return;
            }

            $.ajax({
                url: '/api/login',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ username, password }),
                success: function () {
                    $('#unauthorizedModal').modal('hide');
                    $('#loginError').addClass('d-none');

                    if (pendingRequest) {
                        $.ajax(pendingRequest).done(function (response) {
                            console.log('Solicitud completada después de la autenticación:', response);
                            pendingRequest = null;
                        }).fail(function (error) {
                            console.error('La solicitud falló nuevamente:', error);
                        });
                    }
                },
                error: function () {
                    $('#loginError').text('Credenciales inválidas. Intenta nuevamente.').removeClass('d-none');
                },
            });
        });

        $('#logoutButton').on('click', function () {
            window.location.href = '/logout';
        });
    });

    window.addEventListener('error', function (event) {
        console.log(event.target.tagName);
        if (event.target.tagName === 'IMG') {
            //event.target.src = `${_url_cdn}img/products/icon.png`;
            //event.preventDefault();
            //PENDIENTE MEJORAR
        }
    }, true);

    function __GetDateToday() {
        let fechaSeleccionada = new Date();
        let dia = fechaSeleccionada.getDate().toString().padStart(2, '0');
        let mes = (fechaSeleccionada.getMonth() + 1).toString().padStart(2, '0');
        let anio = fechaSeleccionada.getFullYear();

        return `${dia}/${mes}/${anio}`;
    }

    function __GetInputValue(value) {
        return value === '' || value === undefined ? null : value;
    }

    Date.prototype.toDateInputValue = (function () {
        var local = new Date(this);
        local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
        return local.toJSON().slice(0, 10);
    });

    function __Progress(message) {
        $("#x-loader-message").html(message);
        $("#global-loader-msg").fadeIn().css("display", "flex");
    }

    function __ProgressOff() {
        $("#global-loader-msg").fadeOut(400, function () {
            $("#x-loader-message").html("");
        });
    }

    function __Format(p_fecha) {
        if (!p_fecha) return null;

        if (/^\d{4}-\d{2}-\d{2}$/.test(p_fecha)) {
            const partes_fecha = p_fecha.split("-");
            return `${partes_fecha[2]}/${partes_fecha[1]}/${partes_fecha[0]}`;
        }

        if (/^\d{2}\/\d{2}\/\d{4}$/.test(p_fecha)) {
            return p_fecha;
        }

        return p_fecha;
    }

    function __setMaxDateToToday(p_selector, p_retroactive_days = 0, p_just_today = false) {
        let today = new Date();

        const formattedToday = today.getFullYear() + '-' +
            String(today.getMonth() + 1).padStart(2, '0') + '-' +
            String(today.getDate()).padStart(2, '0');

        if (p_just_today) {
            $(p_selector).attr('min', formattedToday);
            $(p_selector).attr('max', formattedToday);
        } else {
            if (p_retroactive_days > 0) {
                let minDate = new Date(today);
                minDate.setDate(today.getDate() - p_retroactive_days);

                const formattedMinDate = minDate.getFullYear() + '-' +
                    String(minDate.getMonth() + 1).padStart(2, '0') + '-' +
                    String(minDate.getDate()).padStart(2, '0');

                $(p_selector).attr('min', formattedMinDate);
            } else {
                $(p_selector).removeAttr('min');
            }
            $(p_selector).attr('max', formattedToday);
        }
        $(p_selector).val('').removeAttr('value');
    }

    function MoneyFormat(moneda, price) {
        return "<span class=\"currency\">" + moneda + "</span>" +
            "<span class=\"price\">" + PriceNumberFormat(price) + "</span>";
    }

    function getParameterByName(name, url) {
        if (!url) url = window.location.href; // Si no se pasa una URL, usamos la actual
        name = name.replace(/[\[\]]/g, '\\$&'); // Escapar caracteres especiales
        var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
        var results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return ''; // Si el parámetro no tiene valor, retornamos una cadena vacía
        return decodeURIComponent(results[2].replace(/\+/g, ' ')); // Devolvemos el valor del parámetro
    }

    function base64_file(base64str, fileName, contentType) {
        if (window.ReactNativeWebView) {
            // APP
            console.log("Metodo App");
            if (base64str && base64str.length > 0) {
                const [name, ext] = fileName.split('.');
                const response = {
                    d: base64str,
                    title: name,
                    ext: ext
                };
                window.ReactNativeWebView.postMessage(JSON.stringify(response));
            } else {
                const errorResponse = { error: "No se puede descargar el documento seleccionado" };
                window.ReactNativeWebView.postMessage(JSON.stringify(errorResponse));
            }
        } else {
            // NAVEGADOR
            console.log("Metodo Navegador");
            try {
                const binary = atob(base64str.replace(/\s/g, ''));
                const len = binary.length;
                const buffer = new ArrayBuffer(len);
                const view = new Uint8Array(buffer);

                for (let i = 0; i < len; i++) {
                    view[i] = binary.charCodeAt(i);
                }

                const blob = new Blob([view], { type: contentType });

                if (navigator.msSaveBlob) {
                    // IE/Edge
                    navigator.msSaveBlob(blob, fileName);
                } else {
                    // Otros navegadores
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = fileName;

                    document.body.appendChild(link);
                    link.click();

                    setTimeout(() => {
                        document.body.removeChild(link);
                        window.URL.revokeObjectURL(link.href);
                    }, 1500);
                }
            } catch (error) {
                console.error("Ocurrió un error al procesar el archivo:", error);
            }
        }
    }
    
    function scrollToElement(selector) {
        const element = document.querySelector(selector);
        if (element) {
            element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
            });
        } else {
            console.warn(`Elemento con selector "${selector}" no encontrado.`);
        }
    }

</script>
