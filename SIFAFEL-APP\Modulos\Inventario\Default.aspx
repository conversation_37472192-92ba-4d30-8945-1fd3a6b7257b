﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.Default" %>

<%@ Register Src="~/WebUserControls/WUC_InfoProduct.ascx" TagPrefix="uc1" TagName="WUC_InfoProduct" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <uc1:WUC_InfoProduct runat="server" ID="WUC_InfoProduct" />

    <%-- HEADER --%>
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="package" class="me-2"></i>Control de Inventario</h4>
                <h6>Lista de productos</h6>
            </div>
        </div>
        <ul class="table-top-head">
            <li>
                <div class="page-btn">
                    <a href="NuevoProducto.aspx" class="btn btn-added" title="Agregar nuevo producto">
                        <i data-feather="plus-circle" class="me-2"></i>Nuevo producto
                    </a>
                </div>
            </li>
            <li>
                <div class="page-btn">
                    <a href="Compras.aspx" class="btn btn-success" title="Administrar compras">
                        <i data-feather="shopping-bag" class="me-2"></i>Compras
                    </a>
                </div>
            </li>
            <li>
                <a data-bs-toggle="tooltip" data-bs-placement="top" title="Collapse" id="collapse-header">
                    <i data-feather="chevron-up" class="feather-chevron-up"></i>
                </a>
            </li>
        </ul>
    </div>

    <%-- DETALLE --%>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <%--<h4 class="card-title"><i data-feather="clipboard"></i>&nbsp;Productos</h4>--%>

                    <div class="col-md-12">
                        <div class="table-responsive">
                            <table id="tblInvProducto" class="table">
                                <thead class="">
                                    <tr>
                                        <th>Id Producto</th>
                                        <th><i class="fa fa-barcode"></i>&nbsp;Código</th>
                                        <%--<th class="no-sort"><i class="fa fa-images"></i></th>--%>
                                        <th><i class="fa fa-box"></i>&nbsp;Producto</th>
                                        <th>Marca</th>
                                        <th>Modelo</th>
                                        <th>Año</th>
                                        <th>Cantidad</th>
                                        <th>Precio Venta</th>
                                        <th class="no-sort">&nbsp;</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            //Inicializar la tabla de inventario producto
            fnInitTableInvProducto();

            //cargar datos a la tabla de inventario producto
            GetInvProductos();
        });

        function fnInitTableInvProducto() {
            $("#tblInvProducto").DataTable({
                "language": {
                    search: '',
                    searchPlaceholder: "Buscar producto",
                    info: "Mostrando _START_ a _END_ de _TOTAL_ registros",
                    lengthMenu: "Mostrar _MENU_ registros",
                    paginate: {
                        next: '<i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                info: true,
                paging: true,
                autoWidth: true,
                //dom: 'Bfrtip',
                //dom: 'B<"top"f>rt<"bottom"lp><"clear">',
                //lengthMenu: [10, 25, 50, 100],
                buttons: [
                    {
                        extend: 'excelHtml5',
                        autoFilter: true,
                        sheetName: 'Inventario',
                        className: 'btn btn-success btn-sm',
                        //exportOptions: {
                        //    columns: [1, 2, 3, 4, 5, 6, 7, 8, 9]
                        //},
                        text: '<i class="fa fa-file-excel"></i>&nbsp;Excel',
                        attr: {
                            title: 'Exportar productos a Excel',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    },
                    {
                        extend: 'print',
                        className: 'btn btn-info btn-sm',
                        text: '<i class="fa fa-print"></i>&nbsp;Imprimir',
                        attr: {
                            title: 'Exportar productos a PDF',
                        },
                        title: `Inventario - ${new Date().toLocaleDateString('es-ES')}`
                    }
                ],
                columnDefs: [
                    {
                        targets: [0],
                        visible: false
                    },
                    {
                        targets: [1],
                        //ordering: false
                    },
                    {
                        targets: [2],
                        className: 'productimgname',
                        //width: '2px'
                    }
                ],
                columns: [
                    { data: "id_producto" },
                    { data: "codigo" },
                    {
                        data: function (item) {
                            let img_producto = '';
                            if (!item.img_producto) {
                                img_producto = `${_url_cdn}img/products/icon.png`;
                            } else {
                                img_producto = item.img_producto;
                            }
                            return `<div class="view-product me-2">
                                        <a href="#!">
                                            <img src="${img_producto}" alt="" onerror="this.onerror=null;this.src='${_url_cdn}img/products/icon.png';">
                                        </a>
                                    </div>
                                    <a href="#!" class="view-info-product">${item.nombre}</a>`;
                        }
                    },
                    //{ data: "nombre" },
                    { data: "marca" },
                    { data: "modelo" },
                    { data: "anio" },
                    { data: "stock_actual" },
                    {
                        data: "precio_unitario",
                        render: function (data, type, row, meta) {
                            return `<span class="tbl_lbl_precio_total">${MoneyFormat(row.moneda, data)}</span>`;
                        }
                    },
                    {
                        data: function (item) {
                            return `<button class="btn btn-success btn-sm" type="button" title="Editar" onclick="location.href='/Modulos/Inventario/NuevoProducto.aspx?cod=${mtdEnc(mtdEnc(item.codigo))}'"><span class="fa fa-edit"></span></button>
                                    <button class="btn btn-info btn-sm" type="button" role="button" title="Ver detalle"><span class="fa fa-eye"></span></button>
                                    <button class="btn btn-danger btn-sm" type="button" role="button" title="Eliminar"><span class="fa fa-trash"></span></button>
                                    `;
                        }
                    }
                ],
                rowCallback: function (row, data) {
                    $(row).find(".view-info-product").on("click", function () {
                        __InfoProduct(data);
                    });
                }
            });
        }

        function GetInvProductos() {
            //__Progress('Buscando inventario de productos...');
            $.ajax({
                url: `${_url_redirect}Controllers/Inventario.ashx?mth=${mtdEnc("get/products")}`,
                data: null,
                type: "GET",
                async: false,
                contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                dataType: "json",
                beforeSend: function () {
                    //Holdon_Open("Buscando...");
                },
                success: function (result) {
                    if (result.type == "success") {
                        $('#tblInvProducto').dataTable().fnClearTable();
                        $('#tblInvProducto').DataTable().search("").draw();
                        $('#tblInvProducto').dataTable().fnAddData(result.data);
                    }
                    else {
                        Swal.fire({ icon: result.type, text: result.text });
                    }
                },
                error: function (result) {
                    console.error('error:', result);
                    Swal.fire({
                        icon: 'error',
                        text: 'Ocurrio un error al iniciar el proceso de búsqueda.'
                    });
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }
    </script>
</asp:Content>
