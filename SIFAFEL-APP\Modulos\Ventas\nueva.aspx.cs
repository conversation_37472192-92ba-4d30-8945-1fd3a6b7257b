﻿using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Modulos.Ventas
{
    public partial class nueva : System.Web.UI.Page
    {
        /// <summary>
        /// Información del usuario autenticado.
        /// </summary>
        public InfoUsuario info_usuario = new InfoUsuario();

        /// <summary>
        /// Monto de IVA
        /// </summary>
        public double MONTO_IVA { get; set; }

        /// <summary>
        /// Indicador para saber si se está editando una factura.
        /// </summary>
        public bool EDIT_FACTURA { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void Page_Load(object sender, EventArgs e)
        {
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                info_usuario = sessionManager.Get();

                if (!IsPostBack)
                {
                    MONTO_IVA = 1.12;
                }
            }

            EDIT_FACTURA = (Request.QueryString["id"] != null);
        }
    }
}