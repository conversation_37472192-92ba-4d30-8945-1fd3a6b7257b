﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{17873221-1A30-4B32-A3BE-4A3881CD9A2B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SIFAFEL_MODEL</RootNamespace>
    <AssemblyName>SIFAFEL-MODEL</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Castle.Core, Version=5.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.5.1.1\lib\net462\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Moq, Version=4.20.72.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
      <HintPath>..\packages\Moq.4.20.72\lib\net462\Moq.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DataTransferObject\DirPaisDTO.cs" />
    <Compile Include="DataTransferObject\Enum.cs" />
    <Compile Include="DataTransferObject\ModBancoDTO.cs" />
    <Compile Include="DataTransferObject\ModCajaDTO.cs" />
    <Compile Include="DataTransferObject\ModCajaOperacionDTO.cs" />
    <Compile Include="DataTransferObject\ModCajaTransaccionDTO.cs" />
    <Compile Include="DataTransferObject\ModCajaUsuarioDTO.cs" />
    <Compile Include="DataTransferObject\ModCompraDTO.cs" />
    <Compile Include="DataTransferObject\ModCompraExtendidaDTO.cs" />
    <Compile Include="DataTransferObject\ModCompraVentaDTO.cs" />
    <Compile Include="DataTransferObject\ModContribuyenteDTOComparer.cs" />
    <Compile Include="DataTransferObject\ModMedioPagoContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\ModMedioPagoDTO.cs" />
    <Compile Include="DataTransferObject\ModMonedaContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\ModMonedaDTO.cs" />
    <Compile Include="DataTransferObject\ModProductoCategoriaGenerico.cs" />
    <Compile Include="DataTransferObject\ModProductoModeloDTO.cs" />
    <Compile Include="DataTransferObject\ModProductoMarcaDTO.cs" />
    <Compile Include="DataTransferObject\ModProductoCategoriaDTO.cs" />
    <Compile Include="DataTransferObject\ModProductoSucursalDTO.cs" />
    <Compile Include="DataTransferObject\ModVentaDTO.cs" />
    <Compile Include="DataTransferObject\SecAccionDTO.cs" />
    <Compile Include="DataTransferObject\ConfContratoDTO.cs" />
    <Compile Include="DataTransferObject\ConfContratoPagoDTO.cs" />
    <Compile Include="DataTransferObject\ConfContratoPagoMedioDTO.cs" />
    <Compile Include="DataTransferObject\ConfLvalDTO.cs" />
    <Compile Include="DataTransferObject\DirDepartamentoDTO.cs" />
    <Compile Include="DataTransferObject\DirMunicipioDTO.cs" />
    <Compile Include="DataTransferObject\DirRegionDTO.cs" />
    <Compile Include="DataTransferObject\DirZonaDTO.cs" />
    <Compile Include="DataTransferObject\ModClienteDTO.cs" />
    <Compile Include="DataTransferObject\ModClienteContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\ModContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\ModContribuyenteTipoDTO.cs" />
    <Compile Include="DataTransferObject\ModFacturaDTO.cs" />
    <Compile Include="DataTransferObject\ModFacturaDetalleDTO.cs" />
    <Compile Include="DataTransferObject\ModProductoDTO.cs" />
    <Compile Include="DataTransferObject\ModProveedorDTO.cs" />
    <Compile Include="DataTransferObject\ModProveedorContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\ModSucursalDTO.cs" />
    <Compile Include="DataTransferObject\ModSucursalUsuarioDTO.cs" />
    <Compile Include="DataTransferObject\SecMenuDTO.cs" />
    <Compile Include="DataTransferObject\SecMenuRolDTO.cs" />
    <Compile Include="DataTransferObject\SecModuloDTO.cs" />
    <Compile Include="DataTransferObject\SecModuloContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\SecModuloTipoDTO.cs" />
    <Compile Include="DataTransferObject\SecPermisoDTO.cs" />
    <Compile Include="DataTransferObject\SecPermisoUsuarioDTO.cs" />
    <Compile Include="DataTransferObject\SecRolDTO.cs" />
    <Compile Include="DataTransferObject\SecRolAccionDTO.cs" />
    <Compile Include="DataTransferObject\SecRolTipoUsuarioDTO.cs" />
    <Compile Include="DataTransferObject\SecUsuarioDTO.cs" />
    <Compile Include="DataTransferObject\SecUsuarioLogDTO.cs" />
    <Compile Include="DataTransferObject\SecUsuarioTipoDTO.cs" />
    <Compile Include="DataTransferObject\ModCompraVentaProductoDTO.cs" />
    <Compile Include="DataTransferObject\VWInventarioContribuyenteDTO.cs" />
    <Compile Include="DataTransferObject\VWModVentaContribuyenteDTO.cs" />
    <Compile Include="Data_Model_API\Autenticacion\_InfoMenuModulo.cs" />
    <Compile Include="Data_Model_API\Autenticacion\_InfoUsuario.cs" />
    <Compile Include="Data_Model_API\CustomDateConverter.cs" />
    <Compile Include="Data_Model_API\message_response.cs" />
    <Compile Include="Data_Model_API\Modulos\_MedioPago.cs" />
    <Compile Include="Data_Model_API\Modulos\_ModuloCaja.cs" />
    <Compile Include="Data_Model_API\Token\reCAPTCHA_Response.cs" />
    <Compile Include="Data_Model_API\Token\token_reponse.cs" />
    <Compile Include="Data_Model_API\Token\token_request.cs" />
    <Compile Include="Data_Model_API_MS\File\request_file.cs" />
    <Compile Include="Data_Model_API_MS\File\request_file_list.cs" />
    <Compile Include="Data_Model_API_MS\File\response_file.cs" />
    <Compile Include="Data_Model_API_MS\Report\ReportDataIn.cs" />
    <Compile Include="Data_Model_API_MS\Report\ReportDataResult.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utils\Utilidades.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Data_Model_API_MS\Communication\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>