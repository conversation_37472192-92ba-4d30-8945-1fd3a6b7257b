﻿using Newtonsoft.Json;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.SessionState;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Summary description for auth
    /// </summary>
    public class auth : IHttpHandler, IRequiresSessionState
    {
        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";

            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "login":
                    context.Response.Write(JsonConvert.SerializeObject(Login()));
                    break;
                default:
                    context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
                    context.Response.Write(JsonConvert.SerializeObject(new { message = "Método no soportado" }));
                    break;
            }
        }

        public bool IsReusable { get { return false; } }

        /// <summary>
        /// Proceso principal para la autenticación de usuario
        /// </summary>
        /// <returns></returns>
        public response_login Login()
        {
            response_login response = new response_login();
            response_message message = new response_message();

            string vUsuario = RequestParameters.GetValue("user");
            string vPassword = RequestParameters.GetValue("pass");
            string vToken = RequestParameters.GetValue("token_id");

            if (string.IsNullOrEmpty(vUsuario) || string.IsNullOrEmpty(vPassword))
            {
                message.type = TIPO_MENSAJE.ERROR;
                message.text = "Por favor ingrese sus credenciales";
                response.message = message;
                return response;
            }

            if (new black_list_sql(vUsuario).estado)
            {
                message.type = TIPO_MENSAJE.ERROR;
                message.text = "Usuario y/o contraseña no están correctos";
                response.message = message;
                return response;
            }

            bool valid_recaptcha = ConfigurationManager.AppSettings["recaptcha_validate"].Equals("N") || _VerifyCaptcha(vToken).success;

            if (!valid_recaptcha)
            {
                message.type = TIPO_MENSAJE.ERROR;
                message.text = "Token inválido";
                response.message = message;
                return response;
            }

            try
            {
                InfoUsuario info_usuario = new InfoUsuario();
                token_response token = new token_response();
                token_request CredentialUser = new token_request
                {
                    user = vUsuario,
                    password = vPassword,
                    grant_type = ConfigurationManager.AppSettings["API_GRANT_TYPE"],
                    origin = ConfigurationManager.AppSettings["ORIGIN_APP"]
                };

                rest_client rest = new rest_client("api_url", "api_token_prefix", HttpContext.Current.Request.UserAgent);
                rest.AddParamHeaders("VALIDATION_TYPE", ConfigurationManager.AppSettings["API_VALIDATION_TYPE"]);

                token = rest.GenericRestClient<token_response, token_request>("api_sec_authentication_login", "", TypeMethod.Post, CredentialUser);
                rest.Clear();

                if (token.message.type == TIPO_MENSAJE.SUCCESS)
                {
                    rest.TokenPrefixApiRest = token.access_type;
                    info_usuario = rest.GenericRestClient<InfoUsuario, DBNull>("api_sec_authentication_user_info", token.access_token, TypeMethod.Post);

                    rest.Clear();
                    rest.TokenPrefixApiRest = token.access_type;
                    info_usuario.menu = rest.GenericRestClient<List<_InfoMenuModulo>, DBNull>("api_sec_authentication_user_menu", token.access_token, TypeMethod.Post);

                    HttpContext.Current.Session["AuthInfoToken"] = token;
                    HttpContext.Current.Session["AuthInfoUser"] = info_usuario;

                    /*MEJORA*/
                    SessionManager sessionManager = new SessionManager();

                    if (sessionManager.Valid())
                    {
                        InfoUsuario infoUsuario = new InfoUsuario();
                        infoUsuario = sessionManager.Get();

                        if (infoUsuario.sucursal_session == null && infoUsuario.contribuyentes != null)
                        {
                            InfoUsuario.InfoContribuyente.InfoSucursal sucursal = null;

                            sucursal = infoUsuario.id_sucursal_default.HasValue && infoUsuario.id_sucursal_default > 0
                                    ? infoUsuario.contribuyentes.SelectMany(c => c.sucursales).FirstOrDefault(s => s.id == infoUsuario.id_sucursal_default)
                                    : infoUsuario.contribuyentes.SelectMany(c => c.sucursales).FirstOrDefault();

                            if (sucursal != null)
                            {
                                sessionManager.SetIdSucursal(sucursal.id.ToString());
                            }
                        }
                    }
                    message.type = TIPO_MENSAJE.SUCCESS;
                    message.text = "OK";
                    response.url_redirect = ConfigurationManager.AppSettings["url_redirect"] + (info_usuario.url_default ?? "");
                }
                else
                {
                    message.type = TIPO_MENSAJE.ERROR;
                    message.text = "Usuario y/o contraseña no están correctos";
                }
            }
            catch (Exception ex)
            {
                message.type = TIPO_MENSAJE.ERROR;
                message.text = "Error en el proceso de autenticación";
            }

            response.message = message;
            return response;
        }

        /// <summary>
        /// Validación de token reCAPTCHA
        /// </summary>
        /// <param name="pToken"></param>
        /// <returns></returns>
        private reCAPTCHA_Response _VerifyCaptcha(string pToken)
        {
            string secret_key = ConfigurationManager.AppSettings["recaptcha_key_secret"];
            reCAPTCHA_Response response = new reCAPTCHA_Response();
            ServicePointManager.Expect100Continue = true;
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

            string url = $"https://www.google.com/recaptcha/api/siteverify?secret={secret_key}&response={pToken}";

            try
            {
                string s_response = (new WebClient()).DownloadString(url);
                response = JsonConvert.DeserializeObject<reCAPTCHA_Response>(s_response);
            }
            catch (Exception)
            {
                response.success = false;
            }
            return response;
        }
    }
}
