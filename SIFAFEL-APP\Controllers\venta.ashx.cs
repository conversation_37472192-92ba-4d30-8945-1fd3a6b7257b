﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.api_response;
using SIFAFEL_CORE.core;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Modulos;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.DataTransferObject;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services.Description;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Handler de venta
    /// </summary>
    public class venta : SessionHandler
    {
        public InfoUsuario info_usuario = new InfoUsuario();

        public override void HandleRequest(HttpContext context)
        {
            info_usuario = context.Session["AuthInfoUser"] as InfoUsuario;
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "get/products":
                    context.Response.Write(JsonConvert.SerializeObject(GetProducts()));
                    break;
                case "get/product/by/id":
                    context.Response.Write(JsonConvert.SerializeObject(GetProductByCode()));
                    break;
                case "get/customers":
                    context.Response.Write(JsonConvert.SerializeObject(GetCustomers()));
                    break;
                case "get/customer/by/nit":
                    context.Response.Write(JsonConvert.SerializeObject(GetCustomerByNIT()));
                    break;
                case "save/sale":
                    context.Response.Write(JsonConvert.SerializeObject(SaveSale()));
                    break;
                case "get/medio/pago/sucursal":
                    context.Response.Write(JsonConvert.SerializeObject(GetMediosPago()));
                    break;
                case "get/sales":
                    context.Response.Write(JsonConvert.SerializeObject(GetSales()));
                    break;
                case "get/rpt":
                    context.Response.Write(JsonConvert.SerializeObject(GetReportDocument()));
                    break;
                default:
                    if (context.Request["id"] != null)
                    {
                        context.Response.Write(JsonConvert.SerializeObject(GetInfoSale()));
                    }
                    break;
            }
        }

        /// <summary>
        /// Recupera los productos
        /// </summary>
        /// <returns></returns>
        public response GetProducts()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                IEnumerable<VWInventarioContribuyenteDTO> productos = null;

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                    rest.Add("pIdSucursal", sessionManager.GetIdSucursal().ToString());
                    rest.Add("pSoloExistencia", "S");

                    productos = rest.GenericRestClient<IEnumerable<VWInventarioContribuyenteDTO>, DBNull>("api_inventory_get_products",
                                                                                                          token.access_token,
                                                                                                          TypeMethod.Get);
                    if (productos.Count() > 0)
                    {
                        response.data = productos;
                        response.title = "";
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.title = "Código no registrado";
                        response.text = "Producto no existente en el inventario, ¿Desea agregarlo? ";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {

                }
            }
            return response;
        }

        /// <summary>
        /// Recupera un producto por el codigo
        /// </summary>
        /// <returns></returns>
        public response GetProductByCode()
        {
            response message = new response();
            string PRODUCT_CODE = RequestParameters.GetValue("productCode");

            if (!string.IsNullOrWhiteSpace(PRODUCT_CODE))
            {
                SessionManager sessionManager = new SessionManager();
                TokenManager tokenManager = new TokenManager();

                if (sessionManager.Valid() && tokenManager.Valid())
                {
                    try
                    {
                        token_response token = tokenManager.Get();
                        List<VWInventarioContribuyenteDTO> productos = new List<VWInventarioContribuyenteDTO>();

                        rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                        rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                        rest.Add("pIdSucursal", sessionManager.GetIdSucursal().ToString());
                        rest.Add("pCodProduct", PRODUCT_CODE);

                        productos = rest.GenericRestClient<List<VWInventarioContribuyenteDTO>, DBNull>("api_mod_get_producto", token.access_token, TypeMethod.Get);
                        message.data = productos;

                        if (productos.Count() > 0)
                        {
                            message.title = "";
                            message.text = "La búsqueda se realizo correctamente.";
                            message.type = TIPO_MENSAJE.SUCCESS;
                        }
                        else
                        {
                            message.title = "Código no registrado";
                            message.text = "Comuníquese con su encargado para el registro del producto";
                            message.type = TIPO_MENSAJE.WARNING;
                        }
                    }
                    catch (Exception ex)
                    {
                        message.title = "";
                        message.text = "Ocurrio un error al momento de realizar la búsqueda del producto." + ex.Message;
                        message.type = TIPO_MENSAJE.ERROR;
                    }
                }
            }

            return message;
        }

        /// <summary>
        /// Recupera todos los clientes
        /// </summary>
        /// <returns></returns>
        public response GetCustomers()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                IEnumerable<ModClienteDTO> clientes = null;

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    //rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());

                    clientes = rest.GenericRestClient<IEnumerable<ModClienteDTO>, DBNull>("api_mod_cliente_get_list", token.access_token, TypeMethod.Get);

                    if (clientes.Count() > 0)
                    {
                        response.data = clientes;
                        response.title = "";
                        response.text = "La búsqueda se realizo correctamente.";
                        response.type = "success";
                    }
                    else
                    {
                        response.title = "Código no registrado";
                        response.text = "Comuníquese con su encargado para el registro del producto";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {

                }
            }
            return response;
        }

        /// <summary>
        /// Recupera el cliente segun el NIT
        /// </summary>
        /// <returns></returns>
        public response GetCustomerByNIT()
        {
            response message = new response();
            string NIT_CLIENTE = RequestParameters.GetValue("nit");

            if (!string.IsNullOrWhiteSpace(NIT_CLIENTE))
            {
                SessionManager sessionManager = new SessionManager();
                TokenManager tokenManager = new TokenManager();

                if (sessionManager.Valid() && tokenManager.Valid())
                {
                    try
                    {
                        token_response token = tokenManager.Get();
                        ModClienteDTO cliente = new ModClienteDTO();

                        rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                        //rest.Add("pIdContribuyente", sessionManager.GetIdContribuyente().ToString());
                        rest.Add("nit", NIT_CLIENTE);

                        cliente = rest.GenericRestClient<ModClienteDTO, DBNull>("api_mod_cliente_getByNIT", token.access_token, TypeMethod.Get);
                        message.data = cliente;

                        if (cliente?.id_cliente > 0)
                        {
                            message.title = "";
                            message.text = "La búsqueda se realizo correctamente.";
                            message.type = TIPO_MENSAJE.SUCCESS;
                        }
                        else
                        {
                            message.title = "Código no registrado";
                            message.text = "Comuníquese con su encargado para el registro del producto";
                            message.type = TIPO_MENSAJE.WARNING;
                        }
                    }
                    catch (Exception ex)
                    {
                        message.title = "";
                        message.text = "Ocurrio un error al momento de realizar la búsqueda del producto." + ex.Message;
                        message.type = TIPO_MENSAJE.ERROR;
                    }
                }
            }

            return message;
        }

        /// <summary>
        /// Recuperar los medios de pago de una caja
        /// </summary>
        /// <returns></returns>
        public response GetMediosPago()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                try
                {
                    token_response token = tokenManager.Get();

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    rest.Add("p_id_sucursal", sessionManager.GetIdSucursal().ToString());

                    response = rest.GenericRestClient<response, DBNull>("api_mod_medios_pago_sucursal", token.access_token, TypeMethod.Post);

                }
                catch (Exception ex)
                {

                }
            }

            return response;
        }

        /// <summary>
        /// Proceso principal para guardar una venta/cotizacion
        /// </summary>
        /// <returns></returns>
        public response SaveSale()
        {
            response message = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                try
                {
                    ModVentaDTO datosVenta = new ModVentaDTO();
                    datosVenta = RequestParameters.GetObject<ModVentaDTO>("data");

                    datosVenta.encabezado.fecha = DateTime.Now.Date.ToString();
                    datosVenta.id_contribuyente = sessionManager.GetIdContribuyente();
                    datosVenta.id_sucursal = sessionManager.GetIdSucursal();

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                    message = rest.GenericRestClient<response, ModVentaDTO>("api_mod_venta_guardar", token.access_token, TypeMethod.Post, datosVenta);

                    if (message.type == TIPO_MENSAJE.SUCCESS)
                    {
                        message.title = "Operación completada";
                        message.text = "La transacción se realizó exitosamente.";

                        try
                        {
                            //SIFAFEL_CORE.RPT.Facturacion reporte_facturacion = new SIFAFEL_CORE.RPT.Facturacion();
                            //var reporteFactura = reporte_facturacion.rpt_factura(token.access_token, HttpContext.Current.Request.UserAgent, token.access_type, message.Value.ToString());
                            //message.fileByte = reporteFactura.fileByte;
                            //message.base64File = reporteFactura.base64File;

                            //if (message.fileByte?.Length > 0)
                            //{
                            //    //Print Preview File
                            //    string fileName = "Recibo_" + message.Value;
                            //    //TempData.Clear();
                            //    //TempData["file"] = message.fileByte;
                            //    //TempData["fileName"] = fileName + ".pdf";
                            //    //TempData.Keep();

                            //    ////Downoald File after save Invoice
                            //    HttpContext.Current.Response.AddHeader("ContentType", "application/pdf");
                            //    HttpContext.Current.Response.AddHeader("FileName", string.Format("{0}.{1}", fileName, "pdf"));

                            //    //message.report = new responseReport();
                            //    //message.report.titleReport = fileName;
                            //    //message.report.fileSource = ConfigurationManager.AppSettings["url_redirect"] + "ReportViewer/ReportFactura";
                            //}
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }
                catch (Exception ex)
                {
                    message.title = "";
                    message.text = ex.Message.ToString();
                    message.type = TIPO_MENSAJE.ERROR;
                }
            }
            return message;
        }

        /// <summary>
        /// Proceso principal para obtener las ventas
        /// </summary>
        /// <returns></returns>
        public response GetSales()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();
                List<VWModVentaContribuyenteDTO> sales = new List<VWModVentaContribuyenteDTO>();

                try
                {
                    FiltroVentasDTO datosVenta = new FiltroVentasDTO();
                    datosVenta = RequestParameters.GetObject<FiltroVentasDTO>("data");

                    rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);

                    datosVenta.id_contribuyente = sessionManager.GetIdContribuyente();
                    datosVenta.id_sucursal = sessionManager.GetIdSucursal();

                    sales = rest.GenericRestClient<List<VWModVentaContribuyenteDTO>, FiltroVentasDTO>("api_mod_venta_listado", token.access_token, TypeMethod.Post, datosVenta);

                    if (sales.Count() > 0)
                    {
                        DataTable dt = SecurityData.ConvertToDataTable(sales);
                        SecurityData.AddSecretKeyColumn(dt, "id_factura");
                        response.data = dt;
                        response.text = "La búsqueda se realizó correctamente.";
                        response.type = TIPO_MENSAJE.SUCCESS;
                    }
                    else
                    {
                        response.text = "No se pudo obtener el listado de ventas.";
                        response.type = TIPO_MENSAJE.WARNING;
                    }
                }
                catch (Exception ex)
                {
                    response.title = "";
                    response.text = $"Error al obtener el listado de ventas: {ex.Message}";
                    response.type = TIPO_MENSAJE.ERROR;
                }
            }
            return response;
        }

        /// <summary>
        /// Recupera la información de una factura
        /// </summary>
        /// <returns></returns>
        public response GetInfoSale()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                string KEY_FACTURA = encript_core.GetSimple_Context("id");
                string ID_FACTURA = SecretKeyManager.Decrypt(KEY_FACTURA);

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_token, HttpContext.Current.Request.UserAgent);
                    rest.Add("p_id_factura", ID_FACTURA);

                    response = rest.GenericRestClient<response, DBNull>("api_mod_venta_info", token.access_token, TypeMethod.Get);
                }
                catch (Exception ex)
                {

                }
            }
            return response;
        }
        public response_file GetReportDocument()
        {
            response_file response = new response_file();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                string KEY_FACTURA = encript_core.GetSimple_Context("id");
                string ID_FACTURA = SecretKeyManager.Decrypt(KEY_FACTURA);

                try
                {
                    rest_client rest = new rest_client("api_url", token.access_token, HttpContext.Current.Request.UserAgent);
                    rest.Add("p_id_factura", ID_FACTURA);
                    rest.Add("p_formato", FORMATO_IMPRESION.FACTURACION.TICKET_58MM);//PUEDE CAMBIAR

                    response = rest.GenericRestClient<response_file, DBNull>("rpt_factura", token.access_token, TypeMethod.Get);
                }
                catch (Exception ex)
                {

                }
            }
            return response;
        }
    }
}