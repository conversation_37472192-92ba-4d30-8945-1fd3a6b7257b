﻿using Newtonsoft.Json;
using SIFAFEL_CORE.api_request;
using SIFAFEL_CORE.api_response;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Data_Model_API.Modulos;
using SIFAFEL_MODEL.Data_Model_API.Token;
using SIFAFEL_MODEL.DataTransferObject;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.SessionState;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Descripción breve de banco
    /// </summary>
    public class banco : SessionHandler
    {
        public override void HandleRequest(HttpContext context)
        {
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "get/bancos":
                    context.Response.Write(JsonConvert.SerializeObject(GetBancos()));
                    break;
                default:
                    context.Response.StatusCode = 404;
                    context.Response.StatusDescription = "Not Found";
                    break;
            }
        }

        /// <summary>
        /// Recuperar listado de bancos
        /// </summary>
        /// <returns></returns>
        public response GetBancos()
        {
            response response = new response();
            SessionManager sessionManager = new SessionManager();
            TokenManager tokenManager = new TokenManager();

            if (sessionManager.Valid() && tokenManager.Valid())
            {
                token_response token = tokenManager.Get();

                rest_client rest = new rest_client("api_url", token.access_type, HttpContext.Current.Request.UserAgent);
                response = rest.GenericRestClient<response, ModBancoDTO>("api_mod_banco_get_list", token.access_token, TypeMethod.Get);
            }

            return response;
        }

    }
}