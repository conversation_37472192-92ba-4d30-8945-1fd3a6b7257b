﻿
:root {
    --main-modal-boder-radius: 10px;
    /*--main-sifafel-background-color: #605ca8;*/
    --main-sifafel-background-color: rgb(8, 156, 172); /*#3c8dbc*/
    --main-sifafel-background-color-opa: rgb(8, 156, 172, 0.7);
    --main-font-size: 13px;
    --main-font-size-table-header: 13px;
    --main-font-size-table-body: 13px;
    --main-font-size-table-options: 13px;
    --main-height-options: 35px;
}

body {
    font-size: var(--main-font-size);
}

a.marginLeft {
    margin-left: 1em;
}

/*SIFAFEL*/
.panel-primary > .panel-heading,
.panel-default > .panel-heading {
    padding: 5px !important;
}

.panel-primary > .panel-heading {
    background-color: var(--main-sifafel-background-color) !important;
    border-color: var(--main-sifafel-background-color) !important;
}

.panel-default > .panel-heading {
    background-color: #ddd !important;
    border-color: #ddd !important;
}

    .panel-primary > .panel-heading h4,
    .panel-default > .panel-heading h4 {
        margin: 0px;
        font-weight: bold;
        font-size: 12px;
    }

.panel-body {
    padding: 10px;
}

    .panel-body p {
        font-weight: bold;
        text-decoration: underline;
        font-size: medium;
    }

form label {
    margin-bottom: 0px;
    margin-top: 5px;
}

.bg-light-blue,
.label-primary,
.modal-primary .modal-body {
    background-color: var(--main-sifafel-background-color-opa) !important;
}

span {
}

    span.success {
        color: #00a65a;
    }

    span.primary {
        color: #3c8dbc;
    }

.option-buttons .btn-group span:hover {
    cursor: pointer !important;
}

.label-primary span {
    color: #fff;
}

.txt-green {
    color: green;
}

.modal-body {
    padding: 10px;
}

    .modal-body hr {
        margin: 2px;
    }

table .label-primary {
}

tr.group,
tr.group:hover {
    font-weight: bold;
    background-color: #ddd !important;
}

@media screen and (min-width: 1400px) {
    .layout-boxed .wrapper {
        max-width: 1600px;
    }

    .modal-lg {
        width: 1500px;
    }
}

.layout-boxed .wrapper {
    /*border-radius: 15px;*/
}

.main-header .logo {
    font-family: "Reality Hyper";
    font-size: x-large;
}

.sidebar {
    padding-bottom: 0px;
}

.sidebar-form.info-company {
    border: none !important;
}

    .sidebar-form.info-company p {
        color: #848484 !important;
    }

/*Menu*/
/*FontAwesome new version*/
.user-panel > .info > a > .far,
.user-panel > .info > a > .fad,
.user-panel > .info > a > .fal,
.user-panel > .info > a > .fab {
    margin-right: 3px;
}

.navbar-nav > .user-menu .user-image {
    /* width: 35px;
    height: 35px;
    margin-top: -7px;*/
}

.sidebar-menu > li > a > .fas,
.sidebar-menu > li > a > .fab,
.sidebar-menu > li > a > .fad,
.sidebar-menu > li > a > .far,
.sidebar-menu > li > a > .fal,
/**/
.treeview-menu > li > a > .fas,
.treeview-menu > li > a > .fab,
.treeview-menu > li > a > .fad,
.treeview-menu > li > a > .far,
.treeview-menu > li > a > .fal {
    width: 20px !important;
}

.sidebar-mini:not(.sidebar-mini-expand-feature).sidebar-collapse .sidebar-menu > li:hover > .treeview-menu {
    top: 41px;
}

.main-header .sidebar-toggle {
    padding: 17px 15px;
}

    .main-header .sidebar-toggle:before {
        content: none !important;
    }
/*Modal*/
.modal.modal-crud .modal-content {
    /*background-image: url('../img/conf/abstract-technology.jpg');*/
    background: #f6f6f6;
}

.modal .modal-content {
    border-radius: var(--main-modal-boder-radius) !important;
}

.modal .modal-header,
.modal .modal-footer {
    background: var(--main-sifafel-background-color) !important;
    color: #fff !important;
    padding: 5px;
}

.modal .modal-header {
    border-radius: var(--main-modal-boder-radius) var(--main-modal-boder-radius) 0px 0px !important;
}

    .modal .modal-header .title {
        text-align: center;
        margin: 0px;
        font-weight: bold;
        color: white;
    }

    .modal .modal-header .close {
        text-shadow: none !important;
        color: #ffff;
        opacity: 1;
    }

.modal .modal-footer {
    border-radius: 0px 0px var(--main-modal-boder-radius) var(--main-modal-boder-radius) !important;
}


/*forms*/
.form-horizontal .control-label {
    text-align: left !important;
}

@media (min-width: 768px) {
    .form-horizontal .control-label {
        padding-top: 0px;
    }
}
/*swal*/
.swal2-popup {
    font-size: 1rem !important;
}


/*bootstrap*/

@media screen and (max-width: 767px) {
    div.table-responsive {
        border: none !important;
    }
}

/*crud*/
.form-group {
    margin-bottom: 3px;
}

    .form-group.hide-control {
        display: none !important;
    }

.table-condensed > tbody > tr > td {
    padding: 2px !important;
}

.container-image {
    height: 120px;
    width: 120px;
    background: white;
    position: relative;
}

    .container-image .hover {
        background: #000000;
        position: absolute;
        height: 100%;
        width: 100%;
        opacity: 0;
        color: white;
        text-align: center;
        vertical-align: middle;
        line-height: 100px;
    }

        .container-image .hover:hover {
            opacity: 0.5;
            cursor: pointer;
        }

    .container-image img {
        width: 100% !important;
        height: 100% !important;
        border: solid 1px #ccc;
    }

    .container-image p {
        font-weight: normal !important;
    }

img.img-product {
    max-height: 100% !important;
    max-width: 100% !important;
}

.modal img.img-product {
    height: 100px !important;
    width: auto !important;
    max-height: 100px !important;
    max-width: none !important;
}

.dl-horizontal dd {
    text-align: justify !important;
}

.input-group-sm > input.form-control {
    line-height: 20px !important;
}

.input-group .input-group-addon {
    background-color: #eee;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    opacity: 1;
}

.btn-success {
    color: white;
}

/* INVENTARIO */
.barcode {
    font-family: 'barcode font', sans-serif;
    font-size: 20px;
    margin: 0px;
}

table .barcode {
    font-size: 30px !important;
}

.inventario {
}

    .inventario .panel {
    }

        .inventario .panel .panel-heading {
            padding: 5px;
        }

        .inventario .panel h4 {
            margin: 0px;
            font-weight: bold;
        }

    .inventario .btn-group-xs > .btn, .btn-xs,
    table .btn.btn-xs {
        padding: 1px 2px 1px 2px;
        font-size: 9px;
    }

tr td {
}

    tr td .currency {
        float: left;
    }

    tr td .price {
        float: right;
    }

.lb-data .lb-details {
    width: 100% !important;
    text-align: justify !important;
}

ul.existence {
    padding: 0;
    margin-bottom: 0px;
    list-style-type: none;
}

    ul.existence li {
        padding-bottom: 5px;
    }

        ul.existence li:last-child {
            padding-bottom: 0px !important;
        }

        ul.existence li .disponible {
            /*font-weight: bold;*/
            float: right;
            font-size: 18px;
            color: var(--main-sifafel-background-color);
            font-weight: bold;
        }

        ul.existence li .sucursal {
            display: block;
            font-weight: bold;
        }

        ul.existence li .precio {
            font-style: italic;
            display: block;
            color: gray;
        }

        ul.existence li .direccion {
            font-style: italic;
            color: gray;
        }

/*products*/
table.existence-sucursal {
    margin-bottom: 2px;
}

td.existence {
    border-top: none !important;
}

.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn
/*.select2-container*/ {
    height: 25px;
    padding: 2px 10px;
}

    .input-group-sm > .custom-select, .input-group-sm > .form-control:not(textarea) {
        height: calc(1.1em + .5rem + 2px);
    }

.input-group-sm > .custom-select,
.input-group-sm > .form-control,
.input-group-sm > .input-group-append > .btn,
.input-group-sm > .input-group-append > .input-group-text,
.input-group-sm > .input-group-prepend > .btn,
.input-group-sm > .input-group-prepend > .input-group-text {
    line-height: 0.5;
}

.iq-card .iq-card-header {
    min-height: 45px;
}

.form-control.price {
    text-align: right;
}

.form-control.stock {
    text-align: center;
}

.input-group.price-stock {
    display: block;
}

    .input-group.price-stock .form-control.currency {
        width: 25%;
        background: #eee;
        font-weight: bold;
    }

    .input-group.price-stock .form-control.price {
        width: 45%;
    }

    .input-group.price-stock .form-control.stock {
        width: 30%;
    }

.input-group.group-3070 {
    width: 100% !important;
}

    .input-group.group-3070 select.form-control {
        width: 30% !important;
        background: #eee;
        font-weight: bold;
        border-right: 0px;
    }

    .input-group.group-3070 input.form-control {
        width: 70% !important;
    }

.input-xs {
    height: 22px;
    padding: 2px 5px;
    font-size: 12px;
    line-height: 1.5; /* If Placeholder of the input is moved up, rem/modify this. */
    border-radius: 3px;
}

.option-buttons {
    margin: auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
}

/*JQuery-Validate*/
.error-class {
    color: red;
    z-index: 0;
    position: relative;
    display: block;
    text-align: left;
}

.table-responsive {
    overflow-x: initial;
}

.dataTables_wrapper {
    /*font-size: 8pt;*/
}

    .dataTables_wrapper table {
        width: 100% !important;
    }

    .dataTables_wrapper .dataTables_length {
        float: left;
    }

        .dataTables_wrapper .dataTables_length select {
            height: 35px;
        }

    .dataTables_wrapper .dataTables_filter {
        float: right;
    }

        .dataTables_wrapper .dataTables_filter input {
            height: 35px;
        }

    .dataTables_wrapper button.btn-primary {
        color: white !important;
    }

.table {
    font-size: var(--main-font-size-table-body);
}

    .table > .table-head,
    table.table > thead {
        background-color: var(--main-sifafel-background-color) !important;
        border-color: var(--main-sifafel-background-color) !important;
        color: #fff;
    }

table thead th {
    font-weight: normal !important;
    font-size: var(--main-font-size-table-header);
}

.select2-container {
    width: 100% !important;
}

    .select2-container .select2-selection--single {
        /*height: 25px !important;*/
    }

        .select2-container .select2-selection--single .select2-selection__rendered {
            /*padding-left: 0px !important;*/
        }

.select2-container--default .select2-selection--single {
    border: 1px solid #d7dbda;
}

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        /*line-height: 20px !important;*/
        color: #6c757d;
    }

.btn-group-vertical > .btn, .btn-group > .btn {
    height: var(--main-height-options) !important;
    font-size: var(--main-font-size-table-options) !important;
}

/*sifafel io*/
.img-sucursal {
    height: 60px;
    margin-top: 5px;
}

p.min {
    line-height: 0.8;
}

textarea.form-control {
    line-height: 20px;
}

.selectDisable {
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
}

ul.pagination {
    display: inline-flex;
}

.sorting,
.sorting_asc,
.sorting_desc,
.sorting_asc_disabled,
.sorting_desc_disabled {
    cursor: pointer;
    position: relative;
    &:after

{
    position: absolute;
    bottom: 8px;
    right: 8px;
    display: block;
    font-family: 'Glyphicons Halflings';
    opacity: 0.5;
}

}

.sorting:after {
    opacity: 0.2;
    content: "\e150"; /* sort */
}

.sorting_asc:after {
    content: "\e155"; /* sort-by-attributes */
}

.sorting_desc:after {
    content: "\e156"; /* sort-by-attributes-alt */
}
