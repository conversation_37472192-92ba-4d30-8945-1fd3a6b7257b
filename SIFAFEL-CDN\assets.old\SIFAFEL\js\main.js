﻿var url = window.location.href;
var url_origin = url.split('?')[0];
var url_param = url.split('?')[1] || '';

if (url_origin.substr(url_origin.length - 1) != "/") {
    window.location.href = url_origin + "/" + (url_param != "" ? "?" + url_param : url_param);
}

function create_menu_item(lst_menu, data_menu) {
    if (data_menu.length > 0) {
        for (var i = 0; i < data_menu.length; i++) {
            let menu = data_menu[i];
            let menu_option = $("<li>");
            let menu_redirect = $("<a>");

            menu_redirect.append($("<i>").addClass(menu.icono));
            menu_redirect.append($("<span>").html(menu.nombre));

            if (menu.sub_menus.length > 0) {
                let menu_treeview_menu = $("<ul>").addClass("treeview-menu");
                let menu_right = $("<span>").addClass("pull-right-container");

                menu_option.addClass("treeview");

                menu_redirect.attr("href", "#");
                menu_right.append($("<i>").addClass('fa fa-angle-left pull-right'));
                menu_redirect.append(menu_right);

                create_menu_item(menu_treeview_menu, menu.sub_menus);

                menu_option.append(menu_redirect);
                menu_option.append(menu_treeview_menu);
            } else {

                menu_redirect.attr("href", (menu.es_externo) ? menu.url : _url_redirect + (menu.url || ''));
                menu_option.append(menu_redirect);
            }
            lst_menu.append(menu_option);
        }

    }
}

function create_menu(data_menu) {
    var lst_menu = $("#lst_menu").empty();
    if (data_menu) {
        for (var i = 0; i < data_menu.length; i++) {
            if (data_menu[i].visible_web) { lst_menu.append($("<li>").addClass("header").html(data_menu[i].modulo)); }
            create_menu_item(lst_menu, data_menu[i].menus);
        }
    }

    $(lst_menu).find("a").each(function () {
        let _href = $(this).attr("href");
        let _url_origin = url.split('?')[0];

        if (_href == _url_origin) {
            $(this).closest("li").addClass("active");
            $(this).closest("li").closest("ul.treeview-menu").css("display", "block");
            $(this).closest("li").closest("ul.treeview-menu").closest("li.treeview").addClass("menu-open");
            $(this).removeAttr("href");
        }
    });
}