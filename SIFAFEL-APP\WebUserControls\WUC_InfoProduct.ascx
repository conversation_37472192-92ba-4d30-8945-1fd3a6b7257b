﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="WUC_InfoProduct.ascx.cs" Inherits="SIFAFEL_APP.WebUserControls.WUC_InfoProduct" %>
<link href='https://fonts.googleapis.com/css?family=Libre Barcode 128' rel='stylesheet'>
<style type="text/css">
    .bar-code-font {
        font-family: 'Libre Barcode 128';
        font-size: 22px;
    }
</style>
<div class="modal fade" id="modalInfoProducto" data-bs-backdrop="static" data-bs-keyboard="false" style="z-index: 2000" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog  modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Detalle del producto</h4>
                <button type="button" aria-label="Close" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-start">
                <div class="row">
                    <div class="col-lg-8 col-sm-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="bar-code-view">
                                    <h4 id="brc_mdl_prod_barcode" class="bar-code-font"></h4>
                                    <h6 id="brc_mdl_prod_code"></h6>
                                </div>
                                <div class="productdetails">
                                    <ul class="product-bar">
                                        <li>
                                            <h4>Producto</h4>
                                            <h6 id="lbl_mdl_prod_nombre"></h6>
                                        </li>
                                        <li>
                                            <h4>Categoria</h4>
                                            <h6 id="lbl_mdl_prod_categoria"></h6>
                                        </li>
                                        <li>
                                            <h4>Marca</h4>
                                            <h6 id="lbl_mdl_prod_marca"></h6>
                                        </li>
                                        <li>
                                            <h4>Modelo</h4>
                                            <h6 id="lbl_mdl_prod_modelo"></h6>
                                        </li>
                                        <li>
                                            <h4>Cantidad</h4>
                                            <h6 id="lbl_mdl_prod_cantidad"></h6>
                                        </li>
                                        <li>
                                            <h4>Precio</h4>
                                            <h6 id="lbl_mdl_prod_precio"></h6>
                                        </li>
                                        <li>
                                            <h4>Descripción</h4>
                                            <h6 id="lbl_mdl_prod_descripcion"></h6>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-sm-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="slider-product-details">
                                    <div class="slider-product">
                                        <img id="img_mdl_prod_img" src="" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row"></div>
            </div>
            <div class="modal-footer d-flex justify-content-end">
                <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">

    function __InfoProduct(data = {}) {
        //console.table(data);

        const {
            img_producto = `${_url_cdn}img/products/icon.png`,
            nombre = '',
            descripcion = '',
            categoria = '',
            marca = '',
            modelo = '',
            moneda = '',
            precio_unitario = '',
            codigo = '',
            existencia = ''
        } = data;

        $("#img_mdl_prod_img").attr("src", img_producto);
        $("#lbl_mdl_prod_nombre").html(nombre);
        $("#lbl_mdl_prod_descripcion").html(descripcion);
        $("#lbl_mdl_prod_categoria").html(categoria);
        $("#lbl_mdl_prod_marca").html(marca);
        $("#lbl_mdl_prod_modelo").html(modelo);
        $("#lbl_mdl_prod_precio").html(`${moneda} ${precio_unitario}`);
        $("#brc_mdl_prod_code").html(codigo);
        $("#brc_mdl_prod_barcode").html(codigo);
        $("#lbl_mdl_prod_cantidad").html(existencia);

        $("#modalInfoProducto").modal("show");
    }

</script>
