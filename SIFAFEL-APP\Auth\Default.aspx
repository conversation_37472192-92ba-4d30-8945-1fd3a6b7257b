﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="SIFAFEL_APP.Auth.Default" %>

<%@ Register Src="~/WebUserControls/WUC_Loader.ascx" TagPrefix="uc1" TagName="WUC_Loader" %>

<!DOCTYPE html>

<html lang="es-gt">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="author" content="" />
    <meta name="robots" content="noindex, nofollow" />
    <title>Inicio de sesión</title>
    <link rel="shortcut icon" type="image/x-icon" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>img/favicon.png" />
    <link rel="stylesheet" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/bootstrap.min.css" />
    <link rel="stylesheet" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/all.min.css" />
    <link rel="stylesheet" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/style.css" />
</head>
<body class="account-page">
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/jquery-3.7.1.min.js" type="text/javascript"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/plugin.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/feather.min.js" type="text/javascript"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>vendor/sweetalert2/sweetalert2.js"></script>
    <script src="<%=ConfigurationManager.AppSettings["url_cdn"] %>js/script.js" type="text/javascript"></script>
    <script type="text/javascript">
        var grecaptcha;
    </script>
    <script src="https://www.google.com/recaptcha/api.js"></script>
    <uc1:WUC_Loader runat="server" ID="WUC_Loader" />

    <div class="main-wrapper">
        <div class="account-content">
            <div class="login-wrapper bg-img">

                <div class="login-content" style="box-shadow: 1px 1px 5px rgba(204, 204, 204, 0.3);">
                    <form action="index.html">
                        <div class="login-userset">
                            <img src="<%=ConfigurationManager.AppSettings["url_cdn"] %>/img/utils/bg-login-user.webp" width="175" />

                            <div class="form-login mb-3">
                                <h3>Inicio de sesión</h3>
                                <label class="form-label">Usuario:</label>
                                <div class="form-addons">
                                    <input id="txtUser" name="txtUser" type="text" class="form-control auth_user" placeholder="Usuario" />
                                </div>
                                <br />
                                <label class="form-label">Contraseña:</label>
                                <div class="pass-group">
                                    <input id="txtPass" name="txtPass" type="password" class="pass-input form-control auth_user" placeholder="Contraseña" />
                                </div>
                            </div>

                            <div class="form-login">
                                <button id="btnLogin" type="button" data-status="0" class="btn btn-login">Ingresar</button>
                            </div>

                            <div class="form-login authentication-check">
                                <div class="row">
                                    <div class="col-12 d-flex align-items-center justify-content-between">
                                        <div class="text-end">
                                            <a class="forgot-link" href="forgot-password.html">Olvide mi contraseña</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id='recaptcha' class="g-recaptcha"
        data-sitekey="<%=ConfigurationManager.AppSettings["recaptcha_key_public"] %>"
        data-callback="__sndRequest"
        data-size="invisible">
    </div>

    <script type="text/javascript">
        document.querySelector('.login-wrapper.bg-img').style.backgroundImage = "url('<%=ConfigurationManager.AppSettings["url_cdn"] %>/img/utils/bg-login.jpg')";
        var view_password = false;

        $(document).ready(function () {

            $(".auth_user").on('keypress', function (e) {
                if (e.which == 13) { $("#btnLogin").click(); }
            });

            $("#btnLogin").on("click", function () {
                var complete = true;
                $(".auth_user").each(function () {
                    if ($(this).val() == "") {
                        complete = false;
                        return;
                    }
                });

                if (complete) {
                    if (grecaptcha) {
                        grecaptcha.execute();
                    } else {
                        <%if (ConfigurationManager.AppSettings["recaptcha_validate"].Equals("N"))
        {%>
                        __sndRequest(null);
                        <%}
        else
        {%>
                        Swal.fire({
                            icon: 'warning',
                            text: 'Sin conexión a Internet',
                        });
                        <%}%>
                    }
                }
                else {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Datos incompletos',
                        text: 'Debe completar todos los campos requeridos.',
                    });
                }
            });
            setTimeout(function () {
                $("#txtUser").val("grodriguez");
                $("#txtPass").val("admin");
                $("#btnLogin").click();
            }, 50);
        });

        function __sndRequest(token) {
            if ($("#btnLogin").data("status") == "0") {
                __Progress("Iniciando sesión...");
                var fmAuth = new FormData();

                $("#btnLogin").data("status", "1");
                $("#btnLogin").html("Autenticando...");

                fmAuth.append("mth", mtdEnc("login"));
                fmAuth.append("user", mtdEnc($("#txtUser").val()));
                fmAuth.append("pass", mtdEnc($("#txtPass").val()));
                fmAuth.append("token_id", mtdEnc(token));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/auth.ashx",//PENDIENTE
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {
                        document.title = "Autenticando...";
                    },
                    success: function (result, arg) {
                        if (result) {
                            if (result.message) {
                                if (result.message.type == "success") {
                                    window.location.href = result.url_redirect;
                                } else {
                                    Swal.fire({
                                        icon: result.message.type,
                                        text: result.message.text,
                                    });
                                }
                            }
                        } else {
                            Swal.fire({
                                icon: 'error',
                                text: 'Ocurrió un error durante el proceso',
                            });
                        }
                    },
                    error: function (result) {
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrió un error durante el proceso',
                        });
                    }, complete: function () {
                        $("#btnLogin").data("status", "0");
                        $("#btnLogin").html('<i class="icofont-sign-in"></i>&nbsp;Iniciar sesión');
                        __ProgressOff();
                        document.title = "Inicio de sesión";
                    }
                });
            }
        }
    </script>
</body>
</html>
