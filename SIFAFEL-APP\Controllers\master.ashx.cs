﻿using Newtonsoft.Json;
using SIFAFEL_CORE.web_request;
using SIFAFEL_CORE.web_response;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using SIFAFEL_MODEL.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.SessionState;
using static SIFAFEL_CORE.Core.Utility;

namespace SIFAFEL_APP.Controllers
{
    /// <summary>
    /// Summary description for master
    /// </summary>
    public class master : SessionHandler
    {
        public InfoUsuario info_usuario = new InfoUsuario();

        public override void HandleRequest(HttpContext context)
        {
            info_usuario = context.Session["AuthInfoUser"] as InfoUsuario;
            string metodo = RequestParameters.GetValue("mth");

            switch (metodo)
            {
                case "change/sucursal":
                    context.Response.Write(JsonConvert.SerializeObject(CambiarSucursal()));
                    break;
                default:
                    break;
            }
        }

        /// <summary>
        /// Proceso para cambiar la sucursal
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public response_message CambiarSucursal()
        {
            response_message response = new response_message() { type = TIPO_MENSAJE.ERROR };
            SessionManager sessionManager = new SessionManager();

            if (sessionManager.Valid())
            {
                string idSucursal = RequestParameters.GetIdSucursal();
                response = sessionManager.SetIdSucursal(idSucursal);
            }
            else
            {
                //login
            }

            return response;
        }
    }
}