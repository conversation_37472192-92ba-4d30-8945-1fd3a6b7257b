# DataTable NuevaCompra - Documentación Completa

## HTML Structure

```html
<div class="table-responsive">
    <table id="grvProductosCompra" class="table table-striped table-bordered table-sm">
        <thead class="table-head">
            <tr>
                <th>Id</th>
                <th>Código</th>
                <th>Producto</th>
                <th>Cantidad</th>
                <th>Existencia</th>
                <th>Costo U.</th>
                <th>Costo Total</th>
                <th>Precio Venta</th>
                <th>Precio Mínimo</th>
                <th>&nbsp;</th>
            </tr>
        </thead>
    </table>
</div>
```

## CSS Styles

```css
/* Tabla DataTable sin espacios ni descuadres */
#grvProductosCompra_wrapper {
    width: 100% !important;
}

#grvProductosCompra_wrapper .dataTables_scroll {
    width: 100% !important;
    overflow: visible !important;
}

#grvProductosCompra_wrapper .dataTables_scrollHead,
#grvProductosCompra_wrapper .dataTables_scrollBody {
    width: 100% !important;
    overflow: visible !important;
}

#grvProductosCompra_wrapper .dataTables_scrollHead .dataTables_scrollHeadInner {
    width: 100% !important;
}

#grvProductosCompra_wrapper .dataTables_scrollHead .dataTables_scrollHeadInner table {
    width: 100% !important;
}

#grvProductosCompra {
    width: 100% !important;
    table-layout: fixed !important;
    margin: 0 !important;
}

#grvProductosCompra th,
#grvProductosCompra td {
    padding: 8px !important;
    vertical-align: middle !important;
    border: 1px solid #dee2e6 !important;
    white-space: nowrap !important;
}

/* Anchos de columnas en porcentajes */
#grvProductosCompra th:nth-child(1), #grvProductosCompra td:nth-child(1) { width: 8% !important; }
#grvProductosCompra th:nth-child(2), #grvProductosCompra td:nth-child(2) { width: 35% !important; }
#grvProductosCompra th:nth-child(3), #grvProductosCompra td:nth-child(3) { width: 8% !important; }
#grvProductosCompra th:nth-child(4), #grvProductosCompra td:nth-child(4) { width: 8% !important; }
#grvProductosCompra th:nth-child(5), #grvProductosCompra td:nth-child(5) { width: 10% !important; }
#grvProductosCompra th:nth-child(6), #grvProductosCompra td:nth-child(6) { width: 10% !important; }
#grvProductosCompra th:nth-child(7), #grvProductosCompra td:nth-child(7) { width: 10% !important; }
#grvProductosCompra th:nth-child(8), #grvProductosCompra td:nth-child(8) { width: 10% !important; }
#grvProductosCompra th:nth-child(9), #grvProductosCompra td:nth-child(9) { width: 6% !important; }

/* Responsive con scroll horizontal */
.table-responsive {
    overflow-x: auto !important;
    width: 100% !important;
}

@media (max-width: 768px) {
    #grvProductosCompra {
        min-width: 800px !important;
    }
}
```

## JavaScript DataTable Configuration

```javascript
$('#grvProductosCompra').DataTable({
    data: [],
    destroy: true,
    language: {
        "sProcessing": "Procesando...",
        "sLengthMenu": "Mostrar _MENU_ registros",
        "sZeroRecords": "No hay productos agregados a la compra",
        "sEmptyTable": "No hay productos agregados a la compra",
        "sInfo": "Mostrando registros del _START_ al _END_ de un total de _TOTAL_ registros",
        "sInfoEmpty": "Mostrando registros del 0 al 0 de un total de 0 registros",
        "sInfoFiltered": "(filtrado de un total de _MAX_ registros)",
        "sInfoPostFix": "",
        "sSearch": "Buscar:",
        "sUrl": "",
        "sInfoThousands": ",",
        "sLoadingRecords": "Cargando...",
        "oPaginate": {
            "sFirst": "Primero",
            "sLast": "Último",
            "sNext": "",
            "sPrevious": ""
        },
        "oAria": {
            "sSortAscending": ": Activar para ordenar la columna de manera ascendente",
            "sSortDescending": ": Activar para ordenar la columna de manera descendente"
        }
    },
    info: false,
    paging: true,
    autoWidth: false,
    searching: false,
    ordering: false,
    responsive: false,
    scrollX: true,
    columnDefs: [
        {
            targets: [0],
            visible: false
        },
        {
            targets: [1, 3],
            className: 'text-center'
        },
        {
            targets: [2],
            className: 'productimgname'
        },
        {
            targets: [3, 4, 5, 6, 7, 8],
            className: 'ammounts'
        },
        {
            targets: [9],
            className: 'text-center options-tables'
        }
    ],
    columns: [
        { data: "id_producto" },
        { data: "codigo" },
        {
            data: function (item) {
                var img_producto;
                if (!item.img_producto || item.img_producto === '' || item.img_producto === null) {
                    img_producto = '<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png';
                } else {
                    img_producto = item.img_producto;
                }
                return '<div class="view-product me-2">' +
                            '<a href="#!">' +
                                '<img src="' + img_producto + '" alt="" onerror="this.onerror=null;this.src=\'<%=ConfigurationManager.AppSettings["url_cdn"] %>img/products/icon.png\';">' +
                            '</a>' +
                       '</div>' +
                       '<a href="#!" class="view-info-product">' + item.nombre + '</a>';
            }
        },
        // Columna 3: Cantidad (input editable)
        {
            data: function (item) {
                return `<input type="number" class="form-control form-control-sm text-center cantidad-input" value="1" min="1" max="${item.stock_actual || 999}" style="width: 60px;">`;
            }
        },
        // Columna 4: Existencia
        { data: "stock_actual" },
        // Columna 5: Costo U. (input editable)
        {
            data: function (item) {
                return '<input type="number" class="form-control form-control-sm text-center costo-input" value="' + (item.costo_unitario || 0).toFixed(2) + '" min="0" step="0.01" style="width: 80px;">';
            }
        },
        // Columna 6: Costo Total (calculado automáticamente)
        {
            data: function (item) {
                return `<span class="costo-total" style="font-size: 14px;">0.00</span>`;
            }
        },
        // Columna 7: Precio Venta (input editable)
        {
            data: function (item) {
                var precioVenta = item.precio_unitario || item.costo_unitario || 0;
                return '<input type="number" class="form-control form-control-sm text-center precio-venta-input" value="' + precioVenta.toFixed(2) + '" min="0" step="0.01" style="width: 90px;">';
            }
        },
        // Columna 8: Precio mínimo (input editable)
        {
            data: function (item) {
                var precioMinimo = item.min_descuento || item.costo_unitario || 0;
                return '<input type="number" class="form-control form-control-sm text-center precio-min-input" value="' + precioMinimo.toFixed(2) + '" min="0" step="0.01" style="width: 90px;">';
            }
        },
        // Columna 9: Eliminar
        {
            data: function (item) {
                return '<button class="btn btn-danger btn-sm eliminar-producto" type="button" title="Eliminar producto"><i class="fa fa-trash"></i></button>';
            }
        }
    ],
    rowCallback: function (row, data) {
        $(row).find(".view-info-product").on("click", function () {
            console.log("Datos del producto para modal:", data);
            __InfoProduct(data);
        });

        // Calcular costo total inicial cuando se carga la fila
        var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 1;
        var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
        var costoTotal = cantidad * costoUnitario;
        $(row).find('.costo-total').text(costoTotal.toFixed(2));

        // Agregar eventos para calcular costo total automáticamente
        $(row).find('.cantidad-input, .costo-input, .precio-venta-input, .precio-min-input').on('input', function() {
            var cantidad = parseFloat($(row).find('.cantidad-input').val()) || 0;
            var costoUnitario = parseFloat($(row).find('.costo-input').val()) || 0;
            var costoTotal = cantidad * costoUnitario;
            $(row).find('.costo-total').text(costoTotal.toFixed(2));
            
            // Actualizar totales generales
            fnUpdateTotales();
            
            // Validar que precio venta y precio mínimo no sean menores que costo unitario
            var precioVenta = parseFloat($(row).find('.precio-venta-input').val()) || 0;
            var precioMin = parseFloat($(row).find('.precio-min-input').val()) || 0;
            
            if (precioVenta < costoUnitario) {
                $(row).find('.precio-venta-input').css('border-color', 'red');
            } else {
                $(row).find('.precio-venta-input').css('border-color', '');
            }
            
            if (precioMin < costoUnitario) {
                $(row).find('.precio-min-input').css('border-color', 'red');
            } else {
                $(row).find('.precio-min-input').css('border-color', '');
            }
        });

        // Evento para eliminar producto
        $(row).find('.eliminar-producto').on('click', function() {
            Swal.fire({
                title: '¿Eliminar producto?',
                text: '¿Está seguro de que desea eliminar este producto de la compra?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    $('#grvProductosCompra').DataTable().row(row).remove().draw();
                    fnUpdateTotales();
                    Swal.fire('Eliminado', 'El producto ha sido eliminado de la compra.', 'success');
                }
            });
        });

        // Inicializar iconos de Feather en la fila
        feather.replace();
    }
});

// Inicializar iconos de Feather después de crear la tabla
setTimeout(function() {
    feather.replace();
    // Actualizar totales después de cargar la tabla
    fnUpdateTotales();
}, 100);
```

## Función de Actualización de Totales

```javascript
function fnUpdateTotales() {
    let subtotal = 0;
    let descuento = 0;

    // Calcular desde la columna de costo total (suma de cantidad * costo unitario)
    $('#grvProductosCompra tbody tr').each(function() {
        var $row = $(this);
        var costoTotalTexto = $row.find('.costo-total').text().trim();
        var costoTotal = parseFloat(costoTotalTexto) || 0;
        subtotal += costoTotal;
    });

    const total = subtotal - descuento;

    // Actualizar los valores en la interfaz
    $('.lbl-info-subtotal').text(subtotal.toFixed(2));
    $('.lbl-info-descuento').text(descuento.toFixed(2));
    $('.lbl-info-total').text(total.toFixed(2));
}
```

## Características Principales

1. **Responsive**: Scroll horizontal en pantallas pequeñas
2. **Editable**: Inputs para cantidad, costos y precios
3. **Validación**: Precios no pueden ser menores que costo
4. **Cálculo automático**: Costo total = cantidad × costo unitario
5. **Eliminación**: Con confirmación SweetAlert
6. **Modal de producto**: Al hacer clic en el nombre
7. **Totales dinámicos**: Se actualizan automáticamente

## Problemas Conocidos

- Espacios en blanco entre header y contenido en responsive
- Descuadre de columnas en pantallas pequeñas
- ScrollX de DataTables causa problemas de alineación
