﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="cierre.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Caja.cierre" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4><i data-feather="archive"></i>&nbsp;Cierre de Caja</h4>

                <ul class="breadcrumb">
                    <li class="breadcrumb-item">Mó<PERSON>lo de caja</li>
                    <li class="breadcrumb-item active">Cierre</li>
                </ul>
            </div>
        </div>
        <ul class="table-top-head">
            <li>
                <a data-bs-toggle="tooltip" data-bs-placement="top" title="Collapse" id="collapse-header">
                    <i data-feather="chevron-up" class="feather-chevron-up"></i>
                </a>
            </li>
        </ul>
        <div class="page-btn">
            <a href="#" class="btn btn-sm btn-added">
                <i data-feather="clock"></i>&nbsp;Historial
            </a>
        </div>
        <div class="page-btn import">
            <a href="apertura.aspx" class="btn btn-sm btn-added color">
                <i data-feather="archive"></i>&nbsp;Apertura de caja
            </a>
        </div>
    </div>

    <div class="row cierre">
        <div class="col-3">
            <!-- Filtros iniciales -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title"><i class="fas fa-filter"></i>&nbsp;Filtros</h5>
                </div>
                <div class="card-body">
                    <div class="row">

                        <div class="col-md-12">
                            <label for="ddlCaja" class="form-label">Caja:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i data-feather="airplay"></i>
                                </span>
                                <select id="ddlCaja" name="ddlCaja" class="form-select" <%= lstCajas.Count > 1 ? "" : "disabled='disabled'" %>>
                                    <% foreach (var caja in lstCajas)
                                        { %>
                                    <option value="<%= caja.id %>"><%= caja.nombre %></option>
                                    <% } %>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="ddlMoneda" class="form-label">Moneda:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i class="fas fa-coins"></i>
                                </span>
                                <select id="ddlMoneda" name="ddlMoneda" class="form-select" <%= lstMonedas.Count > 1 ? "" : "disabled='disabled'" %>>
                                    <% foreach (var caja in lstMonedas)
                                        { %>
                                    <option value="<%= caja.id %>"><%= caja.codigo_iso %></option>
                                    <% } %>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <label for="txtFecha" class="form-label">Fecha:</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i data-feather="calendar"></i>
                                </span>
                                <input id="txtFecha" name="txtFecha" class="form-control" type="date" />
                            </div>
                        </div>
                        <div class="col-md-12 mt-3 d-grid">
                            <button id="btn_consulta_caja" type="button" class="btn btn-block btn-primary">
                                <i data-feather="filter" class="filter-icon"></i>&nbsp;Consultar
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de medios de pago -->
        <div class="col-9">
            <div id="x-div-medios" class="card">
                <div class="card-header">
                    <h5 class="card-title"><i class="fas fa-coins"></i>&nbsp;Saldo de la caja</h5>
                </div>
                <div class="card-body">
                    <!-- Tabla de medios de pago -->
                    <div class="table-responsive">
                        <table id="tbl_medio_pago">
                            <thead>
                                <tr>
                                    <th>&nbsp;</th>
                                    <th>Apertura</th>
                                    <th>Sistema</th>
                                    <th>Saldo actual</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <td class="text-end"><strong>&nbsp;</strong></td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text money"></span>
                                            <input id="lbl_saldo_inicial_total" class="form-control amount total" disabled="disabled" type="number" value="" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text money"></span>
                                            <input id="lbl_saldo_final_total" class="form-control amount total" disabled="disabled" type="number" value="" />
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text money"></span>
                                            <input id="lbl_cuadre_total" class="form-control amount total" disabled="disabled" type="number" value="" />
                                            <span class="input-group-text" id="basic-addon-total">
                                                <i></i>
                                                &nbsp;<span class="text-success">Q 00.00</span>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div id="x-msg-modulo" class="alert alert-warning" role="alert">
                        <h4 class="alert-heading"></h4>
                        <p class="alert-message"></p>
                    </div>

                    <textarea id="txtObservaciones" rows="2" cols="5" class="form-control" placeholder="Ingrese observaciones"></textarea>

                    <div class="d-flex justify-content-center mt-2">
                        <button id="btn_cerrar_caja" type="button" class="btn btn-block btn-success">
                            <i data-feather="check"></i>Cerrar Caja
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        var tbl_medio_pago;

        function getIconAndClass(diferencia) {
            let iconName, colorClass;
            if (diferencia === 0) {
                iconName = 'check-circle';
                colorClass = 'text-success';
            } else if (diferencia > 0) {
                iconName = 'alert-triangle';
                colorClass = 'text-warning';
            } else {
                iconName = 'alert-circle';
                colorClass = 'text-danger';
            }
            return { iconName, colorClass };
        }

        function formatAmount(amount) {
            let isNegative = amount < 0;
            let num = Math.abs(amount).toFixed(2);
            let [integerPart, decimalPart] = num.split('.');
            integerPart = integerPart.padStart(2, '0');

            return `${isNegative ? '-' : ''}${integerPart}.${decimalPart}`;
        }

        function sumSubTotales() {
            var tableData = tbl_medio_pago.rows().data();
            let _saldo_apertura = 0,
                _saldo_sistema = 0,
                _saldo_caja = 0,
                _saldo_diferencia = 0,
                _moneda = '';
            tableData.each(function (row) {
                _saldo_apertura += parseFloat(row.monto_apertura) || 0;
                _saldo_sistema += parseFloat(row.monto_actual) || 0;
                _saldo_caja += parseFloat(row.monto_cierre) || 0;
                _moneda = row.moneda;
            });

            _saldo_diferencia = _saldo_caja - _saldo_sistema;
            const { iconName, colorClass } = getIconAndClass(_saldo_diferencia);
            const displayNumber = formatAmount(_saldo_diferencia);

            $('#basic-addon-total').html(`<i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${_moneda} <span class="amount">${displayNumber}</span></span>`);
            feather.replace();

            $("#lbl_saldo_inicial_total").val(formatAmount(_saldo_apertura));
            $("#lbl_saldo_final_total").val(formatAmount(_saldo_sistema));
            $("#lbl_cuadre_total").val(formatAmount(_saldo_caja));
        }

        $(document).ready(function () {
            feather.replace();

            $("#x-msg-modulo").hide();
            $("#x-div-medios").hide();

            __setMaxDateToToday("#txtFecha", 60, false);
            $('#txtFecha').val(new Date().toDateInputValue());

            tbl_medio_pago = $('#tbl_medio_pago').DataTable({
                "bFilter": false,
                "ordering": false,
                "bLengthChange": false,
                "bInfo": false,
                "paging": false,
                "language": {
                    search: ' ',
                    searchPlaceholder: "Buscar",
                    paginate: {
                        next: ' <i class=" fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i>'
                    }
                },
                "columns": [
                    {
                        "data": "descripcion",
                        "render": function (data, type, row) {
                            const iconSrc = row.url_logo ? _url_cdn + row.url_logo : 'https://via.placeholder.com/50';
                            return `<img class="medio-icon" src="${iconSrc}" alt="">&nbsp;${data}`;
                        }
                    },
                    {
                        "data": "monto_apertura",
                        "render": function (data, type, row) {
                            const monto = parseFloat(row.monto_apertura) || 0;
                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" value="${monto.toFixed(2)}" disabled />
                                    </div>`;
                        }
                    },
                    {
                        "data": "saldo_final",
                        "render": function (data, type, row) {
                            const monto = parseFloat(row.monto_actual) || 0;
                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" value="${monto.toFixed(2)}" disabled />
                                    </div>`;
                        }
                    },
                    {
                        "data": null,
                        "render": function (data, type, row) {
                            row.monto_cierre = parseFloat(row.monto_cierre) || 0;
                            row.diferencia = row.monto_cierre - (parseFloat(row.monto_actual) || 0);

                            const { iconName, colorClass } = getIconAndClass(row.diferencia);
                            const displayNumber = formatAmount(row.diferencia);

                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" placeholder="${formatAmount(row.monto_actual)}" value="${formatAmount(row.monto_cierre)}" />
                                        <span class="input-group-text" id="basic-addon2">
                                            <i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${row.moneda} <span class="amount">${displayNumber}</span></span>
                                        </span>
                                    </div>`;
                        }
                    }
                ],
                createdRow: function (row, data) {
                    $(".money").html(data.moneda);
                    setTimeout(function () {
                        feather.replace();
                    }, 50);
                    $(row).find("input.amount").on("input", function () {
                        data.monto_cierre = parseFloat($(this).val()) || 0;
                        data.diferencia = data.monto_cierre - (parseFloat(data.monto_actual) || 0);

                        const { iconName, colorClass } = getIconAndClass(data.diferencia);
                        const displayNumber = formatAmount(data.diferencia);

                        $(row).find('#basic-addon2').html(`<i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${data.moneda} <span class="amount">${displayNumber}</span></span>`);
                        feather.replace();

                        sumSubTotales();
                    });
                }
            });

            $('#btn_consulta_caja').on('click', function () {
                $("#x-msg-modulo").hide();

                if (!$('#txtFecha').val()) { Swal.fire('Campo requerido', 'Seleccione una fecha.', 'warning'); return; }
                if (!$('#ddlCaja').val()) { Swal.fire('Campo requerido', 'Seleccione una caja.', 'warning'); return; }
                if (!$('#ddlMoneda').val()) { Swal.fire('Campo requerido', 'Seleccione una moneda.', 'warning'); return; }

                __Progress(`Consultando...`);

                setTimeout(function () {
                    var jsonData = JSON.stringify({
                        fecha: __Format($('#txtFecha').val()),
                        id_caja: $('#ddlCaja').val() ? parseInt($('#ddlCaja').val()) : null,
                        id_moneda: $('#ddlMoneda').val() ? parseInt($('#ddlMoneda').val()) : null,
                        tipo: "CIE"
                    });

                    var fmAuth = new FormData();
                    fmAuth.append("mth", mtdEnc("get/medio/pago"));
                    fmAuth.append("data", mtdEnc(jsonData));

                    $.ajax({
                        url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmAuth,
                        async: true,
                        beforeSend: function () {
                            tbl_medio_pago.clear();
                            tbl_medio_pago.draw();
                            $('#tbl_medio_pago').hide();

                            $("#x-div-medios").hide();
                            $("#btn_cerrar_caja").hide();
                            $("#txtObservaciones").hide();
                        },
                        success: function (response) {
                            if (response) {
                                const alertType = response.type === "error" ? "danger" : response.type;
                                $("#x-msg-modulo").removeAttr('class').addClass(`alert alert-${alertType}`).show();
                                $("#x-msg-modulo").find(".alert-heading").html(response.title);
                                $("#x-msg-modulo").find(".alert-message").html(response.text);

                                $("#x-div-medios").show();

                                if (response.data_tables.length > 0) {
                                    $('#tbl_medio_pago').show();
                                    tbl_medio_pago.rows.add(response.data_tables[0]);
                                    tbl_medio_pago.draw();

                                    setTimeout(function () {
                                        sumSubTotales();
                                    }, 50);

                                    if (response.type === "success") {
                                        $("#btn_cerrar_caja").show();
                                        $("#txtObservaciones").show();
                                    }
                                    else if (response.type === "info") {
                                        tbl_medio_pago.rows().every(function () {
                                            $(this.node()).find('input').prop('disabled', true);
                                        });
                                    } else {
                                        $("#btn_cerrar_caja").hide();

                                    }
                                    tbl_medio_pago.rows().every(function () {
                                        $(this.node()).find('input').trigger('input');
                                    });
                                }
                            }
                        },
                        complete: function () {
                            __ProgressOff();
                        }
                    });
                }, 50);
            });

            $("#btn_cerrar_caja").on("click", function () {
                if (!$('#txtFecha').val()) { Swal.fire('Campo requerido', 'Seleccione una fecha.', 'warning'); return; }
                if (!$('#ddlCaja').val()) { Swal.fire('Campo requerido', 'Seleccione una caja.', 'warning'); return; }
                if (!$('#ddlMoneda').val()) { Swal.fire('Campo requerido', 'Seleccione una moneda.', 'warning'); return; }
                if (!$('#txtObservaciones').val()) { Swal.fire('Campo requerido', 'Por favor ingrese un comentario.', 'warning'); return; }

                var tableData = tbl_medio_pago.rows().data();
                var recalculatedTotal = 0;
                var userEnteredTotal = 0;

                tableData.each(function (row) {
                    recalculatedTotal += parseFloat(row.monto_actual) || 0;
                    userEnteredTotal += parseFloat(row.monto_cierre) || 0;
                });

                if (userEnteredTotal !== recalculatedTotal) {
                    Swal.fire({
                        title: 'Advertencia',
                        text: 'El saldo ingresado no coincide con la suma de los medios de pago. ¿Desea continuar con el cierre?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Sí, continuar',
                        cancelButtonText: 'No, corregir',
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            __CerrarCaja(recalculatedTotal);
                        }
                        //else if (result.dismiss === Swal.DismissReason.cancel) {
                        //    Swal.fire('Corrija el saldo', 'Puede ajustar el saldo manualmente y volver a intentar.', 'info');
                        //}
                    });
                } else {
                    __CerrarCaja(recalculatedTotal);
                }
            });
        });

        function __CerrarCaja(saldoFinal) {
            var mediosPago = [];
            var tableData = tbl_medio_pago.rows().data();

            tableData.each(function (row) {
                mediosPago.push({
                    id_medio_pago: row.id_medio_pago,
                    monto: parseFloat(row.monto_cierre) || 0
                });
            });

            var jsonData = {
                tipo: "CIE",
                fecha: __Format($('#txtFecha').val()),
                id_caja: $('#ddlCaja').val() ? parseInt($('#ddlCaja').val()) : null,
                id_moneda: $('#ddlMoneda').val() ? parseInt($('#ddlMoneda').val()) : null,
                observaciones: $('#txtObservaciones').val(),
                medios_pago: mediosPago,
                total: saldoFinal
            };

            __Progress(`Cerrando Caja...`);

            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("realizar/operacion"));
                fmAuth.append("data", mtdEnc(JSON.stringify(jsonData)));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                    type: "post",
                    contentType: false,
                    processData: false,
                    ContentType: "text/html;charset=utf-8",
                    dataType: "json",
                    data: fmAuth,
                    async: true,
                    beforeSend: function () {

                    },
                    success: function (response) {
                        if (response && response.type === 'success') {
                            Swal.fire('Éxito', 'Caja cerrada correctamente.', 'success');

                            $("#btn_cerrar_caja").hide();
                            $("#txtObservaciones").val("").hide();
                            $('#btn_consulta_caja').click();
                        } else {
                            Swal.fire('Error', response.text || ' Ocurrió un error al cerrar la caja.', response.type);
                        }
                    },
                    complete: function () {
                        __ProgressOff();
                    }
                });
            }, 50);
        }
    </script>
</asp:Content>
