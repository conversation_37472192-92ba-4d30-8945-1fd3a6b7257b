﻿using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace SIFAFEL_APP.Auth
{
    public partial class LogOut : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            WUC_Loader.ChangeStatus(false);
            WUC_Loader.ChangeStatusMessage(true);
        }

        protected void timer_logout_Tick(object sender, EventArgs e)
        {
            InfoUsuario info_usuario = new InfoUsuario();
            if (Session["AuthInfoUser"] != null)
            {
                info_usuario = Session["AuthInfoUser"] as InfoUsuario;

                //clIngreso.updateOutLog(informacionUsuario.idUsuario.ToString());

                try
                {

                }
                catch (Exception ea) { }
            }

            HttpContext.Current.Session.Clear();
            HttpContext.Current.Session.Abandon();
            HttpContext.Current.Session.RemoveAll();
            Response.Cache.SetExpires(DateTime.UtcNow.AddMinutes(-1));
            Response.Cache.SetCacheability(HttpCacheability.NoCache);
            Response.Cache.SetNoStore();

            try
            {
                Session.Abandon();
                Session.RemoveAll();
                FormsAuthentication.SignOut();
                Response.Cache.SetCacheability(HttpCacheability.NoCache);
                Response.Buffer = true;
                Response.ExpiresAbsolute = DateTime.Now.AddDays(-1d);
                Response.Expires = -1000;
                Response.CacheControl = "no-cache";
            }
            catch (Exception ex)
            {
                HttpContext.Current.Response.Write(ex.Message);
            }

            HttpContext.Current.Response.Redirect(ConfigurationManager.AppSettings["url_redirect"] + "Auth");
        }

    }
}