using System;
using System.Collections.Generic;

namespace SIFAFEL_MODEL.DataTransferObject
{
    public class ModCompraExtendidaDTO
    {
        public int? id_contribuyente { get; set; }
        public int? id_sucursal { get; set; }
        public int? id_usuario { get; set; }
        public string moneda { get; set; }
        public decimal mto_subtotal { get; set; }
        public decimal mto_recargo { get; set; }
        public decimal mto_descuento { get; set; }
        public decimal mto_total { get; set; }
        public int id_moneda { get; set; }
        public List<ProductoCompraDTO> productos { get; set; }
        public List<MedioPagoCompraDTO> medios_pago { get; set; }
        public EncabezadoCompraDTO encabezado { get; set; }
    }

    public class ProductoCompraDTO
    {
        public int id_producto { get; set; }
        public int id_moneda { get; set; }
        public string codigo { get; set; }
        public string nombre { get; set; }
        public int cantidad { get; set; }
        public string moneda { get; set; }
        public decimal precio_venta { get; set; }
        public decimal costo_unitario { get; set; }
        public string descripcion { get; set; }
        public string categoria { get; set; }
        public bool estado { get; set; }
        public int existencia { get; set; }
        public decimal precio_unitario { get; set; }
        public decimal precio_minimo { get; set; }
        public decimal recargo { get; set; }
        public decimal descuento { get; set; }
        public string img_producto { get; set; }
    }

    public class MedioPagoCompraDTO
    {
        public string codigo { get; set; }
        public string descripcion { get; set; }
        public string moneda { get; set; }
        public int id_medio_pago { get; set; }
        public string cod_entidad { get; set; }
        public string cod_referencia { get; set; }
        public int id_banco { get; set; }
        public DateTime fecha_referencia { get; set; }
        public string num_referencia { get; set; }
        public string num_autorizacion { get; set; }
        public decimal monto { get; set; }
    }

    public class EncabezadoCompraDTO
    {
        public string id_referencia { get; set; }
        public string tipo_transaccion { get; set; }
        public string fecha { get; set; }
        public int? id_caja { get; set; }
        public string id_cliente { get; set; }
    }
}
