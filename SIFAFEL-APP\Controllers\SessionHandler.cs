﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.SessionState;

namespace SIFAFEL_APP.Controllers
{
    public abstract class SessionHandler : IHttpHand<PERSON>, IRequiresSessionState
    {
        public void ProcessRequest(HttpContext context)
        {
            if (!IsAuthorized(context))
            {
                context.Response.StatusCode = 401;
                context.Response.StatusDescription = "Unauthorized - Token or session invalid";
                context.Response.End();
                return;
            }
            else
            {
                context.Response.ContentType = "application/json";
            }

            HandleRequest(context);
        }

        /// <summary>
        /// Método que valida la autenticación
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        protected bool IsAuthorized(HttpContext context)
        {
            return context.Session["AuthInfoUser"] != null;
        }

        /// <summary>
        /// Cada handler debe implementar este método
        /// </summary>
        /// <param name="context"></param>
        public abstract void HandleRequest(HttpContext context);

        public bool IsReusable
        {
            get { return false; }
        }
    }

}