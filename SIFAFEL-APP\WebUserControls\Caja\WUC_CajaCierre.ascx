﻿<%@ Control Language="C#" AutoEventWireup="true"  Debug="true"  CodeBehind="WUC_CajaCierre.ascx.cs" Inherits="SIFAFEL_APP.WebUserControls.Caja.WUC_CajaCierre" %>

<!DOCTYPE html>
<html lang="es-gt" data-layout-mode="light_mode" data-layout-style="default" data-nav-color="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="description" content="SIFAFEL" />
    <meta name="keywords" content="Punto de venta, facturación electrónica" />
    <meta name="author" content="SIFAFEL" />
    <meta name="robots" content="noindex, nofollow" />
    <title>
    </title>
    <script type="text/javascript">
        (function () {
            const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const htmlElement = document.documentElement;

            htmlElement.setAttribute('data-layout-mode', prefersDarkMode ? 'dark_mode' : 'light_mode');
            htmlElement.setAttribute('data-nav-color', prefersDarkMode ? 'dark' : 'light');
        })();
    </script>
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/animate.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/buttons.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/fontawesome/css/all.min.css" />

    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/style.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>css/main.css" />
    <link rel="stylesheet" type="text/css" href="<%=ConfigurationManager.AppSettings["url_cdn"] %>plugins/daterangepicker/daterangepicker.css" />


    <style type="text/css">
        .swal2-popup {
            font-size: 0.85rem !important;
        }

        .amount {
            text-align: right !important;
        }

            .amount.total {
                font-weight: bold;
            }

        table {
            width: 100% !important;
        }

            table tfoot td.amount {
                text-align: right !important;
            }

                table tfoot td.amount.total {
                    font-weight: bold !important;
                }

        tr td {
        }

            tr td .currency {
                float: left;
            }

            tr td .price {
                float: right;
            }

        body {
            background-color: #fff;
        }
    </style>

</head>

<body>

    <!-- Inputs ocultos -->
    <input type="hidden" id="caj_cierre_txtFecha" />
    <input type="hidden" id="caj_cierre_ddlCaja" />
    <input type="hidden" id="caj_cierre_ddlMoneda" />

    <%--<div class="modal fade" id="caj_cierre_unauthorizedModal" tabindex="-1" aria-labelledby="caj_cierre_unauthorizedModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="caj_cierre_unauthorizedModalLabel">Sesión Expirada</h5>

                </div>
                <div class="modal-body">
                    Tu sesión ha expirado o no tienes autorización para realizar esta acción. Por favor, inicia sesión nuevamente.
                <input type="text" class="form-control" disabled="disabled" placeholder="Usuario" />
                    <input type="password" class="form-control" disabled="disabled" placeholder="Clave" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success">Continuar</button>
                    <button type="button" class="btn btn-danger">Salir</button>
                </div>
            </div>
        </div>
    </div>
    --%>


        <script type="text/javascript">
            let pendingRequestCierre = null; // Almacena la información de la solicitud pendiente

            $(document).ajaxError(function (event, jqxhr, settings, thrownError) {
                if (jqxhr.status === 401) {
                    pendingRequestCierre = {
                        url: settings.url,
                        type: settings.type,
                        data: settings.data || null, // Datos enviados en la solicitud
                        contentType: settings.contentType || 'application/json', // Tipo de contenido
                        headers: settings.headers || {}, // Encabezados personalizados
                    };
                    $('caj_cierre_unauthorizedModal').modal('show');
                } else if (thrownError === 'ERR_CONNECTION_REFUSED') {
                    console.error('Error: Connection refused. Unable to reach the server.');
                } else {
                    console.error(`Unhandled AJAX error: ${thrownError}`);
                }
            });

            window.addEventListener('error', function (event) {
                console.log(event.target.tagName);
                if (event.target.tagName === 'IMG') {
                    //event.target.src = `${_url_cdn}img/products/icon.png`;
                    //event.preventDefault();
                    //PENDIENTE MEJORAR
                }
            }, true);

            function __GetDateToday() {
                let fechaSeleccionada = new Date();
                let dia = fechaSeleccionada.getDate().toString().padStart(2, '0');
                let mes = (fechaSeleccionada.getMonth() + 1).toString().padStart(2, '0');
                let anio = fechaSeleccionada.getFullYear();

                return `${dia}/${mes}/${anio}`;
            }

            function __GetInputValue(value) {
                return value === '' || value === undefined ? null : value;
            }

            Date.prototype.toDateInputValue = (function () {
                var local = new Date(this);
                local.setMinutes(this.getMinutes() - this.getTimezoneOffset());
                return local.toJSON().slice(0, 10);
            });

            function __ProgressCierre(message) {
                $("#caj_cierre_x-loader-message").html(message);
                $("#caj_cierre_global-loader-msg").fadeIn().css("display", "flex");
            }

            function __ProgressOffCierre() {
                $("#caj_cierre_global-loader-msg").fadeOut(400, function () {
                    $("#caj_cierre_x-loader-message").html("");
                });
            }

            function __FormatCierre(p_fecha) {
                if (!p_fecha) return null;

                if (/^\d{4}-\d{2}-\d{2}$/.test(p_fecha)) {
                    const partes_fecha = p_fecha.split("-");
                    return `${partes_fecha[2]}/${partes_fecha[1]}/${partes_fecha[0]}`;
                }

                if (/^\d{2}\/\d{2}\/\d{4}$/.test(p_fecha)) {
                    return p_fecha;
                }

                return p_fecha;
            }

            
        

        </script>










    <div class="aspNetHidden">
        <input type="hidden" name="__VIEWSTATE" id="caj_cierre___VIEWSTATE" value="OcmWZUOHvOw3fe0wiwj4Hk0nYIwMck0VcJ8RlqK0vwm5K+qyVyxU3e6eCElyhHufLwVYjFySMPGwvnixyCga46UrVn5LqVOysXJG7nXaxdM=" />
    </div>

    <div class="aspNetHidden">

        <input type="hidden" name="__VIEWSTATEGENERATOR" id="caj_cierre___VIEWSTATEGENERATOR" value="2FC090C2" />
    </div>



    <div class="modal fade" id="caj_cierre_modal_cierre" tabindex="-1" aria-labelledby="caj_cierre_modal_cierre_label"
        aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">

        <div class="modal-dialog modal-lg">
            <!-- Se agregó modal-dialog -->
            <div class="modal-content">
                <!-- Se agregó modal-content -->

                <div class="modal-header">
                    <h5 class="modal-title" id="caj_cierre_modal_cierre_label">
                        <i class="fas fa-coins"></i>&nbsp;Saldo de la caja
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <div class="modal-body">
                    <div class="row cierre">

                        <!-- Sección de medios de pago -->
                        <div class="col-12">
                            <div id="caj_cierre_x-div-medios" class="card">
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="caj_cierre_tbl_medio_pago" class="table">
                                            <thead>
                                                <tr>
                                                    <th>&nbsp;</th>
                                                    <th>Apertura</th>
                                                    <th>Sistema</th>
                                                    <th>Saldo actual</th>
                                                </tr>
                                            </thead>
                                            <tfoot>
                                                <tr>
                                                    <td class="text-end"><strong>&nbsp;</strong></td>
                                                    <td>
                                                        <div class="input-group input-group-sm">
                                                            <span class="input-group-text money"></span>
                                                            <input id="caj_cierre_lbl_saldo_inicial_total"
                                                                class="form-control amount total" disabled="disabled" type="number" value="" />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="input-group input-group-sm">
                                                            <span class="input-group-text money"></span>
                                                            <input id="caj_cierre_lbl_saldo_final_total"
                                                                class="form-control amount total" disabled="disabled" type="number" value="" />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="input-group input-group-sm">
                                                            <span class="input-group-text money"></span>
                                                            <input id="caj_cierre_lbl_cuadre_total"
                                                                class="form-control amount total" disabled="disabled" type="number" value="" />
                                                            <span class="input-group-text" id="caj_cierre_basic-addon-total">
                                                                <i></i>
                                                                &nbsp;<span class="text-success">Q 00.00</span>
                                                            </span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>

                                    <div id="caj_cierre_x-msg-modulo" class="alert alert-warning" role="alert">
                                        <h4 class="alert-heading"></h4>
                                        <p class="alert-message"></p>
                                    </div>

                                    <textarea id="caj_cierre_txtObservaciones" rows="2" cols="5"
                                        class="form-control" placeholder="Ingrese observaciones"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer d-flex justify-content-end">
                    <button id="caj_cierre_btn_cerrar_caja" type="button" class="btn btn-success">
                        <i data-feather="check"></i>Cerrar Caja
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>

            </div>
        </div>
    </div>



    <script type="text/javascript">
        var tbl_medio_pago;

        function ejecutarModal() {
            if ($.fn.DataTable.isDataTable('#caj_cierre_tbl_medio_pago')) {
                $('#caj_cierre_tbl_medio_pago').DataTable().destroy(); // Destruye la tabla existente
            }
            feather.replace();
            //abrirTablaCajaCierre();

            $("#caj_cierre_x-msg-modulo").hide();
            $("#caj_cierre_x-div-medios").hide();

            tbl_medio_pago = $('#caj_cierre_tbl_medio_pago').DataTable({
                "bFilter": false,
                "ordering": false,
                "bLengthChange": false,
                "bInfo": false,
                "paging": false,
                "language": {
                    search: ' ',
                    searchPlaceholder: "Buscar",
                    paginate: {
                        next: ' <i class=" fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i>'
                    }
                },
                "columns": [
                    {
                        "data": "descripcion",
                        "render": function (data, type, row) {
                            const iconSrc = row.url_logo ? _url_cdn + row.url_logo : 'https://via.placeholder.com/50';
                            return `<img class="medio-icon" src="${iconSrc}" alt="">&nbsp;${data}`;
                        }
                    },
                    {
                        "data": "monto_apertura",
                        "render": function (data, type, row) {
                            const monto = parseFloat(row.monto_apertura) || 0;
                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" value="${monto.toFixed(2)}" disabled />
                                    </div>`;
                        }
                    },
                    {
                        "data": "saldo_final",
                        "render": function (data, type, row) {
                            const monto = parseFloat(row.monto_actual) || 0;
                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" value="${monto.toFixed(2)}" disabled />
                                    </div>`;
                        }
                    },
                    {
                        "data": null,
                        "render": function (data, type, row) {
                            row.monto_cierre = parseFloat(row.monto_cierre) || 0;
                            row.diferencia = row.monto_cierre - (parseFloat(row.monto_actual) || 0);

                            const { iconName, colorClass } = getIconAndClassCierre(row.diferencia);
                            const displayNumber = formatAmountCierre(row.diferencia);

                            return `<div class="input-group input-group-sm">
                                        <span class="input-group-text">${row.moneda}</span>
                                        <input class="form-control amount" type="number" placeholder="${formatAmountCierre(row.monto_actual)}" value="${formatAmountCierre(row.monto_cierre)}" />
                                        <span class="input-group-text" id="caj_cierre_basic-addon2">
                                            <i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${row.moneda} <span class="amount">${displayNumber}</span></span>
                                        </span>
                                    </div>`;
                        }
                    }
                ],
                createdRow: function (row, data) {
                    
                    $(".money").html(data.moneda);
                    setTimeout(function () {
                        feather.replace();
                    }, 50);
                    $(row).find("input.amount").on("input", function () {
                        data.monto_cierre = parseFloat($(this).val()) || 0;
                        data.diferencia = data.monto_cierre - (parseFloat(data.monto_actual) || 0);

                        const { iconName, colorClass } = getIconAndClassCierre(data.diferencia);
                        const displayNumber = formatAmountCierre(data.diferencia);

                        $(row).find('#caj_cierre_basic-addon2').html(`<i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${data.moneda} <span class="amount">${displayNumber}</span></span>`);
                        feather.replace();

                        sumSubTotalesCierre();
                    });
                }
            });

            $("#caj_cierre_btn_cerrar_caja").on("click", function () {
              if (!$('#caj_cierre_txtObservaciones').val()) { Swal.fire('Campo requerido', 'Por favor ingrese un comentario.', 'warning'); return; }

                var tableData = tbl_medio_pago.rows().data();
                var recalculatedTotal = 0;
                var userEnteredTotal = 0;

                tableData.each(function (row) {
                    recalculatedTotal += parseFloat(row.monto_actual) || 0;
                    userEnteredTotal += parseFloat(row.monto_cierre) || 0;
                });

                if (userEnteredTotal !== recalculatedTotal) {
                    Swal.fire({
                        title: 'Advertencia',
                        text: 'El saldo ingresado no coincide con la suma de los medios de pago. ¿Desea continuar con el cierre?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Sí, continuar',
                        cancelButtonText: 'No, corregir',
                        reverseButtons: true
                    }).then((result) => {
                        if (result.isConfirmed) {
                            __CerrarCaja(recalculatedTotal);
                            $("#caj_cierre_modal_medios").modal("hide");

                        }
                        //else if (result.dismiss === Swal.DismissReason.cancel) {
                        //    Swal.fire('Corrija el saldo', 'Puede ajustar el saldo manualmente y volver a intentar.', 'info');
                        //}
                    });
                } else {
                    __CerrarCaja(recalculatedTotal);
                }
            });

        };

        function getIconAndClassCierre(diferencia) {
            let iconName, colorClass;
            if (diferencia === 0) {
                iconName = 'check-circle';
                colorClass = 'text-success';
            } else if (diferencia > 0) {
                iconName = 'alert-triangle';
                colorClass = 'text-warning';
            } else {
                iconName = 'alert-circle';
                colorClass = 'text-danger';
            }
            return { iconName, colorClass };
        }

        function formatAmountCierre(amount) {
            let isNegative = amount < 0;
            let num = Math.abs(amount).toFixed(2);
            let [integerPart, decimalPart] = num.split('.');
            integerPart = integerPart.padStart(2, '0');

            return `${isNegative ? '-' : ''}${integerPart}.${decimalPart}`;
        }

        function sumSubTotalesCierre() {
            var tableData = tbl_medio_pago.rows().data();
            let _saldo_apertura = 0,
                _saldo_sistema = 0,
                _saldo_caja = 0,
                _saldo_diferencia = 0,
                _moneda = '';
            tableData.each(function (row) {
                _saldo_apertura += parseFloat(row.monto_apertura) || 0;
                _saldo_sistema += parseFloat(row.monto_actual) || 0;
                _saldo_caja += parseFloat(row.monto_cierre) || 0;
                _moneda = row.moneda;
            });

            _saldo_diferencia = _saldo_caja - _saldo_sistema;
            const { iconName, colorClass } = getIconAndClassCierre(_saldo_diferencia);
            const displayNumber = formatAmountCierre(_saldo_diferencia);

            $('#caj_cierre_basic-addon-total').html(`<i data-feather="${iconName}" class="${colorClass}"></i>&nbsp;<span class="${colorClass}">${_moneda} <span class="amount">${displayNumber}</span></span>`);
            feather.replace();

            $("#caj_cierre_lbl_saldo_inicial_total").val(formatAmountCierre(_saldo_apertura));
            $("#caj_cierre_lbl_saldo_final_total").val(formatAmountCierre(_saldo_sistema));
            $("#caj_cierre_lbl_cuadre_total").val(formatAmountCierre(_saldo_caja));
        }

        function __CerrarCaja(saldoFinal) {
            var mediosPago = [];
            var tableData = tbl_medio_pago.rows().data();

            tableData.each(function (row) {
                mediosPago.push({
                    id_medio_pago: row.id_medio_pago,
                    monto: parseFloat(row.monto_cierre) || 0
                });
            });

            let fecha=$("#caj_cierre_txtFecha").val();
            let id_caja =$("#caj_cierre_ddlCaja").val();
            let id_moneda =$("#caj_cierre_ddlMoneda").val();
            var jsonData = {
                tipo: "CIE",
                fecha: __FormatCierre(fecha),
                id_caja: parseInt(id_caja) ? parseInt(id_caja) : null,
                id_moneda: parseInt(id_moneda) ? parseInt(id_moneda) : null,
                observaciones: $('#caj_cierre_txtObservaciones').val(),
                medios_pago: mediosPago,
                total: saldoFinal
            };


            __ProgressCierre(`Cerrando Caja...`);

            setTimeout(function () {
                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("realizar/operacion"));
                fmAuth.append("data", mtdEnc(JSON.stringify(jsonData)));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmAuth,
                        async: true,
                        beforeSend: function () {

                        },
                        success: function (response) {
                            if (response && response.type === 'success') {
                                Swal.fire('Éxito', 'Caja cerrada correctamente.', 'success');

                                $("#caj_cierre_btn_cerrar_caja").hide();
                                $("#caj_cierre_txtObservaciones").val("").hide();
                                $('#caj_cierre_btn_consulta_caja').click();
                                $("#caj_cierre_modal_medios").modal("hide");

                            } else {
                                Swal.fire('Error', response.text || ' Ocurrió un error al cerrar la caja.', response.type);
                            }
                        },
                        complete: function () {
                            __ProgressOffCierre();
                        }
                    });
                }, 50);
        }

        function __ModalCajaCierre(data = {}) {
            
            const {
                fecha = '',
                id_caja = '',
                id_monedaa = '',
            } = data;

            $("#caj_cierre_txtFecha").val(data.fecha);
            $("#caj_cierre_ddlCaja").val(data.id_caja);
            $("#caj_cierre_ddlMoneda").val(data.id_moneda);


            abrirTablaCajaCierre(data.fecha, data.id_caja, data.id_moneda);
            $("#caj_cierre_modal_cierre").modal("show");


            ejecutarModal();

        }

        function abrirTablaCajaCierre(fecha, id_caja, id_moneda) {
            $("#caj_cierre_x-msg-modulo").hide();

            if (!fecha) { Swal.fire('Campo requerido', 'Seleccione una fecha.', 'warning'); return; }
            if (!id_caja) { Swal.fire('Campo requerido', 'Seleccione una caja.', 'warning'); return; }
            if (!id_moneda) { Swal.fire('Campo requerido', 'Seleccione una moneda.', 'warning'); return; }

            __ProgressCierre(`Consultando...`);

            setTimeout(function () {
                var jsonData = JSON.stringify({
                    fecha: __Format(fecha),
                    id_caja: parseInt(id_caja),
                    id_moneda: parseInt(id_moneda),
                    tipo: "CIE"
                });

                var fmAuth = new FormData();
                fmAuth.append("mth", mtdEnc("get/medio/pago"));
                fmAuth.append("data", mtdEnc(jsonData));

                $.ajax({
                    url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmAuth,
                        async: true,
                        beforeSend: function () {
                            tbl_medio_pago.clear();
                            tbl_medio_pago.draw();
                            $('#caj_cierre_tbl_medio_pago').hide();

                            $("#caj_cierre_x-div-medios").hide();
                            $("#caj_cierre_btn_cerrar_caja").hide();
                            $("#caj_cierre_txtObservaciones").hide();
                        },
                        success: function (response) {
                            if (response) {
                                const alertType = response.type === "error" ? "danger" : response.type;
                                $("#caj_cierre_x-msg-modulo").removeAttr('class').addClass(`alert alert-${alertType}`).show();
                                $("#caj_cierre_x-msg-modulo").find(".alert-heading").html(response.title);
                                $("#caj_cierre_x-msg-modulo").find(".alert-message").html(response.text);

                                $("#caj_cierre_x-div-medios").show();

                                if (response.data_tables.length > 0) {
                                    $('#caj_cierre_tbl_medio_pago').show();
                                    tbl_medio_pago.rows.add(response.data_tables[0]);
                                    tbl_medio_pago.draw();

                                    setTimeout(function () {
                                        sumSubTotalesCierre();
                                    }, 50);
                                    
                                    if (response.type === "success") {
                                        $("#caj_cierre_btn_cerrar_caja").show();
                                        $("#caj_cierre_txtObservaciones").show();
                                    }
                                    else if (response.type === "info") {
                                        tbl_medio_pago.rows().every(function () {
                                            $(this.node()).find('input').prop('disabled', true);
                                        });
                                    } else {
                                        $("#caj_cierre_btn_cerrar_caja").hide();
                                    }
                                    tbl_medio_pago.rows().every(function () {
                                        $(this.node()).find('input').trigger('input');
                                    });
                                }
                            }
                        },
                        complete: function () {
                            __ProgressOffCierre();
                        }
                    });
                }, 50);
        };

        function __ProgressCierre(message) {
            $("#caj_cierre_x-loader-message").html(message);
            $("#caj_cierre_global-loader-msg").fadeIn().css("display", "flex");
        }

        function __ProgressOffCierre() {
            $("#caj_cierre_global-loader-msg").fadeOut(400, function () {
                $("#caj_cierre_x-loader-message").html("");
            });
        }

    </script>
</body>
</html>
