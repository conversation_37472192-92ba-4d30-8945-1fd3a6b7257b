﻿<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BF9F0F2A-B279-4AB8-AD43-1C6D4DC2F9A2}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SIFAFEL_APP</RootNamespace>
    <AssemblyName>SIFAFEL-APP</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=4.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\lib\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Auth\Default.aspx" />
    <Content Include="Auth\LogOut.aspx" />
    <Content Include="Default.aspx" />
    <Content Include="Cuenta\Default.aspx" />
    <Content Include="Modulos\Caja\apertura.aspx" />
    <Content Include="Modulos\Caja\cierre.aspx" />
    <Content Include="Modulos\Caja\Default.aspx" />
    <Content Include="Modulos\Configuracion\Default.aspx" />
    <Content Include="Modulos\Inventario\Compras.aspx" />
    <Content Include="Modulos\Inventario\Default.aspx" />
    <Content Include="Modulos\Inventario\NuevaCompra.aspx" />
    <Content Include="Modulos\Inventario\NuevoProducto.aspx" />
    <Content Include="Modulos\Ventas\Default.aspx" />
    <Content Include="Modulos\Ventas\nueva.aspx" />
    <Content Include="Web.config" />
    <Content Include="WebUserControls\Caja\WUC_CajaApertura.ascx" />
    <Content Include="WebUserControls\Caja\WUC_CajaCierre.ascx" />
    <Content Include="WebUserControls\WUC_InfoProduct.ascx" />
    <Content Include="WebUserControls\WUC_Loader.ascx" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Auth\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Auth\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Auth\LogOut.aspx.cs">
      <DependentUpon>LogOut.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Auth\LogOut.aspx.designer.cs">
      <DependentUpon>LogOut.aspx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\auth.ashx.cs">
      <DependentUpon>auth.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\banco.ashx.cs">
      <DependentUpon>banco.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\caja.ashx.cs">
      <DependentUpon>caja.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\compra.ashx.cs">
      <DependentUpon>compra.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\Inventario.ashx.cs">
      <DependentUpon>Inventario.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\master.ashx.cs">
      <DependentUpon>master.ashx</DependentUpon>
    </Compile>
    <Compile Include="Controllers\SessionHandler.cs" />
    <Compile Include="Controllers\venta.ashx.cs">
      <DependentUpon>venta.ashx</DependentUpon>
    </Compile>
    <Compile Include="Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Master\MasterPrincipal.Master.cs">
      <DependentUpon>MasterPrincipal.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Master\MasterPrincipal.Master.designer.cs">
      <DependentUpon>MasterPrincipal.Master</DependentUpon>
    </Compile>
    <Compile Include="Cuenta\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Cuenta\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Master\MasterPrincipal_NoAuth.Master.cs">
      <DependentUpon>MasterPrincipal_NoAuth.Master</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Master\MasterPrincipal_NoAuth.Master.designer.cs">
      <DependentUpon>MasterPrincipal_NoAuth.Master</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Caja\apertura.aspx.cs">
      <DependentUpon>apertura.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Caja\apertura.aspx.designer.cs">
      <DependentUpon>apertura.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Caja\cierre.aspx.cs">
      <DependentUpon>cierre.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Caja\cierre.aspx.designer.cs">
      <DependentUpon>cierre.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Caja\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Caja\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Configuracion\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Configuracion\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Inventario\Compras.aspx.cs">
      <DependentUpon>Compras.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Inventario\Compras.aspx.designer.cs">
      <DependentUpon>Compras.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Inventario\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Inventario\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Inventario\NuevaCompra.aspx.cs">
      <DependentUpon>NuevaCompra.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Inventario\NuevaCompra.aspx.designer.cs">
      <DependentUpon>NuevaCompra.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Inventario\NuevoProducto.aspx.cs">
      <DependentUpon>NuevoProducto.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Inventario\NuevoProducto.aspx.designer.cs">
      <DependentUpon>NuevoProducto.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Ventas\Default.aspx.cs">
      <DependentUpon>Default.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Ventas\Default.aspx.designer.cs">
      <DependentUpon>Default.aspx</DependentUpon>
    </Compile>
    <Compile Include="Modulos\Ventas\nueva.aspx.cs">
      <DependentUpon>nueva.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Modulos\Ventas\nueva.aspx.designer.cs">
      <DependentUpon>nueva.aspx</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WebUserControls\Caja\WUC_CajaApertura.ascx.cs">
      <DependentUpon>WUC_CajaApertura.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebUserControls\Caja\WUC_CajaApertura.ascx.designer.cs">
      <DependentUpon>WUC_CajaApertura.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebUserControls\Caja\WUC_CajaCierre.ascx.cs">
      <DependentUpon>WUC_CajaCierre.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebUserControls\Caja\WUC_CajaCierre.ascx.designer.cs">
      <DependentUpon>WUC_CajaCierre.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebUserControls\WUC_InfoProduct.ascx.cs">
      <DependentUpon>WUC_InfoProduct.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebUserControls\WUC_InfoProduct.ascx.designer.cs">
      <DependentUpon>WUC_InfoProduct.ascx</DependentUpon>
    </Compile>
    <Compile Include="WebUserControls\WUC_Loader.ascx.cs">
      <DependentUpon>WUC_Loader.ascx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebUserControls\WUC_Loader.ascx.designer.cs">
      <DependentUpon>WUC_Loader.ascx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Master\MasterPrincipal.Master" />
    <Content Include="Controllers\auth.ashx" />
    <Content Include="Controllers\master.ashx" />
    <Content Include="Controllers\caja.ashx" />
    <Content Include="Controllers\venta.ashx" />
    <Content Include="Controllers\banco.ashx" />
    <Content Include="Controllers\Inventario.ashx" />
    <Content Include="Master\MasterPrincipal_NoAuth.Master" />
    <Content Include="Controllers\compra.ashx" />
    <None Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Modulos\Cuentas\" />
    <Folder Include="Modulos\Gastos\" />
    <Folder Include="Modulos\Reportes\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SIFAFEL-CORE\SIFAFEL-CORE.csproj">
      <Project>{b8432d3d-55c5-4b82-a238-efebcb5a4ba6}</Project>
      <Name>SIFAFEL-CORE</Name>
    </ProjectReference>
    <ProjectReference Include="..\SIFAFEL-MODEL\SIFAFEL-MODEL.csproj">
      <Project>{17873221-1a30-4b32-a3be-4a3881cd9a2b}</Project>
      <Name>SIFAFEL-MODEL</Name>
    </ProjectReference>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>54326</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:54326/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Este proyecto hace referencia a los paquetes NuGet que faltan en este equipo. Use la restauración de paquetes NuGet para descargarlos. Para obtener más información, consulte http://go.microsoft.com/fwlink/?LinkID=322105. El archivo que falta es {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.4.1.0\build\net472\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>