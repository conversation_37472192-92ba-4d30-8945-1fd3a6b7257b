<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="Compras.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Inventario.Compras" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">
    <style type="text/css">
        .dataTables_length {
            display: none;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">
    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4>Listado de compras</h4>
                <h6>Administra las compras</h6>
            </div>
        </div>
        <div class="page-btn">
            <a href="NuevaCompra.aspx" class="btn btn-added color">
                <i data-feather="plus-circle" class="me-2"></i>Nueva compra
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i data-feather="calendar" class="feather-16"></i>
                        </span>
                        <input type="text" id="txtRangoFecha" class="form-control" placeholder="Seleccione rango de fechas" />
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text">
                            <i data-feather="airplay"></i>
                        </span>
                        <select id="ddlEstado" class="form-control">
                            <option value="">Todos</option>
                            <option value="ACT">Activo</option>
                            <option value="ANU">Anulado</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button id="btnBuscarCompras" type="button" href="#" class="btn btn-success btn-sm p-1">
                        <i data-feather="search" class="me-2"></i>
                    </button>
                </div>
            </div>

            <div class="table-responsive">
                <table id="grvCompras" class="table">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>Fecha</th>
                            <th>No. Poliza</th>
                            <th>Estado</th>
                            <th>Proveedor</th>
                            <th>Total</th>
                            <th>Usuario</th>
                            <th>Opciones</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            // Inicializar el control de rango de fechas
            var start = moment().subtract(30, 'days');
            var end = moment();

            $('#txtRangoFecha').daterangepicker({
                startDate: start,
                endDate: end,
                maxDate: moment(),
                locale: {
                    format: 'DD/MM/YYYY',
                    applyLabel: "Aplicar",
                    cancelLabel: "Cancelar",
                    fromLabel: "Desde",
                    toLabel: "Hasta",
                    customRangeLabel: "Personalizado"
                }
            });

            // Inicializar la tabla de compras
            $('#grvCompras').DataTable({
                "language": {
                    "emptyTable": "No hay datos disponibles en la tabla",
                    "zeroRecords": "No se encontraron registros coincidentes",
                    "loadingRecords": "Cargando...",
                    "processing": "Procesando...",
                    "search": ' ',
                    "lengthMenu": "",
                    "searchPlaceholder": "Buscar",
                    "info": " ",
                    "paginate": {
                        next: ' <i class="fa fa-angle-right"></i>',
                        previous: '<i class="fa fa-angle-left"></i> '
                    },
                },
                layout: {
                    topStart: {
                        buttons: ['copy', 'excel', 'pdf', 'colvis']
                    }
                },
                "columnDefs": [
                    {
                        "targets": [0, 3],
                        "className": "text-center ref-number"
                    },
                    {
                        "targets": [3],
                        "className": "text-center"
                    },
                    {
                        "targets": [7],
                        "className": "action-table-data"
                    }
                ],
                columns: [
                    { "data": "id_compra" },
                    {
                        "data": "fecha_compra",
                        "render": function (data, type, row) {
                            return moment(data).format('DD/MM/YYYY');
                        }
                    },
                    { "data": "num_poliza" },
                    {
                        "data": "estado",
                        "render": function (data, type, row) {
                            let badge = '';
                            switch (data) {
                                case 'ACT':
                                    badge = '<span class="badges bg-lightgreen">Activo</span>';
                                    break;
                                case 'ANU':
                                    badge = '<span class="badges bg-lightred">Anulado</span>';
                                    break;
                                default:
                                    badge = '<span class="badges bg-lightgrey">Desconocido</span>';
                                    break;
                            }
                            return badge;
                        }
                    },
                    { "data": "tbl_mod_proveedor.nombre" },
                    {
                        "data": "imp_total",
                        "render": function (data, type, row) {
                            return `<span class="currency">Q</span> ${__formatMoney(data, 2, '.', ',')}`;
                        }
                    },
                    { "data": "tbl_sec_usuario.nombre" },
                    {
                        "data": null,
                        "orderable": false,
                        "className": "text-center",
                        "render": function (data, type, row) {
                            return `<div class="edit-delete-action data-row">
                                        <a class="me-2 p-2 mb-0 print-file" href="#!">
                                            <i data-feather="printer" class="action-eye"></i>
                                        </a>
                                        <a class="me-2 p-2 mb-0" href="#!">
                                            <i data-feather="eye" class="info-img"></i>
                                        </a>
                                        <a class="me-2 confirm-text p-2 mb-0 ${row.estado === 'ANU' ? 'btn disabled' : ''}" href="#!">
                                            <i data-feather="trash-2"></i>
                                        </a>
                                    </div>
                                `;
                        }
                    }
                ],
                drawCallback: function () {
                    feather.replace();
                },
                createdRow: function (row, data, indexRow) {
                    $(row).find(".print-file").on("click", function () {
                        console.log(`print`);

                        $.ajax({
                            url: `${_url_redirect}Controllers/compra.ashx?mth=${mtdEnc("get/rpt")}&id=${data.secret_key}`,
                            type: 'GET',
                            beforeSend: function () {
                                __Progress("Generando documento...");
                            },
                            success: function (response) {
                                console.log(response);
                                if (response) {
                                    base64_file(
                                        base64str = response.base64_content,
                                        fileName = response.name,
                                        contentType = response.content_type);
                                }
                            },
                            complete: function () {
                                __ProgressOff();
                            }
                        });
                    });
                }
            });

            // Configurar el botón de búsqueda
            $("#btnBuscarCompras").on("click", function () {
                var rangoFecha = $("#txtRangoFecha").val();
                var estado = $("#ddlEstado").val();

                if (!rangoFecha) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Advertencia',
                        text: 'Por favor, ingrese un rango de fechas.'
                    });
                    return;
                }

                var fechas = rangoFecha.split(' - ');
                var fechaDesde = fechas[0];
                var fechaHasta = fechas[1];

                var datos = {
                    fecha_desde: __Format(fechaDesde),
                    fecha_hasta: __Format(fechaHasta),
                    estado: estado
                };

                var table = $('#grvCompras').DataTable();
                __Progress("Consultando...");

                var frmData = new FormData();
                frmData.append("mth", mtdEnc("get/purchases"));
                frmData.append("data", mtdEnc(JSON.stringify(datos)));

                $.ajax({
                    type: 'POST',
                    url: `${_url_redirect}Controllers/compra.ashx`,
                    data: frmData,
                    contentType: false,
                    processData: false,
                    beforeSend: function () {
                        table.clear();
                    },
                    success: function (response) {
                        if (response.data) {
                            table.clear().rows.add(response.data).draw();
                        }
                    },
                    error: function (xhr, status, error) {
                        console.log('Error:', error);
                    },
                    complete: function () {
                        __ProgressOff();
                    }
                });
            });

            // Cargar datos iniciales
            setTimeout(function () {
                $("#btnBuscarCompras").click();
            });
        });
    </script>
</asp:Content>
