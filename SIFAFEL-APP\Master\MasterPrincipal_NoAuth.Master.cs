﻿using SIFAFEL_CORE.web_request;
using SIFAFEL_MODEL.Data_Model_API.Autenticacion;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace SIFAFEL_APP.Master
{
    public partial class MasterPrincipal_NoAuth : System.Web.UI.MasterPage
    {
        /// <summary>
        /// Información del usuario autenticado.
        /// </summary>
        public InfoUsuario infoUsuario = new InfoUsuario();

        /// <summary>
        /// Nombre del contribuyente & sucursal
        /// </summary>
        public string nombre_pagina { get; set; }

        /// <summary>
        /// url de redireccionamiento
        /// </summary>
        private string url_redirect = ConfigurationManager.AppSettings["url_redirect"];

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// Encripta algun string
        /// </summary>
        /// <param name="pString"></param>
        /// <returns></returns>
        public string encript_string(string pString)
        {
            return encript_core.SetValue(pString);
        }

    }
}