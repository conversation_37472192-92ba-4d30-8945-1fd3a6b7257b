﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/MasterPrincipal.Master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="SIFAFEL_APP.Modulos.Caja.Default" %>

<%@ Register Src="~/WebUserControls/Caja/WUC_CajaCierre.ascx" TagPrefix="uc1" TagName="WUC_CajaCierre" %>
<%@ Register Src="~/WebUserControls/Caja/WUC_CajaApertura.ascx" TagPrefix="uc2" TagName="WUC_CajaApertura" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cph_head" runat="server">

    <style type="text/css">
        table.dataTable tbody tr,
        table.dataTable tbody td,
        table.dataTable thead th {
            /* height: 10px; */
            word-break: keep-all;
            white-space: nowrap;
            /* line-height: 0.8; */
            /* padding: 8px 18px; */
            /* text-align: center; */
        }

        table.dataTable tr.group-row td {
            text-align: center;
            font-weight: bold;
        }

        table.dataTable tr.dtrg-group.dtrg-end th {
            text-align: right;
            font-weight: normal;
        }

        .dt-group {
            font-weight: bold;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 2px 4px;
        }

        .text-center th {
            text-align: center;
        }

        .group-header,
        .group-footer {
            font-weight: bold;
            /* padding: 10px; */
        }
        .group-header td{
            background-color: #ddd !important;
        }
        .total-row {
            font-weight: bold;
            /* padding: 10px; */
            text-align: center;
        }

        .group-header {
            font-weight: bold;
            text-align: left;
        }

        .total-row {
            font-weight: bold;
        }

        #tbl_transacciones {
            width: 100%;
            border-collapse: collapse;
        }

        table.dataTable > tbody > tr.selected > *,
        table.dataTable > tbody > tr.selected * {
            box-shadow: inset 0 0 0 9999px rgb(247 139 71) !important;
            color: #ffffff !important;
        }

        .feather-sm {
            width: 16px;
            height: 16px;
        }
        
        .table-container {
            overflow-y: auto;
            min-height: auto;
            max-height: 500px;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cph_body" runat="server">

    <uc1:WUC_CajaCierre runat="server" ID="WUC_CajaCierre" />
    <uc2:WUC_CajaApertura runat="server" ID="WUC_CajaApertura" />

    <div class="page-header">
        <div class="add-item d-flex">
            <div class="page-title">
                <h4>Módulo de caja</h4>
                <h6>Administración de caja</h6>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text">
                            <i data-feather="airplay"></i>
                        </span>
                        <select id="ddlCaja" name="ddlCaja" class="form-select form-select-sm" <%= lstCajas?.Count > 1 ? "" : "disabled='disabled'" %>>
                            <% foreach (var caja in lstCajas)
                                { %>
                            <option value="<%= caja.id %>"><%= caja.nombre %></option>
                            <% } %>
                        </select>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group input-group-sm">
                        <span class="input-group-text">
                            <i class="fas fa-coins"></i>
                        </span>
                        <select id="ddlMoneda" name="ddlMoneda" class="form-select" <%= lstMonedas.Count > 1 ? "" : "disabled='disabled'" %>>
                            <% foreach (var caja in lstMonedas)
                                { %>
                            <option value="<%= caja.id %>"><%= caja.codigo_iso %></option>
                            <% } %>
                        </select>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-lg-3 mb-2">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i data-feather="calendar" class="feather-16"></i>
                        </span>
                        <input type="text" id="txtRangoFecha" class="form-control" />
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-1 mb-2 d-grid">
                    <%--<br />--%>
                    <button id="btnBuscarOperaciones" type="button" href="#" class="btn btn-success btn-sm p-1">
                        <i data-feather="search" class="me-2"></i>
                    </button>
                </div>


            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12 col-xl-12 d-flex" id="cont_operaciones" style="display: none !important;">
            <div class="card flex-fill w-100 mb-4">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table id="tbl_operaciones" class="display table table-hover table-sm" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th>Apertura</th>
                                    <th>Cierre</th>
                                    <th>Moneda</th>
                                    <th>Saldo inicial</th>
                                    <th>Saldo sistema</th>
                                    <th>Saldo cuadre</th>
                                    <th>Estado</th>
                                    <th>Comentarios</th>
                                    <th>Opciones</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 col-md-12 col-xl-5 d-flex" id="cont_transacciones" style="display: none !important;">
            <div class="card flex-fill w-100 mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Movimientos</h4>
                    <button class="btn btn-sm btn-sm"><i class="fas fa-file-pdf"></i></button>
                </div>
                <div class="card-body p-1">
                    <table id="tbl_transacciones" class="display dataTable table table-striped table-hover table-sm" style="width: 100%;">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Concepto</th>
                                <th colspan="2" class="text-end">Monto</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-sm-12 col-md-12 col-xl-7 d-flex" id="cont_medio_pago" style="display: none !important;">
            <div class="card flex-fill w-100 mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Medios de pago</h4>
                    <label><b id="lblTransaccion"></b></label>
                </div>
                <div class="card-body p-1">
                    <div class="table-responsive">
                        <table id="tbl_medios_pago" class="table table-striped table-bordered" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th>Medio de pago</th>
                                    <th>Monto</th>
                                    <th>Detalle</th>
                                    <th>Adjuntos</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">

        const baseUrl = "<%=ConfigurationManager.AppSettings["url_cdn"] %>";

        function accionOperacion(action, fechaCaja) {
            const fechaFormateada = fechaCaja.split(' ')[0]; // Tomar solo la parte de la fecha antes de la hora

            var fechaSeleccionada = fechaFormateada ? fechaFormateada : __GetDateToday();
            var caja = $("#ddlCaja").val();
            var moneda = $("#ddlMoneda").val();

            const data = {
                fecha: __Format(fechaSeleccionada),
                id_caja: caja ? parseInt(caja) : null,
                id_moneda: moneda ? parseInt(moneda) : null,
            };

            if (action === "aperturar") {
                __ModalCajaApertura(data);
            } else if (action === "cerrar") {
                __ModalCajaCierre(data);
            }
            $("#btnBuscarOperaciones").click();
        }

        function getIconAndClassStatus(status) {
            let iconName, colorClass;
            if (status === 'CUA') {
                iconName = 'check-circle';
                colorClass = 'text-success';
            } else if (status === 'SOB') {
                iconName = 'alert-triangle';
                colorClass = 'text-warning';
            } else {
                iconName = 'alert-circle';
                colorClass = 'text-danger';
            }
            return { iconName, colorClass };
        }

        $(document).ready(function () {

            var start = moment().subtract(30, 'days');
            var end = moment();

            $('#txtRangoFecha').daterangepicker({
                startDate: start,
                endDate: end,
                maxDate: moment(),
                locale: {
                    format: 'DD/MM/YYYY',
                    applyLabel: "Aplicar",
                    cancelLabel: "Cancelar",
                    fromLabel: "Desde",
                    toLabel: "Hasta",
                    customRangeLabel: "Personalizado"
                }
            });
            
            $('#btnBuscarOperaciones').on('click', function () {
                $("#cont_transacciones").get(0).style.setProperty("display", "none", "important");
                var caja = $('#ddlCaja').val();
                var moneda = $('#ddlMoneda').val();
                var rangoFecha = $('#txtRangoFecha').val();

                if (!caja) { Swal.fire('Campo requerido', 'Selecciona una caja.', 'warning'); return; }
                if (!moneda) { Swal.fire('Campo requerido', 'Selecciona una moneda.', 'warning'); return; }
                if (!rangoFecha) { Swal.fire({ icon: 'warning', title: 'Advertencia', text: 'Por favor, ingrese un rango de fechas.' }); return; }

                var fechas = rangoFecha.split(' - ');
                var fechaInicio = fechas[0];
                var fechaFin = fechas[1];

                __Progress(`Consultando...`);

                setTimeout(function () {
                    var jsonData = JSON.stringify({
                        id_caja: caja ? parseInt(caja) : null,
                        fecha_inicio: __Format(fechaInicio),
                        fecha_fin: __Format(fechaFin),
                        id_moneda: moneda ? parseInt(moneda) : null,
                    });

                    var fmOper = new FormData();
                    fmOper.append("mth", mtdEnc("get/cajas/operaciones"));
                    fmOper.append("data", mtdEnc(jsonData));

                    $.ajax({
                        url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmOper,
                        async: true,
                        success: function (response) {
                            if (response) {
                                const datos = response.data;

                                if ($.fn.DataTable.isDataTable('#tbl_operaciones')) {
                                    $('#tbl_operaciones').DataTable().destroy();
                                }

                                const tabla = $('#tbl_operaciones').DataTable({
                                    pageLength: 10,
                                    info: false,
                                    searching: false,
                                    ordering: false,
                                    dom: 'lrtip',
                                    lengthChange: false,
                                    language: {
                                        emptyTable: "No hay datos disponibles en la tabla",
                                        zeroRecords: "No se encontraron registros coincidentes",
                                        loadingRecords: "Cargando...",
                                        processing: "Procesando...",
                                        paginate: {
                                            next: ' <i class="fa fa-angle-right"></i>',
                                            previous: '<i class="fa fa-angle-left"></i>'
                                        }
                                    },
                                    data: datos,
                                    "columnDefs": [
                                        {
                                            "targets": [8],
                                            "className": "action-table-data"
                                        }
                                    ],
                                    columns: [
                                        {
                                            title: "Apertura",
                                            data: "fecha_apertura",
                                            render: function (data, type, row) {
                                                const img = row.img_usuario_apertura
                                                    ? `<img src="${baseUrl}${row.img_usuario_apertura}" class="img-circle" style="width: 20px; height: 20px; margin-right: 5px; border-radius: 30%;">`
                                                    : '';
                                                return `<div style="display: flex; align-items: center;">${img} ${data}&nbsp;</div>`;
                                            }
                                        },
                                        {
                                            title: "Cierre",
                                            data: "fecha_cierre",
                                            render: function (data, type, row) {
                                                if (!data) return '&nbsp;';
                                                const img = row.img_usuario_cierre
                                                    ? `<img src="${baseUrl}${row.img_usuario_cierre}" class="img-circle" style="width: 20px; height: 20px; margin-right: 5px; border-radius: 30%;">`
                                                    : '';
                                                return `<div style="display: flex; align-items: center;">${img} ${data}</div>`;
                                            }
                                        },
                                        { title: "Moneda", data: "moneda", className: "text-center" },
                                        {
                                            title: "Saldo Inicial",
                                            data: "saldo_inicial",
                                            className: "text-end",
                                            render: data => __formatMoney(data, 2, '.', ',')
                                        },
                                        {
                                            title: "Saldo Final",
                                            data: "saldo_final",
                                            className: "text-end",
                                            render: data => __formatMoney(data, 2, '.', ',')
                                        },
                                        {
                                            title: "Cuadre",
                                            data: null,
                                            className: "text-end",
                                            render: function (data, type, row) {
                                                if (["APE", "NEW"].includes(row.estado)) return '';
                                                const { iconName, colorClass } = getIconAndClassStatus(row.tipo_cuadre);
                                                return `${__formatMoney(row.saldo_cuadre, 2, '.', ',')}&nbsp;<i data-feather="${iconName}" class="${colorClass}"></i>`;
                                            }
                                        },
                                        {
                                            title: "Estado",
                                            data: "estado",
                                            className: "text-center",
                                            render: function (data) {
                                                if (data === 'NEW') return '';
                                                return `<i class="fas ${data === 'CIE' ? 'fa-lock' : 'fa-lock-open'}"></i>`;
                                            }
                                        },
                                        {
                                            title: "Observaciones",
                                            data: "observaciones",
                                            className: "text-center",
                                            render: function (data, type, row) {
                                                const img = row.img_usuario_cierre
                                                    ? `<img src="${baseUrl}${row.img_usuario_cierre}" class="img-circle" style="width: 20px; height: 20px; margin-right: 5px; border-radius: 30%;">`
                                                    : '';
                                                return `<div style="display: flex; align-items: center;">${data ? img : ''}${data || ''}&nbsp;</div>`;
                                            }
                                        },
                                        {
    title: "Acciones",
    data: null,
    orderable: false,
    className: "text-center",
    render: function (data, type, row) {
        let html = `<div class="edit-delete-action data-row">`;

        if ((!row.fecha_cierre || row.fecha_cierre === "") && row.estado !== "NEW") {
            html += `
                <a class="me-2 confirm-text p-2 mb-0 btn-sm btn-danger"
                    onclick="accionOperacion('cerrar', '${row.fecha_apertura}')">
                    <i data-feather="power" class="feather-sm"></i>
                </a>`;
        }

        if (row.estado === "NEW") {
            html += `
                <a class="me-2 p-2 mb-0"
                    onclick="accionOperacion('aperturar', '${row.fecha_apertura}')">
                    <i data-feather="plus" class="feather-sm"></i>
                </a>`;
        }

        html += `</div>`;
        return html;
    }
}


                                    ],
                                    createdRow: function (row, data) {
                                        $(row).data('operacion', data);
                                        $(row).find('td').eq(7).addClass('action-table-data');
                                    },
                                    drawCallback: function () {
                                        feather.replace(); // Asegura que los íconos se rendericen después del draw
                                    }
                                });

                                $("#cont_operaciones").show();

                                $('#tbl_operaciones tbody').off('click', 'tr').on('click', 'tr', function () {
                                    $('#tbl_operaciones tbody tr').removeClass('selected');
                                    $(this).addClass('selected');

                                    const operacion = $(this).data('operacion');
                                    if (operacion) {
                                        if (operacion.estado !== "NEW") {
                                            TransaccionesOperacion(operacion);
                                        } else {
                                            $("#cont_transacciones").css("display", "none !important");
                                        }
                                        $("#cont_medio_pago").css("display", "none !important");
                                    }
                                });
                            }

                        },
                        complete: function () {
                            __ProgressOff();
                        }
                    });
                }, 50);
            });

            function TransaccionesOperacion(operacion) {
                $("#cont_transacciones").show();

                __Progress(`Cargando transacciones...`);

                fechaCaja = operacion.fecha_apertura ? operacion.fecha_apertura.split(' ')[0] : __GetDateToday();
                setTimeout(function () {
                    var jsonData = JSON.stringify({
                        id_caja: operacion.id_caja ? parseInt(operacion.id_caja) : null,
                        fecha: operacion.fecha_apertura ? operacion.fecha_apertura.split(' ')[0] : null,
                        id_moneda: operacion.id_moneda ? parseInt(operacion.id_moneda) : null,
                        id_operacion: operacion.id_operacion ? parseInt(operacion.id_operacion) : null
                    });

                    var fmTran = new FormData();
                    fmTran.append("mth", mtdEnc("get/cajas/transacciones"));
                    fmTran.append("data", mtdEnc(jsonData));

                    $.ajax({
                        url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                        type: "post",
                        contentType: false,
                        processData: false,
                        ContentType: "text/html;charset=utf-8",
                        dataType: "json",
                        data: fmTran,
                        async: true,
                        success: function (response) {
                            if (!response || !response.data) return;

                            const datos = response.data;
                            $('#tbl_transacciones tbody').empty();

                            const grupos = {};
                            let totalGeneral = 0,
                                monedaTransaccion = '';

                            // Agrupar por tipo_movimiento y acumular montos
                            datos.forEach(item => {
                                const tipo = item.tipo_movimiento || "Sin tipo";
                                const orden = item.orden_tipo ?? 99;
                                if (!grupos[tipo]) {
                                    grupos[tipo] = {
                                        orden: orden,
                                        moneda: item.moneda,
                                        items: [],
                                        subtotal: 0
                                    };
                                }
                                grupos[tipo].items.push(item);
                                grupos[tipo].subtotal += parseFloat(item.monto || 0);
                            });

                            // Ordenar grupos según orden_tipo
                            const tiposOrdenados = Object.entries(grupos).sort((a, b) => a[1].orden - b[1].orden);

                            // Procesar y renderizar grupos dinámicamente
                            tiposOrdenados.forEach(([tipo, grupo]) => {
                                const { items, subtotal, moneda } = grupo;

                                // Encabezado
                                $('#tbl_transacciones tbody').append(`<tr class="group-header">
                                                                           <td colspan="4">${tipo.toUpperCase()}</td>
                                                                      </tr>`);

                                // Filas
                                items.forEach(item => {
                                    const img = item.img_usuario
                                        ? `<img src="${baseUrl}${item.img_usuario}" class="img-circle" style="width: 20px; height: 20px; margin-right: 5px; border-radius: 30%;">`
                                        : '';
                                    const fila = $(`<tr>
                                                        <td><div style="display: flex; align-items: center;">${img} ${item.fecha_hora}</div></td>
                                                        <td><i class="${item.icono}"></i>&nbsp;${item.concepto}</td>
                                                        <td>${item.moneda}</td>
                                                        <td class="text-end">${__formatMoney(item.monto, 2, '.', ',')}</td>
                                                    </tr>`).data('transaccion', item);
                                    $('#tbl_transacciones tbody').append(fila);
                                });

                                // Subtotal
                                $('#tbl_transacciones tbody').append(`<tr class="group-footer">
                                                                          <td colspan="2" style="text-align: right;">Subtotal</td>
                                                                          <td>${moneda}</td>
                                                                          <td class="text-end">${__formatMoney(subtotal, 2, '.', ',')}</td>
                                                                      </tr>`);

                                // Total general con lógica según orden_tipo
                                if (grupo.orden % 2 === 0) {
                                    totalGeneral += subtotal;
                                } else {
                                    totalGeneral -= subtotal;
                                }
                                monedaTransaccion = moneda;
                            });

                            // Total General
                            $('#tbl_transacciones tbody').append(`<tr class="total-row">
                                                                      <td colspan="2" style="text-align: right;">Total General:</td>
                                                                      <td>${monedaTransaccion}</td>
                                                                      <td class="text-end">${__formatMoney(totalGeneral, 2, '.', ',')}</td>
                                                                  </tr>`);

                            // Scroll dinámico
                            const tbody = $('#tbl_transacciones tbody');
                            if ($('#tbl_transacciones').parent().hasClass('table-container')) {
                                $('#tbl_transacciones').unwrap();
                            }
                            if (tbody.children('tr').length > 10) {
                                $('#tbl_transacciones').wrap('<div class="table-container"></div>');
                            }

                            // Evento por fila
                            tbody.off('click', 'tr').on('click', 'tr', function () {
                                const transaccion = $(this).data('transaccion');
                                if (transaccion) {
                                    tbody.children('tr').removeClass('selected');
                                    $(this).addClass('selected');
                                    MediosPagoTransaccion(transaccion);
                                }
                            });
                        },
                        complete: function () {
                            __ProgressOff();
                            scrollToElement("#tbl_transacciones");
                        }
                    });

                }, 50);
            }
        });

        function MediosPagoTransaccion(transaccion) {
            $('#lblTransaccion').text(transaccion.concepto);

            var jsonData = JSON.stringify({
                id_caja: $('#ddlCaja').val() ? parseInt($('#ddlCaja').val()) : null,
                id_moneda: $('#ddlMoneda').val() ? parseInt($('#ddlMoneda').val()) : null,
                cod_entidad: transaccion.cod_entidad ? transaccion.cod_entidad : null,
                num_referencia: transaccion.num_referencia ? transaccion.num_referencia : null
            });

            var fmTran = new FormData();
            fmTran.append("mth", mtdEnc("get/medio/pagoTransaccion"));
            fmTran.append("data", mtdEnc(jsonData));

            $.ajax({
                url: "<%=ConfigurationManager.AppSettings["url_redirect"] %>Controllers/caja.ashx",
                type: "post",
                contentType: false,
                processData: false,
                dataType: "json",
                data: fmTran,
                success: function (response) {

                    if (response) {
                        let mediosPago = response.data;
                        if (response.data.length > 0) {
                            $("#cont_medio_pago").get(0).style.setProperty("display", "block", "important");

                            if ($.fn.DataTable.isDataTable('#tbl_medios_pago')) {
                                $('#tbl_medios_pago').DataTable().destroy();
                            }

                            let tabla = $('#tbl_medios_pago').DataTable({
                                pageLength: 10,
                                info: false,
                                searching: false,
                                paging: false,
                                language: {
                                    emptyTable: "No hay datos disponibles en la tabla",
                                    zeroRecords: "No se encontraron registros coincidentes",
                                    loadingRecords: "Cargando...",
                                    processing: "Procesando...",
                                }
                            });

                            tabla.clear();
                            mediosPago.forEach(item => {
                                let monto = parseFloat(item.monto).toFixed(2);
                                let fila = tabla.row.add([
                                    item.medio_pago,
                                    `${item.moneda} ${monto}`,
                                    item.concepto,
                                    `<button class="btn btn-link" style="padding:0;" onclick="editarMedio(${item.id_medio_pago})"><i class="fa fa-eye"></i></button>`
                                ]).draw().node();

                                $(fila).data('medio_pago', item);

                                // Remueve la clase 'selected' de todas las filas antes de agregar la nueva selección
                                //$('#tbl_transacciones tbody tr').removeClass('selected');

                                // Agrega la clase 'selected' a la fila recién añadida
                                //$(fila).addClass('selected');
                            });
                        } else {
                            var contMedioPago = $("#cont_medio_pago").get(0);
                            if (contMedioPago.style.display == "none") {
                            } else {
                                $("#cont_medio_pago").fadeOut(400, function () {
                                    $(this).get(0).style.setProperty("display", "none", "important");
                                });
                            }
                        }

                    }
                },
                complete: function () {
                    __ProgressOff();
                }
            });
        }

    </script>
</asp:Content>
