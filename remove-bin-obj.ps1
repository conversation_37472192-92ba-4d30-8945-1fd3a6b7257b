# Script para eliminar archivos bin y obj del índice de Git
Write-Host "Eliminando archivos bin y obj del índice de Git..." -ForegroundColor Cyan

# Eliminar archivos bin
Write-Host "Eliminando archivos bin..." -ForegroundColor Yellow
git rm -r --cached "*/bin/*" 2>$null
git rm -r --cached "*/*/bin/*" 2>$null
git rm -r --cached "*/*/*/bin/*" 2>$null

# Eliminar archivos obj
Write-Host "Eliminando archivos obj..." -ForegroundColor Yellow
git rm -r --cached "*/obj/*" 2>$null
git rm -r --cached "*/*/obj/*" 2>$null
git rm -r --cached "*/*/*/obj/*" 2>$null

Write-Host "Proceso completado." -ForegroundColor Green
Write-Host "Ahora puedes hacer un commit para confirmar los cambios:" -ForegroundColor Cyan
Write-Host "git add .gitignore" -ForegroundColor White
Write-Host "git commit -m 'Excluir carpetas bin y obj de Git'" -ForegroundColor White
