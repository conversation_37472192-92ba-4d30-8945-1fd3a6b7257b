﻿<%--/* 

@model SIFAFEL_MODEL.DataTransferObject.ModProductoDTO

@using (Html.BeginForm(null, null, FormMethod.Post, new { id = "frm-save-product" }))
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="row">
            <div class="col-md-12">
                @* INFORMACION DEL PRODUCTO *@

                <div class="iq-card">
                    <div class="iq-card-header d-flex justify-content-between">
                        <div class="iq-header-title">
                            <h4 class="card-title">Nuevo producto</h4>
                        </div>
                    </div>
                    <div class="iq-card-body">
                        <div class="row">

                            <div class="col-md-2">

                                <div class="container-image">
                                    <div class="hover">
                                        <p>Carga imagen</p>
                                    </div>
                                    @if (string.IsNullOrWhiteSpace(Model.img_producto))
                                    {
                                        <img id="img_producto" src="data:image/png;base64,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" />
                                    }
                                    else
                                    {
                                        <img id="img_producto" name="img_producto" src="@Model.img_producto" />
                                    }

                                    <input id="file_product" name="file_product" type="file" style="display:none;" accept="image/*" />
                                </div>

                                @*<h4 id="lbl_barcode" class="barcode">&nbsp;</h4>*@
                            </div>

                            <div class="col-md-10">

                                <div class="form-horizontal">

                                    @* ID PRODUCTO *@
                                    <div class="form-group hide-control row">
                                        @Html.LabelFor(model => model.id_producto, htmlAttributes: new { @class = "control-label col-md-12" })
                                        <div class="col-md-12">
                                            @Html.EditorFor(model => model.id_producto, new { htmlAttributes = new { @class = "form-control form-control-sm" } })
                                            @Html.ValidationMessageFor(model => model.id_producto, "", new { @class = "text-danger" })
                                        </div>
                                    </div>

                                    <div class="row">

                                        @* CODIGO *@
                                        <div class="col-md-6">

                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-barcode"></i>&nbsp;

                                                    @Html.LabelFor(model => model.codigo)
                                                </div>
                                                <div class="col-sm-8">
                                                    <div class="input-group input-group-sm">

                                                        @Html.EditorFor(model => model.codigo, new { htmlAttributes = new { @class = "form-control input-xs", @placeholder = "Código de barras", @search = "0" } })
                                                        @Html.ValidationMessageFor(model => model.codigo, "", new { @class = "text-danger" })

                                                        <div class="input-group-append">
                                                            <button id="btnBusProCod" class="btn btn-success" type="button" tabindex="-1">
                                                                <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-search"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        @* MARCA *@
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-car"></i>&nbsp;
                                                    @Html.LabelFor(model => model.marca)
                                                </div>
                                                <div class="col-sm-8">
                                                    @Html.DropDownListFor(model => model.id_marca, new SelectList(ViewBag.marca, "id_marca", "descripcion"), "Seleccionar marca", htmlAttributes: new { @class = "form-control form-select" })
                                                    @Html.ValidationMessageFor(model => model.id_marca, "", new { @class = "text-danger" })
                                                </div>
                                            </div>
                                        </div>

                                        @* PROVEEDOR *@
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-user-tie"></i>&nbsp;
                                                    @Html.LabelFor(model => model.id_proveedor)
                                                </div>
                                                <div class="col-sm-8">
                                                    @Html.DropDownListFor(model => model.id_proveedor, new SelectList(ViewBag.Proveedores, "id_proveedor", "nombres"), "Seleccionar proveedor", htmlAttributes: new { @class = "form-control form-select" })
                                                    @Html.ValidationMessageFor(model => model.id_proveedor, "", new { @class = "text-danger" })
                                                </div>
                                            </div>
                                        </div>

                                        @* MODELO *@
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-car"></i>&nbsp;
                                                    @Html.LabelFor(model => model.modelo)
                                                </div>
                                                <div class="col-sm-8">
                                                    @Html.DropDownListFor(model => model.id_modelo, Enumerable.Empty<SelectListItem>(), "Seleccionar modelo", htmlAttributes: new { @class = "form-control form-select" })
                                                    @Html.ValidationMessageFor(model => model.id_modelo, "", new { @class = "text-danger" })
                                                </div>
                                            </div>
                                        </div>

                                        @* CATEGORIA *@
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-box"></i>&nbsp;
                                                    @Html.LabelFor(model => model.id_categoria)
                                                </div>
                                                <div class="col-sm-8">
                                                    @Html.DropDownListFor(model => model.id_categoria, new SelectList(ViewBag.Categoria, "id_categoria", "descripcion"), "Seleccionar categoria", htmlAttributes: new { @class = "form-control form-select" })
                                                    @Html.ValidationMessageFor(model => model.id_categoria, "", new { @class = "text-danger" })
                                                </div>
                                            </div>
                                        </div>

                                        @* AÑO *@
                                        <div class="col-md-6">
                                            <div class="form-group row">
                                                <div class="col-sm-4">
                                                    <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-calendar"></i>&nbsp;
                                                    @Html.LabelFor(model => model.anio)
                                                </div>
                                                <div class="col-sm-8">

                                                    @Html.DropDownListFor(model => model.anio, Enumerable.Empty<SelectListItem>(), "Seleccionar año", htmlAttributes: new { @class = "form-control form-control-sm form-select" })

                                                    @Html.ValidationMessageFor(model => model.anio, "", new { @class = "text-danger" })
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <div class="row"></div>

                            <div class="col-md-12">
                                @* NOMBRE *@

                                <div class="form-group row">
                                    <div class="col-sm-2">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-box"></i>&nbsp;
                                        @Html.LabelFor(model => model.nombre)
                                    </div>
                                    <div class="col-sm-10">

                                        @Html.EditorFor(model => model.nombre, new { htmlAttributes = new { @class = "form-control form-control-sm", @placeholder = "Producto", required = "required" } })
                                        @Html.ValidationMessageFor(model => model.nombre, "", new { @class = "text-danger" })

                                    </div>
                                </div>

                                <div class="form-group row">
                                    <div class="col-sm-2">
                                        <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-boxes"></i>&nbsp;
                                        @Html.LabelFor(model => model.descripcion)
                                    </div>
                                    <div class="col-sm-10">

                                        @Html.TextAreaFor(model => model.descripcion, new { @class = "form-control", @placeholder = "Descripción", @rows = "2", @style = "resize: none;", required = "required" })
                                        @Html.ValidationMessageFor(model => model.descripcion, "", new { @class = "text-danger" })

                                    </div>
                                </div>
                                <br />
                            </div>

                        </div>
                    </div>
                </div>


                <div class="row"></div>
                <div class="row">

                    <div class="col-md-6">

                        <div class="iq-card">
                            <div class="iq-card-header d-flex justify-content-between">
                                <div class="iq-header-title">
                                    <h4 class="card-title"><i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-bags-shopping"></i>&nbsp;Detalle de compra</h4>
                                </div>
                            </div>
                            <div class="iq-card-body">

                                @* referencia_compra *@
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-file-invoice"></i>&nbsp;
                                            @Html.LabelFor(model => model.referencia_compra, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.referencia_compra, new { htmlAttributes = new { @class = "form-control form-control-sm", @placeholder = "Cod. Referencia" } })
                                        </div>
                                    </div>
                                </div>

                                @* fecha_compra *@
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-calendar"></i>&nbsp;
                                            @Html.LabelFor(model => model.fecha_compra, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.fecha_compra, new { htmlAttributes = new { @class = "form-control form-control-sm", @type = "date" } })
                                        </div>
                                    </div>
                                </div>

                                @* fecha_vencimiento *@
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-calendar"></i>&nbsp;
                                            @Html.LabelFor(model => model.fecha_vencimiento, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.TextBoxFor(model => model.fecha_vencimiento, "{0:yyyy-MM-dd}", new { @class = "form-control form-control-sm", @type = "date" })
                                            @*@Html.EditorFor(model => model.fecha_vencimiento, new { htmlAttributes = new { @class = "form-control form-control-sm", @type = "date" } })*@
                                        </div>
                                    </div>
                                </div>
                                <br />

                                @* costo_unitario *@
                                <div class="form-horizontal">
                                    @* moneda *@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill"></i>&nbsp;
                                            @Html.LabelFor(model => model.costo_unitario, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">

                                                @Html.DropDownListFor(model => model.id_moneda, new SelectList(ViewBag.MonedaContribuyente, "id_moneda", "codigo_iso"), htmlAttributes: new { @class = "form-control currency", @tabindex = "-1" })
                                                @Html.EditorFor(model => model.costo_unitario, new { htmlAttributes = new { @class = "form-control price", @type = "number", required = "required" } })
                                            </div>
                                        </div>
                                    </div>

                                    @* precio *@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill"></i>&nbsp;
                                            @Html.LabelFor(model => model.precio_unitario, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                @Html.DropDownListFor(model => model.id_moneda_venta, new SelectList(ViewBag.MonedaContribuyente, "id_moneda", "codigo_iso"), htmlAttributes: new { @class = "form-control currency id_moneda_venta", @disabled = "disabled" })
                                                @Html.EditorFor(model => model.precio_unitario, new { htmlAttributes = new { @class = "form-control price", @placeholder = "Precio venta", @type = "number", required = "required" } })
                                            </div>
                                        </div>
                                    </div>

                                    @* precio min*@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-money-bill"></i>&nbsp;
                                            @Html.LabelFor(model => model.min_descuento, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                @Html.DropDownListFor(model => model.id_moneda_venta, new SelectList(ViewBag.MonedaContribuyente, "id_moneda", "codigo_iso"), htmlAttributes: new { @class = "form-control currency id_moneda_venta", @disabled = "disabled" })
                                                @Html.EditorFor(model => model.min_descuento, new { htmlAttributes = new { @class = "form-control price", @placeholder = "Precio venta (min)", @type = "number" } })
                                            </div>
                                        </div>
                                    </div>

                                    @* ganancia_producto *@
                                    <div class="form-group row">
                                        <div id="label-ganancia" class="col-sm-6">
                                            <i id="icon_ganancia" class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-coins"></i>&nbsp;
                                            @Html.LabelFor(model => model.ganancia_producto)
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="input-group input-group-sm group-3070">
                                                @Html.DropDownListFor(model => model.id_moneda_venta, new SelectList(ViewBag.MonedaContribuyente, "id_moneda", "codigo_iso"), htmlAttributes: new { @class = "form-control currency id_moneda_venta", @disabled = "disabled" })
                                                @Html.EditorFor(model => model.ganancia_producto, new { htmlAttributes = new { @class = "form-control price", @placeholder = "Gancia por producto", @type = "number", @disabled = "disabled" } })
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="iq-card">
                            <div class="iq-card-header d-flex justify-content-between">
                                <div class="iq-header-title">
                                    <h4 class="card-title"><i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-bags-shopping"></i>&nbsp;Stock inventario</h4>
                                </div>
                            </div>
                            <div class="iq-card-body">

                                @* stock *@
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-box-check"></i>&nbsp;
                                            @Html.LabelFor(model => model.stock, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.stock, new { htmlAttributes = new { @class = "form-control form-control-sm stock", @type = "number", @step = "any", required = "required" } })
                                        </div>
                                    </div>
                                </div>

                                @* stock_actual *@
                                <div class="form-horizontal">
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-boxes"></i>&nbsp;
                                            @Html.LabelFor(model => model.stock_actual, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.stock_actual, new { htmlAttributes = new { @class = "form-control form-control-sm stock", @type = "number", @disabled = "disabled", @step = "any" } })
                                        </div>
                                    </div>

                                    @* stock_total *@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-boxes"></i>&nbsp;
                                            @Html.LabelFor(model => model.stock_total, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.stock_total, new { htmlAttributes = new { @class = "form-control form-control-sm stock", @type = "number", @disabled = "disabled", @step = "any" } })
                                        </div>
                                    </div>

                                    <br />

                                    @* stock_maximo *@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-bell-on txt-green"></i>&nbsp;
                                            @Html.LabelFor(model => model.stock_maximo, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.stock_maximo, new { htmlAttributes = new { @class = "form-control form-control-sm stock", @placeholder = "Stock máximo", @type = "number", @step = "any" } })
                                        </div>
                                    </div>

                                    @* stock_minimo *@
                                    <div class="form-group row">
                                        <div class="col-sm-6">
                                            <i class="@System.Configuration.ConfigurationManager.AppSettings["html_font_type"] fa-bell-on txt-green"></i>&nbsp;
                                            @Html.LabelFor(model => model.stock_minimo, new { @class = "control-label" })
                                        </div>
                                        <div class="col-sm-6">
                                            @Html.EditorFor(model => model.stock_minimo, new { htmlAttributes = new { @class = "form-control form-control-sm stock", @placeholder = "Stock mínimo", @type = "number", @step = "any" } })
                                        </div>
                                    </div>

                                    <br />
                                    <p></p>
                                    <br />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col text-center">
                <button id="btnGuardaProducto" type="button" class="btn btn-lg btn-success"><i class="far fa-save"></i>&nbsp;Guardar</button>&nbsp;
                <button id="btnCancelaProducto" type="button" class="btn btn-lg btn-danger" onclick="window.location.href = '/Inventario'"><i class="far fa-times"></i>&nbsp;Cancelar</button>
            </div>
        </div>
        <br />
    </div>
}

@*<script src="https://adminlte.io/themes/AdminLTE/bower_components/ckeditor/ckeditor.js"></script>
    <script src="https://adminlte.io/themes/AdminLTE/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js"></script>
    <script>
        $(function () {
            // Replace the <textarea id="editor1"> with a CKEditor
            // instance, using default configuration.
            CKEDITOR.replace('descripcion')
            //bootstrap WYSIHTML5 - text editor
            $('.textarea').wysihtml5()
        })
    </script>*@

<script type="text/javascript" src="@System.Configuration.ConfigurationManager.AppSettings["url_cdn"]vendor/jquery-validate/jquery.validate.js"></script>
<script type="text/javascript">

    function _findProduct(code) {
        var _data = {
            codigo: code
        }
        $.ajax({
            url: "@System.Configuration.ConfigurationManager.AppSettings["url_redirect"]Producto/GetProductByCode",
            data: _data,
            type: "POST",
            contentType: 'application/x-www-form-urlencoded; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                Holdon_Open("Procesando...");
            },
            success: function (response) {
                if (response.data) {
                    if (response.data.length > 0) {
                        let data_value = response.data[0];
                        setDataProduct(data_value);
                    }
                    else {
                        $("#codigo").attr("search", "0").attr("data-valor", "");
                        $("#id_producto").val("0");

                        //Limpiar los campos
                        //$("input[type=number], input[type=email], textarea, input[type=date]").val("");
                        limpiarCampos();
                    }
                } else {
                    $("#codigo").attr("search", "0").attr("data-valor", "");
                    $("#id_producto").val("0");
                    limpiarCampos();
                }
            },
            error: function (response) {
                $("#codigo").attr("search", "0");
            },
            complete: function (response) {
                Holdon_Close();
            }
        });
    }

    $(document).ready(function () {

        if (@Model.id_producto === 0)
        {
            limpiarCampos();
        }

        $(".form-control.stock, .form-control.price").each(function () {
            $(this).attr("min", $(this).val() || "0");
            $(this).attr("value", $(this).val() || "0");
        });

        $(".form-control.stock").each(function () {

            $(this).on("keydown", function (event) {
                if (event.key === '.') { event.preventDefault(); }
            });
            $(this).on("input", function (event) {
                event.target.value = event.target.value.replace(/[^0-9]*/g, '');
            });

            $(this).on("blur", function () {
                if ($(this).val().trim() === "") {
                    $(this).val($(this).val() || "0");
                }
            });
        });

        /*begin: upload-file*/
        $("#file_product").change(function () {
            var input = this;
            var url = $(this).val();
            var ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();

            /*$('#img_product_value').attr('src', _url_cdn + 'SIFAFEL/img/modules/inventory/load-gif.gif');*/

            if (input.files && input.files[0] && (ext == "gif" || ext == "png" || ext == "jpeg" || ext == "jpg")) {
                var reader = new FileReader();

                reader.onload = function (e) {
                    $('#img_producto').attr('src', e.target.result);

                    //$("#img_producto").val(e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
            else {
                //$('#img_product_value').attr('src', _url_cdn + 'SIFAFEL/img/modules/inventory/load-gif.gif');
            }
        });


        $(".container-image .hover").click(function () {
            $("#file_product").click();
        });

        setTimeout(function () {
            $("#codigo").focus();
        }, 1000);

        $("#codigo").on("keyup", function () {
            $("#lbl_barcode").html($(this).val());
        });

        $("#costo_unitario").on("keyup, change", function () {
            $("#precio_unitario").val($(this).val());
            $("#precio_unitario").attr("min", $(this).val());
        });

        $("#costo_unitario, #precio_unitario").on("keyup, change", function () {
            let precio = $("#precio_unitario").val() || 0;
            let costo_unitario = $("#costo_unitario").val() || 0;
            let ganancia = (precio - costo_unitario).toFixed(2);
            let precio_max = Math.round(parseFloat(precio));
            console.log(precio, costo_unitario);
            $("#icon_ganancia").attr("class", "");
            $("#label-ganancia").attr("class", "");

            if (ganancia >= 0) {
                $("#icon_ganancia").addClass("far fa-angle-up");
                $("#label-ganancia").addClass("text-success col-sm-6");
            } else {
                Swal.fire({
                    icon: 'warning',
                    text: 'El Precio no debe ser menor al costo del producto.',
                });
                precio = costo_unitario;
                precio_max = Math.round(parseFloat(precio));
                ganancia = (precio - costo_unitario).toFixed(2);
                $("#precio_unitario").val(precio);

                $("#icon_ganancia").addClass("far fa-angle-down");
                $("#label-ganancia").addClass("text-red col-sm-6");
            }

            $("#ganancia_producto").val(ganancia);

            $("#min_descuento").val(precio);
            $("#min_descuento").attr("max", precio_max);
            $("#min_descuento").attr("min", costo_unitario);
        });

        $("#stock").on("keyup, change", function () {
            let stock = parseInt($(this).val() || 0);
            let stock_actual = parseInt($("#stock_actual").val() || 0);
            let stock_total = stock + stock_actual;

            $("#stock_total").val(stock_total);
        });

        $("#id_moneda").change(function () {
            $(".id_moneda_venta").val($(this).val());
        });

        //$("#codigo").on("blur", function () {
        //    let val_pre = mtdValue($(this).data("valor") || "");
        //    if ($(this).val() == "") {
        //        $(this).data("valor", "");
        //        $("#id_producto").val("0");
        //    }
        //    else if ($(this).val() != val_pre) {
        //        _findProduct($("#codigo").val());
        //    }
        //});

        $("#codigo").on('keypress', function (e) {
            if (e.which == 13) {
                if ($(this).val().trim() != "") {
                    _findProduct($("#codigo").val());
                }
            }
        });

        $("#btnBusProCod").on("click", function () {
            _findProduct($("#codigo").val());
        });

        $("#id_marca").change(function () {
            //$("#id_modelo").val("").empty();
            if ($(this).val() != "") {
                $("#id_modelo").FillSelect({
                    value: "id_modelo",
                    text: "descripcion",
                    ajax: {
                        url: _url_redirect + "Modulo/Inventario/GetModelos?id_marca=" + mtdEnc($(this).val()),
                        complete: function () {
                            let data_value = $("#id_modelo").data("value");
                            if (data_value != "") {
                                $("#id_modelo").val(mtdValue(data_value)).change();
                                $("#id_modelo").data("value", "");
                            }
                        }
                    },
                    firstOption: {
                        value: "",
                        text: "Seleccionar modelo",
                        visible: true
                    }
                });
            }
        });

        setTodayDateFormat("#fecha_compra");

        //
        $(".form-select").select2({
            //placeholder: "Seleccionar",
            //allowClear: true
        });


        $("#btnGuardaProducto").on("click", function () {
            //validar formulario
            if ($("#frm-save-product").valid({ lang: 'es' })) {
                var $inputs = $("#frm-save-product").find(':input');
                //var _data = {};
                var _data = new FormData();
                $inputs.each(function (index) {
                    let data_value = $(this).val();
                    if ($(this).attr('type') == "file") {
                        var file = $(this)[0];
                        if (file.files && file.files[0]) {
                            data_value = file.files[0];
                        }
                    }
                    _data.append($(this).attr('name'), data_value);
                });

                //obtenemos la imagen y la seteamos
                var srcImagen = $("#img_producto").attr("src");
                _data.append($("#img_producto").attr('name'), srcImagen);

                $.ajax({
                    url: "@System.Configuration.ConfigurationManager.AppSettings["url_redirect"]Modulo/Producto/CrearProducto",
                    data: _data,
                    type: "POST",
                    //contentType: 'application/x-www-form-urlencoded; charset=utf-8',
                    contentType: false,
                    processData: false,
                    dataType: "json",
                    beforeSend: function () {
                        Holdon_Open("Procesando...");
                    },
                    success: function (result) {
                        if (result.type == "success") {
                            Swal.fire({
                                icon: result.type,
                                text: result.text,
                            });
                        }
                        else {
                            Swal.fire({
                                icon: result.type,
                                text: result.text,
                            });
                        }
                        limpiarCampos();
                    },
                    error: function (result) {
                        console.log('error:', result);
                        Swal.fire({
                            icon: 'error',
                            text: 'Ocurrió un error durante el proceso',
                        });
                    },
                    complete: function () {
                        Holdon_Close();
                    }
                });
            }
            return false;
        });
    });

    //se agregar los datos de los productos
    function setDataProduct(data_value) {
        $("#id_producto").val(data_value.id_producto);
        $("#codigo").attr("search", "1").attr("data-valor", mtdEnc($("#codigo").val()));
        $("#nombre").val(data_value.nombre);
        $("#descripcion").val(data_value.descripcion);
        $("#img_producto").attr("src", data_value.img_producto || config_sifafel.img_product);

        $("#id_proveedor").val(data_value.id_proveedor || "").change();
        $("#id_categoria").val(data_value.id_categoria || "").change();
        $("#id_marca").val(data_value.id_marca || "").change();
        $("#id_modelo").val("").change();
        $("#id_modelo").data("value", mtdEnc(data_value.id_modelo || ""));
        $("#anio").val(data_value.anio || "").change();

        $("#fecha_vencimiento").val(DateFormatYYYmmdd(data_value.fecha_vencimientoFD));
        $("#costo_unitario").val(data_value.costo_unitario);
        $("#precio_unitario").val(data_value.precio_unitario);
        $("#min_descuento").val(data_value.min_descuento);
        $("#ganancia_producto").val((data_value.precio_unitario - data_value.costo_unitario).toFixed(2));

        //var stock = parseInt($("#stock").val()) + 1;
        //$("#stock").val(stock);
        //$("#stock_total").val(data_value.stock_actual + stock);
        $("#stock_actual").val(data_value.stock_actual);
        $("#stock_maximo").val(data_value.stock_maximo);
        $("#stock_minimo").val(data_value.stock_minimo);
    }

    function limpiarCampos() {
        $("#id_producto").val("");
        $("#nombre").val("");
        $("#descripcion").val("");
        $("#img_producto").attr("src", config_sifafel.img_product);

        $("#id_proveedor").val("").change();
        $("#id_categoria").val("").change();
        $("#id_marca").val("").change();
        $("#id_modelo").val("").change();
        $("#anio").val("").change();

        $("#fecha_vencimiento").val("");
        $("#costo_unitario").val(0);
        $("#precio_unitario").val(0);
        $("#min_descuento").val(0);
        $("#ganancia_producto").val(0);

        $("#stock").val(0);
        $("#stock_total").val(0);
        $("#stock_actual").val(0);
        $("#stock_maximo").val(0);
        $("#stock_minimo").val(0);
    }

</script>--%>
